import { AppBigCaption, AppBigText, AppMediumText, Card } from '@/components/_common/ui'
import WorkIcon from '@/assets/icons/work.svg'
import { AvatarWithInfo } from '@/components/_common/CardInfo/AvatarWithInfo'

import { SpecializationList } from '@/components/home/<USER>/OurPsychologist/SpecializationList'
import { PsychologistProps } from '@/components/home/<USER>/OurPsychologist/PsychologistCardItem'
import { ScheduleList } from '@/components/home/<USER>/OurPsychologist/ScheduleList'
import { Separator } from '@/components/ui/separator'
import Link from 'next/link'

export const MobileCardItem = (psychologist: PsychologistProps) => {
  const specialization = psychologist?.problemCategory?.length
    ? psychologist?.problemCategory.map((item) => item.problemCategory)
    : []
  return (
    <Card className="p-3 xs:p-3 sm:p-3 md:p-3 bg-white flex flex-col justify-center gap-y-2 w-full z-1">
      <Link href={`/detail-psychologist/${psychologist?.id}`} className="flex items-center gap-x-4">
        <AvatarWithInfo
          className="w-[72px] h-[72px]"
          image={psychologist?.profilePhoto ?? ''}
          heading={<AppBigText bold>{psychologist?.fullName}</AppBigText>}
          subHeading={
            <>
              <AppBigCaption className="text-gray-200 grid gap-y-2">
                <SpecializationList
                  className="text-caption-md font-medium text-gray-300"
                  list={specialization}
                  showItem={2}
                />
                <span className="flex items-center gap-x-1 text-gray-400 text-body-sm font-bold">
                  <WorkIcon />
                  {Number(psychologist?.calculatedExperience) < 1
                    ? 'Kurang dari 1'
                    : psychologist?.calculatedExperience || '0'}{' '}
                  tahun
                </span>
              </AppBigCaption>
            </>
          }
        />
      </Link>

      {/* Show schedule if available */}
      {psychologist.breakdownAvailability && psychologist.breakdownAvailability.length > 0 && (
        <>
          <div className="-mx-3">
            <Separator orientation="horizontal" />
          </div>
          <div className="-mx-3 px-3">
            <ScheduleList
              breakdownAvailability={psychologist.breakdownAvailability}
              maxItems={20}
              psychologistId={psychologist.id}
            />
          </div>
        </>
      )}
    </Card>
  )
}
