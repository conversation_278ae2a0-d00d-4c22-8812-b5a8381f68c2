import { createSlice, PayloadAction } from '@reduxjs/toolkit'

export type MetaItemProps = {
  additionalMeta: any
  meta: any
}
export type MetaProps = {
  isFetched: boolean
  clientReport: {
    meta: null | MetaItemProps
    pageFilter: any
    dateFilter: any
  }
  testimony: {
    meta: null | MetaItemProps
    pageFilter: any
    dateFilter: any
  }
  counseling: {
    meta: null | MetaItemProps
    pageFilter: any
    dateFilter: any
  }
}
const adminMetaSlice = createSlice({
  name: 'meta-admin',
  initialState: {
    isFetched: false,
    clientReport: {
      meta: null,
      pageFilter: null,
      dateFilter: null,
    },
    testimony: {
      meta: null,
      pageFilter: null,
      dateFilter: null,
    },
    counseling: {
      meta: null,
      pageFilter: null,
      dateFilter: null,
    },
  } as MetaProps,
  reducers: {
    setMetaTestimony(state, action: PayloadAction<MetaItemProps>) {
      state.testimony.meta = action.payload
    },
    setMetaCounseling(state, action: PayloadAction<MetaItemProps>) {
      state.counseling.meta = action.payload
    },
    setMetaClientReport(state, action: PayloadAction<MetaItemProps>) {
      state.clientReport.meta = action.payload
    },
    setPageFilterClientReport(state, action: PayloadAction<Partial<any>>) {
      state.clientReport.pageFilter = action.payload
    },
    setIsFetched(state, action: PayloadAction<boolean>) {
      state.isFetched = action.payload
    },
  },
})

export const {
  setMetaTestimony,
  setMetaClientReport,
  setPageFilterClientReport,
  setMetaCounseling,
  setIsFetched,
} = adminMetaSlice.actions
export default adminMetaSlice.reducer
