'use client'

import React, { useRef, useState } from 'react'
import AppInput from '@/components/_common/input/Input'
import { InputImage } from '@/components/_common/InputImage'
import { RadioInput } from '@/components/_common/RadioInput/RadioInput'
import { AppSelect } from '@/components/_common/Select/AppSelect'
import { UseFormGetValues, UseFormRegister, UseFormReturn, UseFormSetValue } from 'react-hook-form'
import {
  EducationOptionsList,
  EthnicityoptionsList,
  MaritalStatusOptions,
  OccupationOptionsList,
  Otheroptions,
  ReligionOptionsList,
} from '@/constans/onboardOptions'

const MAX_FILE_SIZE = 2 * 1024 * 1024 // 2MB

export default function formAddClientStepTwo({
  pageNumber,
  register,
  getValues,
  setValue,
  errors,
}: {
  pageNumber: number
  register: UseFormRegister<any>
  getValues: UseFormGetValues<any>
  setValue: UseFormSetValue<any>
  errors: UseFormReturn['formState']['errors']
  onSubmit: () => void
}) {
  const [previewImage, setPreviewImage] = useState<string | null>(null)
  const ref = useRef<any>(null)

  const handleFileChange = (file: File | null) => {
    if (file) {
      if (!['image/jpeg', 'image/png', 'image/webp'].includes(file.type)) {
        alert('Format file harus JPEG, PNG, atau WEBP')
        return
      }

      if (file.size > MAX_FILE_SIZE) {
        alert('Ukuran file maksimal 2MB')
        return
      }

      setPreviewImage(URL.createObjectURL(file))
      setValue('profilePhoto', file, { shouldValidate: true })
    }
  }

  return (
    <>
      <div className="flex flex-col gap-[6px] mb-4">
        <h2 className="text-[26px] font-bold">Informasi Pribadi</h2>
        <span className="text-[12px] text-[#737373]">Step {pageNumber} dari 4</span>
      </div>
      {/* foto */}
      <InputImage
        {...register('profilePhoto')}
        name="profilePhoto"
        label="Foto"
        accept="image/jpeg,image/png"
        onChange={handleFileChange}
        preview={previewImage || ''}
        objectPosition="center"
        inputRef={ref}
        maxFileSizeMb={2}
      />

      {/* radio status */}
      <RadioInput
        options={MaritalStatusOptions}
        name={'maritalStatus'}
        value={getValues('maritalStatus')!}
        label="Status Pernikahan"
        errorMsg={!!errors.maritalStatus ? String(errors.maritalStatus.message) : undefined}
        onChange={(val) => {
          setValue('maritalStatus', val, { shouldValidate: true })
        }}
      />

      {/* Urutan Bersaudara */}
      <div className="grid gap-2">
        <label className="text-body-md font-bold text-gray-400">Urutan Bersaudara*</label>
        <div className="flex gap-4 items-center">
          <AppInput
            {...register('childTo')}
            className="pt-0 max-w-[105px]"
            type="number"
            name="childTo"
            onChange={(val) => {
              setValue('childTo', val.target.value, { shouldValidate: true })
              const currentBirthOrder = getValues('birthOrder') ?? '0/0'
              const splitBirthOrder = currentBirthOrder.split('/')
              setValue('birthOrder', `${val.target.value}/${splitBirthOrder[1]}`, {
                shouldValidate: true,
              })
            }}
            placeholder="Anak ke"
          />
          <span>Dari</span>
          <AppInput
            {...register('totalSibling')}
            className="pt-0 grow"
            type="number"
            name="totalSibling"
            onChange={(val) => {
              setValue('totalSibling', val.target.value, { shouldValidate: true })
              const currentBirthOrder = getValues('birthOrder') ?? '0/0'
              const splitBirthOrder = currentBirthOrder.split('/')
              setValue('birthOrder', `${splitBirthOrder[0]}/${val.target.value}`, {
                shouldValidate: true,
              })
            }}
            placeholder="Jumlah Saudara"
          />
        </div>
        <label className="text-red-400 text-body-sm">
          {errors.childTo || errors.totalSibling ? 'Urutan Bersaudara harus diisi dengan benar' : undefined}
        </label>
      </div>

      {/* pendidikan */}
      <AppSelect
        {...register('education')}
        options={EducationOptionsList || []}
        onChange={(val) => setValue('education', val, { shouldValidate: true })}
        value={getValues('education') ? String(getValues('education')) : ''}
        className="h-[50px]"
        label="Pendidikan"
        name="education"
        placeholder="Pilih Pendidikan"
        errorMsg={!!errors.education ? String(errors.education.message) : undefined}
      />
      {/* Pekerjaan */}
      <AppSelect
        {...register('occupation')}
        options={OccupationOptionsList || []}
        onChange={(val) => setValue('occupation', val, { shouldValidate: true })}
        value={getValues('occupation') ? String(getValues('occupation')) : ''}
        className="h-[50px]"
        label="Pekerjaan"
        name="occupation"
        placeholder="Pilih Pekerjaan"
        errorMsg={!!errors.occupation ? String(errors.occupation.message) : undefined}
      />
      {getValues('occupation') === Otheroptions && (
        <AppInput
          {...register('occupationOther')}
          className="pt-0"
          type="text"
          name="occupationOther"
          placeholder="Silahkan masukan pekerjaan lainnya"
          errorMsg={!!errors.occupationOther ? String(errors.occupationOther.message) : undefined}
        />
      )}

      {/* Suku Bangsa */}
      <AppSelect
        {...register('ethnicity')}
        options={EthnicityoptionsList || []}
        onChange={(val) => setValue('ethnicity', val, { shouldValidate: true })}
        value={getValues('ethnicity') ? String(getValues('ethnicity')) : ''}
        className="h-[50px]"
        label="Suku Bangsa"
        name="ethnicity"
        placeholder="Pilih Suku Bangsa"
        errorMsg={!!errors.ethnicity ? String(errors.ethnicity.message) : undefined}
      />
      {getValues('ethnicity') === Otheroptions && (
        <AppInput
          {...register('ethnicityOther')}
          className="pt-0"
          type="text"
          name="ethnicityOther"
          placeholder="Silahkan masukan suku bangsa lainnya"
          errorMsg={!!errors.ethnicityOther ? String(errors.ethnicityOther.message) : undefined}
        />
      )}

      {/* Agama */}
      <AppSelect
        {...register('religion')}
        options={ReligionOptionsList || []}
        onChange={(val) => setValue('religion', val, { shouldValidate: true })}
        value={getValues('religion') ? String(getValues('religion')) : ''}
        className="h-[50px]"
        label="Agama"
        name="religion"
        placeholder="Pilih Agama"
        errorMsg={!!errors.religion ? String(errors.religion.message) : undefined}
      />
      {getValues('religion') === Otheroptions && (
        <AppInput
          {...register('religionOther')}
          className="pt-0"
          type="text"
          name="religionOther"
          placeholder="Silahkan masukan agama lainnya"
          errorMsg={!!errors.religionOther ? String(errors.religionOther.message) : undefined}
        />
      )}
    </>
  )
}
