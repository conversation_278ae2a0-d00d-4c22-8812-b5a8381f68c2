import Image from 'next/image'
import { Swiper, SwiperSlide } from 'swiper/react'
import { Pagination } from 'swiper/modules'
import 'swiper/css'
import 'swiper/css/pagination'
import './style.scss'
import TickIcon from '@/assets/icons/tick-list.svg'
import ButtonPrimary from '@/components/_common/ButtonPrimary'
import Link from 'next/link'

const ContentSlide = () => (
  <div className="flex flex-col max-w-[1120px] w-full gap-y-2">
    <div>
      <span className="font-bold text-[#004262]">#PrioritizingYourMentalHealth</span>
      <div className="text-[28px] text-[#004262] md:text-[50px] font-bold max-w-[646px] tracking-tight">
        Temukan Psikolog dan Layanan Psikologimu di Sini
      </div>
    </div>
    <div className="grid">
      <span className="flex">
        <TickIcon className="w-6 h-6 mr-2" />
        Psikolog Berlisensi HIMPSI, Profesional, dan <PERSON>
      </span>
      <span className="flex">
        <TickIcon className="w-6 h-6 mr-2" />
        Pendekatan Personal dan Fleksibel
      </span>
      <span className="flex">
        <TickIcon className="w-6 h-6 mr-2" />
        Kerahasiaan dan Keamanan Terjamin
      </span>
    </div>
    <div className="flex gap-x-2">
      <Link
        href={
          'https://api.whatsapp.com/send/?phone=6285173025865&text=Hi+Mental+Healing%2C+%28isi+pesan+kamu+disini%29&type=phone_number&app_absent=0'
        }
        target="_blank"
      >
        <ButtonPrimary
          textSize="text-body-lg"
          className="rounded-full shadow-lg"
          variant={'contained'}
          size="sm"
        >
          Daftar Konseling
        </ButtonPrimary>
      </Link>
    </div>
  </div>
)

const SlideContent = [{ id: 1, image: '/ilustration/hero-header.svg', content: <ContentSlide /> }]

export const SLiderHome = () => {
  const pagination = {
    clickable: true,
  }
  return (
    <div className="relative -mt-[64px] w-full max-w-full min-w-0">
      <Swiper spaceBetween={30} pagination={pagination} modules={[Pagination]} className="mySwiper w-full">
        {SlideContent.map((slide) => (
          <SwiperSlide
            key={slide.id}
            style={{
              width: '100%',
            }}
          >
            <div className="relative h-[640px] md:h-[840px] w-full flex bg-gradient-to-b from-[#E6F5FD] via-[#E6F5FD] via-70% to-white">
              <div className="w-full p-8 z-1 flex justify-center mt-mobileSliderhHeaderContent md:mt-desktopSliderhHeaderContent">
                {slide.content}
              </div>
              <Image
                src={slide.image}
                alt="mh-mascot"
                fill
                className="object-scale-down md:object-cover object-bottom md:object-center md:bg-[#34B1ED]"
              />
            </div>
          </SwiperSlide>
        ))}
      </Swiper>
      <div className="w-full hidden md:flex md:h-[90px] lg:h[100px] xl:h-[120px] relative">
        <Image
          src={'/ilustration/header-bot.svg'}
          alt="head-bot"
          fill
          className="object-cover object-bottom"
        />
      </div>
    </div>
  )
}
