import { useToast } from '@/components/ui/use-toast'
import { counsellingService } from '@/services/counselling.service'
import { useSelector } from '@/store'
import { AuthRole } from '@/store/auth/auth.action'
import { useQuery } from '@tanstack/react-query'

export const useGetCounselingDetails = (id: string) => {
  const { toast } = useToast()
  const { user } = useSelector((state) => state.Authentication)
  return useQuery({
    queryKey: ['CounselingDetails', { id }],
    queryFn: () => {
      if (user?.role === AuthRole.PSIKOLOG) {
        return counsellingService
          .getCounselingbyId(id)
          .then((response) => response)
          .catch((error) => {
            toast({
              description: 'Terjadi masalah dengan server, Silahkan hubungi Admin',
              variant: 'danger',
            })
          })
      } else if (user?.role === AuthRole.ADMIN || user?.role === AuthRole.SUPERADMIN) {
        return counsellingService
          .adminGetCounselingById(id)
          .then((response) => response)
          .catch((error) => {
            toast({
              description: '<PERSON><PERSON><PERSON><PERSON> masalah dengan server, Silahkan hubungi Admin',
              variant: 'danger',
            })
          })
      }
    },
  })
}
