import ButtonPrimary from '@/components/_common/ButtonPrimary'
import { AvatarWithInfo } from '@/components/_common/CardInfo/AvatarWithInfo'
import { IIcons } from '@/components/_common/icon'
import AppInput from '@/components/_common/input/Input'
import { ListInformation } from '@/components/_common/ListInformation'
import { AppBigText, AppMediumText } from '@/components/_common/ui'
import { useToast } from '@/components/ui/use-toast'
import { counsellingService } from '@/services/counselling.service'
import { formatStringToFulldateOutput, formatStringToTimeOutput } from '@/utils/displayDate'
import { useState } from 'react'

export default function AdminRejectForm({ item, onClose }: { item: any; onClose: () => void }) {
  const { toast } = useToast()
  const [steps, setSteps] = useState<number>(1)
  const [reason, setReason] = useState<string>('')

  const dateLabel = formatStringToFulldateOutput(item.startTime)
  const timeLabel = `${formatStringToTimeOutput({ date: item.startTime })} - ${formatStringToTimeOutput({ date: item.endTime })}`

  const handleClickReject = async () => {
    if (!reason) {
      toast({
        description: 'Silahkan isi alasan pembatalan terlebih dahulu.',
        variant: 'danger',
      })
      return
    }
    try {
      const payload = {
        message: reason,
      }
      await counsellingService.adminRejectCounselling(payload, item.id)
      setSteps(2)
    } catch (error) {
      toast({
        description: 'Terjadi masalah dalam proses pembatalan. Silahkan hubungi admin jika butuh bantuan.',
        variant: 'danger',
      })
    }
  }

  return (
    <div className="grid grid-cols-1 grid-rows-1 gap-4">
      {steps === 1 ? (
        <>
          <div className="grid gap-2">
            <AppBigText>Tolak jadwal koseling dengan {item?.client?.fullName}.</AppBigText>
            <ListInformation
              className="py-0 pb-2"
              listItem={[
                { label: dateLabel, icon: IIcons.Calendar },
                { label: timeLabel, icon: IIcons.Time },
              ]}
            />
          </div>
          <div className="grid grid-cols-4 grid-rows-1 gap-4 border-b border-line-200 pb-4 items-center">
            <AppMediumText bold className="col-span-4">
              Kirim pesan kepada {item?.client?.fullName}
            </AppMediumText>
            <div className="col-span-4">
              <AppInput
                type="textarea"
                placeholder="Beritahukan alasan mengapa Anda harus menolak jadwal konseling ini."
                value={reason || ''}
                onChange={(event) => setReason(event.target.value)}
                rows={3}
              />
            </div>
            <span className="col-span-4 text-danger-100 text-medium text-caption-md">
              Penolakan jadwal akan terhitung sebagai satu penalty bagi akun Anda. Penalty beberapa kali akan
              menyebabkan akun Anda dinonaktifkan.{' '}
            </span>
            <span className="col-span-4 text-gray-400 text-body-sm font-medium">
              Catatan: Selalu cek dan update jadwal ketersediaan untuk meminimalisir pembatalan jadwal.
            </span>
          </div>
          <div className="flex justify-end items-center gap-2">
            <ButtonPrimary
              onClick={() => handleClickReject()}
              className="rounded-sm"
              variant="outlined"
              size="xs"
              color="gray"
            >
              Tolak Jadwal Konseling
            </ButtonPrimary>
          </div>
        </>
      ) : steps === 2 ? (
        <>
          <div className="grid gap-2 border-b border-line-200 pb-4">
            <AppBigText>
              Kami telah mengirimkan informasi pembatalan kepada Klien. Setelah Klien mengkonfirmasi
              pembatalan jadwal, kami akan memberikan notifikasi kepada Anda.
            </AppBigText>
          </div>
          <AvatarWithInfo
            wrapClassName="items-center"
            orientation="column"
            className="w-[60px] h-[60px]"
            image={item?.client?.profilePhoto}
            name="Hilmi Salim"
          >
            <>
              <AppBigText bold>Konseling untuk {item?.client?.nickname} Dibatalkan</AppBigText>
              <ListInformation
                className="py-0 pb-2 border-b-0"
                listItem={[
                  { label: dateLabel, icon: IIcons.Calendar },
                  { label: timeLabel, icon: IIcons.Time },
                ]}
              />
            </>
          </AvatarWithInfo>
        </>
      ) : null}
    </div>
  )
}
