import { dispatch, useSelector } from '@/store'
import { RescheduleDateTime } from './RescheduleDateTime'
import { RescheduleNote } from './RescheduleNote'
import { RescheduleSuccessState } from './RescheduleSuccessState'
import { useState } from 'react'
import { useGetScheduleByDate } from '@/hooks/useGetScheduleByDate.hook'
import { MOMENT_INPUT_DATE_FORMAT } from '@/constans/date'
import { setRescheduleStep } from '@/store/psikolog/schedule.reducer'
import { useToast } from '@/components/ui/use-toast'
import { counsellingService } from '@/services/counselling.service'
import moment from 'moment-timezone'

export default function ScheduleForm({
  callbackToggleCalendar,
  item,
  onClose,
}: {
  callbackToggleCalendar?: (arg: boolean) => void
  item: any
  onClose: () => void
}) {
  const { toast } = useToast()
  const counselingDuration = item?.duration || 60
  const { rescheduleStep, rescheduleDetail } = useSelector((state) => state.psikologSchedule)
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date())
  const [selectedTime, setSelectedTime] = useState<string>('')

  const { data: dataSchedule } = useGetScheduleByDate(
    selectedDate ? moment(selectedDate).format(MOMENT_INPUT_DATE_FORMAT) : '',
    counselingDuration
  )

  const handleClickSelectedDate = (date: Date | undefined) => {
    setSelectedDate(date)
    setSelectedTime('')
  }
  const handleClickSelectedTime = (time: any) => {
    setSelectedTime(time)
  }
  const handleClickSubmit = async () => {
    if (!rescheduleDetail?.note) {
      toast({
        variant: 'danger',
        description: 'lengkapi data terlebih dahulu.',
      })
      return
    }
    try {
      const scheduleDateTime = moment
        .tz(`${rescheduleDetail?.date} ${rescheduleDetail?.time}`, dataSchedule?.schedule?.[0]?.timezone)
        .toISOString()

      const payload = {
        schedule: scheduleDateTime,
        message: rescheduleDetail?.note || '',
      }
      await counsellingService.rescheduleCounselling(item?.id, payload)
      dispatch(setRescheduleStep(rescheduleStep + 1))
    } catch (error: any) {
      let message = 'Terjadi kesalahan saat merubah jadwal. Silahkan ulangi beberapa saat lagi.'
      if (error?.response?.data?.errorCode === 'SCHEDULE_ALREADY_BOOKED') {
        message = 'Jadwal konseling yang Anda pilih sudah terisi, silahkan pilih jadwal lain.'
      } else if (error?.response?.data?.errorCode === 'RESCHEDULE_TOO_CLOSE') {
        message = 'Jadwal konseling yang Anda pilih terlalu dekat, silahkan pilih jadwal lain.'
      }
      toast({
        variant: 'danger',
        description: message,
      })
    }
  }

  return (
    <div className="grid grid-cols-1 grid-rows-1 gap-4">
      {rescheduleStep === 1 ? (
        <RescheduleDateTime
          timeSelected={selectedTime}
          dateSelected={selectedDate}
          item={item}
          timeList={dataSchedule?.schedule}
          callbackToggleCalendar={callbackToggleCalendar}
          onSelectedDate={(date) => handleClickSelectedDate(date)}
          onSelectedTime={(time) => handleClickSelectedTime(time)}
        />
      ) : rescheduleStep === 2 ? (
        <RescheduleNote
          counselingDuration={counselingDuration}
          clientName={item?.client?.fullName}
          onSubmit={() => handleClickSubmit()}
        />
      ) : rescheduleStep === 3 ? (
        <RescheduleSuccessState
          counselingDuration={counselingDuration}
          clientName={item?.client?.fullName}
          profilePhoto={item?.client?.profilePhoto}
        />
      ) : null}
    </div>
  )
}
