import { LabelValue } from '@/components/_common/CardInfo/LabelValue'
import useGetPsychologistTimezone from '@/hooks/useGetPsychologistTimezone.hook'
import useGetTimezoneLabel from '@/hooks/useGetTimezone.hook'
import { ScheduleListProps } from '@/store/psikolog/schedule.reducer'
import { formatStringToDateOutput, formatStringToFullDateTimeOutput } from '@/utils/displayDate'

export const ScheduleItemContent = ({
  startTime,
  problemCategory,
  expectation,
  description,
  messageForPsychologist,
  messageForClient,
  createdAt,
  duration,
}: ScheduleListProps) => {
  const psychologistTimezone = useGetPsychologistTimezone()
  const timeZoneLabel = useGetTimezoneLabel()
  const problemcategoryOutput = problemCategory?.length
    ? problemCategory?.map((val) => val.problemCategory).join(', ')
    : ''
  const createdAtOutput = createdAt
    ? formatStringToFullDateTimeOutput({
        date: createdAt,
        timezone: psychologistTimezone,
        timeLabel: timeZoneLabel,
      })
    : ''
  const note = messageForPsychologist ? messageForPsychologist : messageForClient ? messageForClient : '-'
  const activityTitle = duration === 60 ? 'Konseling - Healing 1' : 'Konseling - Healing 2'
  return (
    <div className="grid grid-cols-3 grid-rows-1 gap-y-4">
      <span className="col-span-3">
        <LabelValue
          labelClass="text-body-sm font-medium text-gray-300"
          valueClass="font-bold text-gray-400"
          label="Jenis Kegiatan"
          value={activityTitle ?? ''}
        />
      </span>

      <span className="col-span-3">
        <LabelValue
          labelClass="text-body-sm font-medium text-gray-300"
          valueClass="font-bold text-gray-400"
          label="Kategori Permasalahan"
          value={problemcategoryOutput ?? ''}
        />
      </span>

      <span className="col-span-3">
        <LabelValue
          labelClass="text-body-sm font-medium text-gray-300"
          valueClass="font-bold text-gray-400"
          label="Harapan setelah konseling"
          value={expectation ?? ''}
        />
      </span>
      <span className="col-span-3">
        <LabelValue
          displayRows
          labelClass="text-body-sm font-medium text-gray-300"
          valueClass="font-medium text-gray-400"
          label="Detail Keluhan"
          value={description ?? ''}
        />
      </span>
      <span className="col-span-3">
        <LabelValue
          displayRows
          labelClass="text-body-sm font-medium text-gray-300"
          valueClass="font-bold text-gray-400"
          label="Keterangan"
          value={note ?? ''}
        />
      </span>
      <span className="col-span-3">
        <LabelValue
          displayRows
          labelClass="text-body-sm font-medium text-gray-300"
          valueClass="font-medium text-gray-400"
          label={`Jadwal dibuat: ${createdAtOutput ?? ''}`}
          value=""
        />
      </span>
    </div>
  )
}
