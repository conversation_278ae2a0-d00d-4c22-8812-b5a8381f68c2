'use client'

import React from 'react'
import { BigTitleTypography } from '../_common/ui'
import TabList from '../_common/tabs/TabList'
import TabItem from '../_common/tabs/TabItem'

type DetailDataProps = {
  title: string
  Header: () => JSX.Element | React.ReactNode
  tabList: any[]
}

export const DetailData = ({ title, Header, tabList }: DetailDataProps) => {
  return (
    <>
      <div className="flex sm:flex sm:items-center pb-6">
        <BigTitleTypography>Detail {title}</BigTitleTypography>
      </div>
      <Header />
      <div className="gap-4 mt-4">
        <TabList className="sticky top-navbar z-30 bg-white" activeTabIndex={0}>
          {tabList.map((tab, index) => {
            return (
              <TabItem className="bg-main-100" key={index} label={tab.label}>
                {tab.content}
              </TabItem>
            )
          })}
        </TabList>
      </div>
    </>
  )
}
