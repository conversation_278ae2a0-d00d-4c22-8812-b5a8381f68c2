import ButtonPrimary from '@/components/_common/ButtonPrimary'
import AppInput from '@/components/_common/input/Input'
import { useEffect, useState } from 'react'
import { Skeleton } from '@/components/ui/skeleton'
import YoutubeEmbed from '@/components/psikolog/setting/media/YoutubeEmbedVideo'

type InputYoutubelinkProps = {
  isEdit?: boolean
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void
  onSubmit: (link: string | undefined) => void
  onEdit: () => void
  btnShow?: boolean
  value: string | undefined
  isLoading?: boolean
}

const API_KEY = 'AIzaSyAdTcjjVniUazHxgEQy5Wn99k--Noz-1XE'

export const VideoPsikolog = ({
  isEdit = true,
  value,
  onEdit,
  onSubmit,
  isLoading: isLoadingApp,
}: InputYoutubelinkProps) => {
  const [isLoading, setIsLoading] = useState(false)
  const [link, setLink] = useState<string>('')
  const [title, setTitle] = useState<string>('')
  const [inputLink, setInputLink] = useState<string | undefined>(value)

  const handleClickSave = () => {
    setIsLoading(true)
    setTimeout(() => {
      setIsLoading(false)
    }, 1500)
    onSubmit(inputLink)
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    e.preventDefault()
    setInputLink(e.target.value)
  }

  useEffect(() => {
    async function fetchYTApi(embedId: string) {
      const response = await fetch(
        `https://youtube.googleapis.com/youtube/v3/videos?part=snippet%2CcontentDetails%2Cstatistics&id=${embedId}&key=${API_KEY}`
      )
        .then((res) => res.json())
        .then((result) => {
          const videoItem = result.items[0]
          setLink(`https://www.youtube.com/embed/${embedId}`)
          setTitle(videoItem?.snippet?.title)
        })
        .catch((error) => {
          console.log(error)
        })
      return response
    }
    try {
      const linkSrc = inputLink ?? ''
      const getEmbedId = linkSrc?.split('v=')[1]
      const embedId = getEmbedId?.split('&')[0]
      fetchYTApi(embedId)
    } catch (error) {
      console.log(error)
    }
  }, [inputLink])

  if (isEdit) {
    return (
      <div className="flex items-center gap-4">
        <label className={`cursor-pointer flex items-center bg-opacity-50`}>
          {isLoading && <Skeleton className="h-[62px] w-[110px] rounded-xl" />}
          <YoutubeEmbed height="62" width="110" className={isLoading ? 'hidden' : ''} src={link ?? ''} />
        </label>
        <AppInput className="flex-1" type={'text'} value={inputLink ?? ''} onChange={handleChange} />
        <ButtonPrimary
          className="h-[52px]"
          isLoading={isLoading || isLoadingApp}
          variant={'outlined'}
          color="gray"
          size="xs"
          onClick={() => handleClickSave()}
        >
          Simpan
        </ButtonPrimary>
      </div>
    )
  }
}
