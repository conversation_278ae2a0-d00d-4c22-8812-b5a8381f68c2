'use client'
import { useEffect, useState } from 'react'
import TabItem from '@/components/_common/tabs/TabItem'
import TabList from '@/components/_common/tabs/TabList'
import { HeaderContent } from '@/components/admin/HeaderContent'
import ContentKonseling from '@/components/userTransaksi/contentKonseling'
import ContentPending from '@/components/userTransaksi/contentPending'
import ContentRiwayat from '@/components/userTransaksi/contentRiwayat'
import { useCounseling } from '@/hooks/useCounselings'

export default function TransaksiComponent() {
  const [activeTabIndex, setActiveTabIndex] = useState<number>(0)
  const {
    counselingList,
    loading,
    error,
    meta,
    getUpcomingCounseling,
    getPendingPaymentCounseling,
    getHistoryCounseling,
  } = useCounseling()

  // State to hold counts for each tab
  const [tabCounts, setTabCounts] = useState({
    upcoming: 0,
    pending: 0,
    history: 0,
  })

  // State to hold data for each tab content
  const [counselingData, setCounselingData] = useState({
    upcoming: [],
    pending: [],
    history: [],
  })

  useEffect(() => {
    // Function to fetch all data needed for tabs
    const fetchAllData = async () => {
      try {
        // Fetch data for each status category
        const upcomingResult = await getUpcomingCounseling()
        const pendingPaymentResult = await getPendingPaymentCounseling()
        const historyCounseling = await getHistoryCounseling()

        // Update the counts
        setTabCounts({
          upcoming: upcomingResult?.meta.total || 0,
          pending: pendingPaymentResult?.meta.total || 0,
          history: historyCounseling?.meta.total || 0,
        })

        // Update the data for each tab
        setCounselingData({
          upcoming: upcomingResult?.data || [],
          pending: pendingPaymentResult?.data || [],
          history: historyCounseling?.data || [],
        })
      } catch (err) {
        console.error('Error fetching counseling data:', err)
      }
    }

    fetchAllData()
  }, [])

  const SettingTab = [
    {
      label: `Konseling (${tabCounts.upcoming})`,
      content: (
        <ContentKonseling
          title="Konseling dengan Psikolog"
          data={counselingData.upcoming}
          loading={loading}
        />
      ),
    },
    {
      label: `Pending (${tabCounts.pending})`,
      content: (
        <ContentPending
          title="Menunggu Konfirmasi Psikolog"
          title2="Menunggu Konfirmasi Kamu"
          title3="Menunggu Pembayaran Konseling"
          pendingData={counselingData.pending}
          loading={loading}
        />
      ),
    },
    {
      label: `Riwayat (${tabCounts.history})`,
      content: (
        <ContentRiwayat
          title="Konseling Selesai"
          title2="Konseling Dibatalkan"
          title3="Pembayaran Konseling Gagal"
          historyData={counselingData.history}
          loading={loading}
        />
      ),
    },
  ]

  return (
    <>
      <div className="lg:mt-[64px] pt-0 md:pt-4 lg:pt-0 px-0 md:px-4 lg:px-0 lg:w-[930px] xl:w-[1120px] max-w-[1120px] flex flex-col justify-center gap-0 md:gap-6 w-full mb-10 md:mb-[200px]">
        {/* mobile */}
        <div className="bg-[#E7F7FF] relative z-10 block md:hidden">
          <HeaderContent title="Transaksi" className="px-4 md:px-0 my-12 md:mt-0 lg:mt-20 relative z-10" />
          <figure className="w-[100px] h-[100px] absolute bottom-4 right-4">
            <img src={'/ilustration/call.svg'} alt="ilustration" className="w-full h-full" />
          </figure>
        </div>
        {/* desktop */}
        <HeaderContent title="Transaksi" className="hidden sm:hidden md:block md:mb-0 lg:mb-10" />
        {/* navigation */}
        <div className="-mt-4 relative z-10 bg-white rounded-t-[15px]">
          <div className="gap-4 rounded-t-[15px]">
            <TabList
              onClickTabs={(index) => {
                setActiveTabIndex(index)
              }}
              className="top-navbar pl-4 md:pl-0  rounded-t-[15px]"
              activeTabIndex={activeTabIndex}
            >
              {SettingTab.map((setting, index) => {
                return (
                  <TabItem className="" key={index} label={setting.label}>
                    {setting.content}
                  </TabItem>
                )
              })}
            </TabList>
          </div>
        </div>

        {error && <div className="p-4 bg-red-100 text-red-700 rounded-md mt-4 mx-4 md:mx-0">{error}</div>}
      </div>
    </>
  )
}
