'use client'
import Breadcrumb from '@/components/breadcrumbs/Breadcrumbs'
import { useGetPageInformation } from '../hook/useGetPageInformation.hook'
import { HeaderContent } from '@/components/admin/HeaderContent'

type ProfileInformationProps = {
  pageId: string
}

export const ProfileInformation = ({ pageId }: ProfileInformationProps) => {
  const { data } = useGetPageInformation(pageId)
  return (
    <div className="lg:mb-[196px] lg:mt-[64px] px-4 lg:px-0 lg:w-[930px] xl:w-[1120px] max-w-[1120px] flex flex-col justify-center gap-6 relative z-0 py-4 lg:py-0 mb-[150px] md:mb-0">
      <Breadcrumb containerClasses="hidden md:flex" pageName={data?.title} />
      <HeaderContent className="mb-0 gap-y-0" title={data?.title} />
      <div className="flex flex-col gap-4">
        <span className="text-[12px] text-[#535353]">{data?.subtitle}</span>
        <div dangerouslySetInnerHTML={{ __html: data?.content }}></div>
      </div>
    </div>
  )
}
