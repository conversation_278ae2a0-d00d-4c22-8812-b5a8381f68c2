import { AppBigCaption, AppMediumText, Card } from '@/components/_common/ui'
import QuoteIcon from '@/assets/icons/quote.svg'
import { SpecializationList } from '../OurPsychologist/SpecializationList'
import { Separator } from '@/components/ui/separator'
import { AvatarWithInfo } from '@/components/_common/CardInfo/AvatarWithInfo'

type TestimonyProps = {
  testimony: string
  clientName: string
  gender: string
  psychologist: {
    fullName: string
    profilePhoto: string
    problemCategory: any[]
  }
}
export const TestimonyCardItem = (testimony: TestimonyProps) => {
  return (
    <Card className="p-0 xs:p-0 sm:p-0 md:p-0 bg-white w-[262px] flex flex-col justify-center gap-y-2">
      <div className="p-3 flex flex-col gap-y-3">
        <QuoteIcon />
        <span className="text-body-sm font-medium text-gray-400">{testimony?.testimony}</span>
        <span className="text-body-sm font-medium text-gray-300">{testimony?.clientName}</span>
      </div>
      <Separator />
      <div className="p-3 flex flex-col gap-y-3">
        <span className="text-body-sm font-medium text-gray-300">Konseling dengan Psikolog</span>
        <AvatarWithInfo
          className="w-[42px] h-[42px]"
          image={testimony?.psychologist?.profilePhoto ?? ''}
          heading={<AppMediumText bold>{testimony?.psychologist?.fullName}</AppMediumText>}
          subHeading={
            <AppBigCaption className="text-gray-200">
              <SpecializationList
                className="text-body-md font-medium text-gray-300"
                list={testimony?.psychologist?.problemCategory || []}
                showItem={2}
              />
            </AppBigCaption>
          }
        />
      </div>
    </Card>
  )
}
