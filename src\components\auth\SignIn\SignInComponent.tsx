'use client'
import { But<PERSON> } from '@/components/ui/button'
import Title from '../Title'
import WrapperAuth from '../WrapperAuth'
import Image from 'next/image'
import ImageGoogle from '@/assets/icons/google.png'
import AppInput from '@/components/_common/input/Input'
import ButtonPrimary from '@/components/_common/ButtonPrimary'
import { signInWithEmailAndPassword, User } from 'firebase/auth'
import { signInWithGoogle } from '@/lib/firebase/auth'
import { dispatch, useSelector } from '@/store'
import { AuthRole, verifyAuth, verifyAuthSignInWithEmail } from '@/store/auth/auth.action'
import { useEffect, useState } from 'react'

import { yupResolver } from '@hookform/resolvers/yup'
import { useForm } from 'react-hook-form'
import * as yup from 'yup'
import { useRouter } from 'next/navigation'
import { Routes } from '@/constans/routes'
import Translation from '@/constans/Translation'
import { authService } from '@/services/auth.service'
import { useToast } from '@/components/ui/use-toast'
import { firebaseAuth } from '@/lib/firebase/config'
import { getValidAuthRole, getValidAuthTokens } from '@/lib/cookies'

const validationSchema = yup.object().shape({
  email: yup.string().required(Translation.RequiredEmail).email(Translation.ValidEmail),
  password: yup.string().required(Translation.PasswordRequired),
})

export type SignInGoogleResponse = {
  token?: string
  providerId?: string
  user?: User
}

const SignInComponent = () => {
  const { toast } = useToast()
  const [isCompleteEmail, setIsCompleteEmail] = useState<boolean>(false)
  const { isCompleteOnboard } = useSelector((state) => state.Authentication)
  const userToken = getValidAuthTokens()
  const userRole = getValidAuthRole()
  const router = useRouter()
  const [emailState, setEmailState] = useState<string>('')
  const [isLoadingPage, setIsLoadingPage] = useState<boolean>(false)
  const [isRegister, setIsRegister] = useState<boolean>(false)
  const [errorMessage, setErrorMessage] = useState<string | null>(null)
  const [errorState, setErroState] = useState<string | null>(null)

  useEffect(() => {
    if (!!userToken) {
      if (isCompleteOnboard && userRole) {
        if (userRole === AuthRole.ADMIN || userRole === AuthRole.SUPERADMIN) {
          router.push(Routes.AdminHome)
        }
        if (userRole === AuthRole.PSIKOLOG) {
          router.push(Routes.PsychologistHome)
        }
        if (userRole === AuthRole.CLIENT) {
          router.push(Routes.UserHome)
        }
      } else {
        router.push(Routes.Onboard)
      }
    }
  }, [userToken, isCompleteOnboard, router, userRole])

  async function handleClickThirdParty() {
    try {
      const response = await signInWithGoogle()
      const { token, user } = response as SignInGoogleResponse

      dispatch(verifyAuth({ user, token } as SignInGoogleResponse))
    } catch (error) {
      console.log(error)
    }
  }

  const {
    register,
    handleSubmit,
    trigger,
    setValue,
    formState: { errors, isLoading, isSubmitting },
  } = useForm({
    resolver: yupResolver(validationSchema),
  })

  const handleClickNext = async () => {
    setIsRegister(false)
    setIsLoadingPage(true)
    trigger('email')
    try {
      if (!errors.email) {
        setErrorMessage(null)
        const validEmail = await authService.verifyingEmail(emailState)
        if (validEmail.isExist) {
          setIsCompleteEmail(true)
          setTimeout(() => {
            setIsLoadingPage(false)
          }, 1000)
        } else {
          setTimeout(() => {
            setIsRegister(true)
            setErrorMessage(
              `Email <strong>${emailState}</strong> tidak ditemukan. Klik Daftar untuk buat akun.`
            )
            setIsLoadingPage(false)
          }, 1000)
        }
      } else {
        setIsLoadingPage(false)
        setIsCompleteEmail(false)
      }
    } catch (error) {
      setIsLoadingPage(false)
      setIsCompleteEmail(false)
    }
  }

  async function onSubmitSignIn(data: { email: string; password: string }) {
    try {
      setErroState(null)
      setIsLoadingPage(true)
      await signInWithEmailAndPassword(firebaseAuth, data.email, data.password)
        .then((res) => {
          dispatch(verifyAuthSignInWithEmail(res))
        })
        .catch((err) => {
          if (err?.code === 'auth/invalid-credential') {
            setErroState('Email atau password yang anda masukkan tidak valid.')
          } else if (err?.code === 'auth/user-not-found') {
            setErroState('Email yang anda masukkan tidak ditemukan.')
          } else if (err?.code === 'auth/invalid-password') {
            setErroState('Password yang anda masukkan tidak valid.')
          }
          setIsLoadingPage(false)
        })
        .finally(() => {
          setIsLoadingPage(false)
        })
    } catch (error) {
      setIsLoadingPage(false)
    } finally {
      setIsLoadingPage(false)
    }
  }
  async function onSubmitSignUp() {
    setIsLoadingPage(true)
    try {
      authService
        .signinWithEmail(emailState)
        .then((res) => {
          toast({
            variant: 'success',
            title: res?.message || 'Success send email',
          })
          setTimeout(() => {
            setIsLoadingPage(false)
            window.localStorage.setItem('emailForRegistration', emailState)
            router.push(Routes.EmailVerificationMessage + '?email=' + emailState)
          }, 500)
        })
        .catch((err) => {
          setIsLoadingPage(false)
          toast({
            title: err?.response?.data?.message ?? 'Failed send email',
          })
        })
    } catch (error) {
      setIsLoadingPage(false)
      toast({
        variant: 'danger',
        title: 'Kirim email gagal, silahkan coba beberapa saat lagi',
      })
    }
  }

  return (
    <WrapperAuth>
      <div className="grid gap-y-4">
        <Title
          title="Hai, Senang Bertemu Anda"
          subTitle="Yuk, Masuk dulu untuk dapat mengakses semua fitur yang ada."
        />
        <Button
          variant="outline"
          className="w-full p-3 min-h-[48px] space-x-2"
          onClick={() => handleClickThirdParty()}
        >
          <Image src={ImageGoogle} width={24} height={24} alt="google-icon" />
          Masuk dengan Google
        </Button>
        <div className="relative flex items-center">
          <div className="flex-grow border-t border-line-200"></div>
          <span className="flex-shrink mx-4 text-gray-400">Atau</span>
          <div className="flex-grow border-t border-line-200"></div>
        </div>
        <div>
          <AppInput
            {...register('email')}
            errorMsg={!!errors.email ? errors.email.message : undefined}
            className="pt-0"
            label="Email"
            type="email"
            name="email"
            onChange={(e) => {
              if (errorMessage) {
                setIsRegister(false)
                setErrorMessage(null)
              }
              setValue('email', e.target.value, { shouldValidate: true })
              setEmailState(e.target.value)
            }}
          />
          {errorMessage ? (
            <span
              className="text-gray-300 text-caption-md"
              dangerouslySetInnerHTML={{ __html: errorMessage }}
            ></span>
          ) : null}
        </div>
        {isCompleteEmail && (
          <AppInput
            {...register('password')}
            name="password"
            className="pt-0"
            label="Password"
            type="password"
            onForgotPassword={() => router.push(Routes.UserForgotPassword)}
            errorMsg={!!errors.password ? errors.password.message : errorState ? errorState : undefined}
          />
        )}
        <ButtonPrimary
          disabled={isLoading || isLoadingPage || isSubmitting}
          isLoading={isLoading || isLoadingPage || isSubmitting}
          variant="contained"
          size="xs"
          className="w-full p-3 min-h-[48px] space-x-2"
          onClick={() => {
            if (isRegister) {
              onSubmitSignUp()
            } else {
              if (isCompleteEmail) {
                handleSubmit(onSubmitSignIn)()
              } else {
                handleClickNext()
              }
            }
          }}
        >
          {isRegister ? 'Daftar' : isCompleteEmail ? 'Masuk' : 'Selanjutnya'}
        </ButtonPrimary>
      </div>
    </WrapperAuth>
  )
}

export default SignInComponent
