import ButtonPrimary from '@/components/_common/ButtonPrimary'
import { AppBigText } from '@/components/_common/ui'
import { Checkbox } from '@/components/ui/checkbox'
import { useToast } from '@/components/ui/use-toast'
import { counsellingService } from '@/services/counselling.service'
import { useState } from 'react'

export default function ApproveCounselingDialog({ item, onClose }: { item: any; onClose: () => void }) {
  const { toast } = useToast()
  const [isChecked, setIsChecked] = useState<boolean>(false)

  const handleClickApproveCounseling = async () => {
    if (isChecked === false) {
      toast({
        title: 'Gagal',
        description: 'Anda belum menyetujui syarat dan ketentuan dari tindakan ini. ',
        variant: 'danger',
      })
      return
    }
    try {
      await counsellingService.adminApproveCounselling(item.id)
      toast({
        title: 'Berhasil',
        description: (
          <span>
            Konseling dengan <span className="font-bold">{item.client?.fullName}</span> berhasil anda
            diterima.
          </span>
        ),
        variant: 'success',
      })
      onClose()
    } catch (err) {
      toast({
        title: 'Gagal',
        description: 'Konseling gagal diterima. Silahkan coba lagi.',
        variant: 'danger',
      })
    }
  }

  return (
    <div className="grid grid-cols-1 grid-rows-1 gap-4">
      <AppBigText>
        Pastikan Anda dapat menghadiri jadwal konseling yang sudah Anda terima dan disetujui bersama.
      </AppBigText>
      <AppBigText>
        Pembatalan jadwal oleh Psikolog akan mendapatkan pinalti yang dapat mengakibatkan sanksi dalam
        platform Mentalhealing.id.{' '}
      </AppBigText>
      <div key={item.title} className="flex justify-between items-center space-x-2">
        <label
          htmlFor={item.title}
          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex items-center gap-2"
        >
          <Checkbox
            checked={isChecked}
            onCheckedChange={(checked) => setIsChecked(!!checked)}
            className="rounded-[4px] disabled:opacity-50 disabled:data-[state=checked]:bg-gray-100 disabled:data-[state=checked]:border-0"
            id={item.title}
          />
          <span className="text-body-lg">Saya mengerti, jangan tampilkan informasi ini kembali.</span>
        </label>
      </div>

      <div className="flex flex-wrap justify-center sm:justify-end items-center gap-2">
        <ButtonPrimary
          onClick={() => onClose && onClose()}
          className="rounded-sm w-full sm:w-auto"
          variant="outlined"
          size="xs"
          color="gray"
        >
          Kembali
        </ButtonPrimary>
        <ButtonPrimary
          onClick={() => handleClickApproveCounseling()}
          className="rounded-sm w-full sm:w-auto"
          variant="contained"
          size="xs"
        >
          Ya, Terima
        </ButtonPrimary>
      </div>
    </div>
  )
}
