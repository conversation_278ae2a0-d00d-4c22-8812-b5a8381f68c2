'use client'
import Title from '../Title'

import { useRouter, useSearchParams } from 'next/navigation'
import { Routes } from '@/constans/routes'
import { Separator } from '@/components/ui/separator'
import EmailIlustration from '@/assets/ilustration/email.svg'
import { useEffect, useState, useRef } from 'react'
import Link from 'next/link'
import { getAuth, isSignInWithEmailLink, signInWithEmailLink, UserCredential } from 'firebase/auth'
import { dispatch, useSelector } from '@/store'
import { AuthRole, verifyAuthSignInWithEmail } from '@/store/auth/auth.action'
import { getValidAuthRole, getValidAuthTokens } from '@/lib/cookies'

export const EmailValidate = () => {
  const router = useRouter()
  const { isCompleteOnboard } = useSelector((state) => state.Authentication)
  const userToken = getValidAuthTokens()
  const userRole = getValidAuthRole()
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [error, setError] = useState<string | null>(null)
  const runValidateCount = useRef(0)
  const searchParam = useSearchParams()
  const emailDeepLink = searchParam.get('email') ?? ''

  useEffect(() => {
    const abortController = new AbortController()

    const validatingEmail = async () => {
      setIsLoading(true)
      try {
        const auth = getAuth()
        console.log(auth)
        if (isSignInWithEmailLink(auth, window.location.href)) {
          let email = window.localStorage.getItem('emailForRegistration') || emailDeepLink
          if (!email) {
            setTimeout(() => {
              setError('Harap lengkapi email anda untuk di validasi')
              setIsLoading(false)
            }, 1000)
            return
          }
          const result = await signInWithEmailLink(auth, email, window.location.href)
          window.localStorage.removeItem('emailForRegistration')
          dispatch(verifyAuthSignInWithEmail(result))
        }
      } catch (err) {
        console.log(err)
        setTimeout(() => {
          setError('Terjadi gangguan teknis, silahkan hubungi admin')
          setIsLoading(false)
        }, 1000)
      }
    }

    if (runValidateCount.current === 0) {
      validatingEmail()
    }

    return () => {
      runValidateCount.current += 1
      return abortController.abort()
    }
  }, [])

  useEffect(() => {
    if (!!userToken) {
      if (isCompleteOnboard && userRole) {
        if (userRole === AuthRole.ADMIN) {
          router.push(Routes.AdminHome)
        }
        if (userRole === AuthRole.PSIKOLOG) {
          router.push(Routes.PsychologistHome)
        }
        if (userRole === AuthRole.CLIENT) {
          router.push(Routes.UserHome)
        }
      } else {
        router.push(Routes.Onboard)
      }
    }
  }, [userToken, isCompleteOnboard, router, userRole])

  return (
    <div className="grid gap-y-4">
      <div className="relative max-h-[100px] max-w-[100px] mx-auto text-center">
        <EmailIlustration />
      </div>
      <Title
        center
        title="Memvalidasi Email Anda"
        subTitle="Silahkan tunggu beberapa saat, akan diarahkan ke halaman lengkapi data diri. "
      />
      <Separator />
      <div className="flex flex-col gap-y-[8px] text-center justify-center items-center">
        {error ? (
          error
        ) : isLoading ? (
          <div className="border-t-transparent border-solid animate-spin  rounded-full border-blue-400 border-8 h-20 w-20"></div>
        ) : null}
      </div>
      {!error ? (
        <>
          <Separator />
          <span className="`text-body-sm font-medium text-gray-400 text-center">
            Menuju halaman{' '}
            <Link className="text-main-100 underline" href={Routes.Onboard}>
              lengkapi data diri
            </Link>
          </span>
        </>
      ) : null}
    </div>
  )
}

export default EmailValidate
