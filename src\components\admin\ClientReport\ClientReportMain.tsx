'use client'
import TabItem from '@/components/_common/tabs/TabItem'
import TabList from '@/components/_common/tabs/TabList'
import { Card } from '@/components/_common/ui'
// import { columns } from './columns'
import { ColumnDef } from '@tanstack/react-table'
import { HeaderContent } from '../HeaderContent'
import { DateRange } from 'react-day-picker'
import { useEffect, useRef, useState } from 'react'
import { ListingClientReport } from './ListingClientReport'
import { ADMIN_CLIENT_REPORT_API } from '@/constans/API_PATH'
import { UserPhoto } from '@/components/UserPhoto/UserPhoto'
import { InfoDataDisplay } from '@/components/datagrid/InfoDataDisplay'
import moment from 'moment-timezone'
import 'moment/locale/id'
import { useDispatch, useSelector } from '@/store'

export const ClientReportMain = () => {
  const refRefetchAvailable = useRef<any>()
  const refRefetchWaiting = useRef<any>()
  const dispatch = useDispatch()
  const { clientReport } = useSelector((state) => state.Meta)
  const [rangeDate, setRangeDate] = useState<DateRange | undefined>({ from: new Date(), to: new Date() })
  const [pageFilter, setPageFilter] = useState<any[]>([])
  const [activeTabIndex, setActiveTabIndex] = useState<number>(0)
  const meta = clientReport.meta

  useEffect(() => {
    activeTabIndex === 0 && refRefetchAvailable.current && refRefetchAvailable.current()
    activeTabIndex === 1 && refRefetchWaiting.current && refRefetchWaiting.current()
  }, [activeTabIndex])

  const columns: ColumnDef<any>[] = [
    {
      accessorKey: 'id',
      header: 'Klien Report ID',
    },
    {
      accessorKey: 'client',
      header: 'Klien',
      cell: ({ cell, row }) => {
        return (
          <div className="font-bold hover:underline hover:text-main-100">
            <UserPhoto
              photo={cell.row.original['client']?.['profilePhoto']}
              title={cell.row.original['client']?.['fullName']}
            />
          </div>
        )
      },
    },
    {
      accessorKey: 'psikolog',
      header: 'Psikolog',
      cell: ({ cell, row }) => {
        return (
          <div className="font-bold hover:text-main-100">
            <UserPhoto
              photo={cell.row.original['psychologist']?.['profilePhoto']}
              title={cell.row.original['psychologist']?.['fullName']}
              subTitle="No-Specialization"
            />
          </div>
        )
      },
    },
    {
      accessorKey: 'jadwal',
      header: 'Jadwal',
      cell: ({ cell, row }) => {
        const date = moment(cell.row.original?.counseling?.startTime).locale('id').format('dddd, D MMM YYYY')
        const time = moment(cell.row.original?.counseling?.startTime).locale('id').format('HH:mm')
        return <InfoDataDisplay title={date} subTitle={time} />
      },
    },
  ]

  const CounsellingData = [
    {
      label: `Tersedia (${meta?.additionalMeta?.complete ?? 0})`,
      content: (
        <ListingClientReport
          showSecondFilter
          refRefetch={refRefetchAvailable}
          fetchPath={ADMIN_CLIENT_REPORT_API}
          pageFilter={pageFilter}
          actions={['update', 'delete']}
          rangeDate={rangeDate}
          columns={columns as unknown as ColumnDef<any, any>}
        />
      ),
    },
    {
      label: `Menunggu Diisi (${meta?.additionalMeta?.incomplete ?? 0})`,
      content: (
        <ListingClientReport
          showSecondFilter
          refRefetch={refRefetchWaiting}
          fetchPath={ADMIN_CLIENT_REPORT_API}
          pageFilter={pageFilter}
          actions={['reminder']}
          rangeDate={rangeDate}
          columns={columns as unknown as ColumnDef<any, any>}
        />
      ),
    },
  ]

  return (
    <>
      <HeaderContent title="Klien Report" handleDatePicker={() => {}} />
      <Card className="border-0 p-0 xs:p-0 sm:border sm:p-6">
        <TabList
          className="sticky top-navbar z-30 bg-white"
          onClickTabs={(index) => {
            setActiveTabIndex(index)
            if (index === 0) {
              setPageFilter([])
            } else {
              setPageFilter([{ key: 'anamnesis', operator: 'equal', value: null }])
            }
          }}
          activeTabIndex={activeTabIndex}
        >
          {CounsellingData.map((counselling, index) => {
            return (
              <TabItem key={index} label={counselling.label}>
                {counselling.content}
              </TabItem>
            )
          })}
        </TabList>
      </Card>
    </>
  )
}
