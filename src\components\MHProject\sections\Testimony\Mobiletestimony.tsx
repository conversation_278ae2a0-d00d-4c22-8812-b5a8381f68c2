'use client'
import { Swiper, SwiperSlide } from 'swiper/react'
import { FreeMode, Navigation } from 'swiper/modules'
import 'swiper/css'
import 'swiper/css/pagination'
import { TestimonyResponse } from './Testimony'
import { TestimonyCardItem } from './TestimonyCardItem'

export const MobileTestimony = ({ testimonyList }: { testimonyList: TestimonyResponse[] }) => {
  return (
    <div className="w-screen max-w-[1120px] relative flex flex-col gap-y-6 md:gap-y-10">
      <div className="relative w-full">
        <Swiper
          slidesPerView={'auto'}
          navigation
          spaceBetween={16}
          freeMode={true}
          pagination={{
            clickable: true,
          }}
          modules={[FreeMode, Navigation]}
          className="mySwiperpsychologist"
        >
          {testimonyList.length
            ? testimonyList.map((item: TestimonyResponse) => (
                <SwiperSlide className="first:pl-4 last:pr-4" key={item.id} style={{ width: 'fit-content' }}>
                  <TestimonyCardItem {...item} />
                </SwiperSlide>
              ))
            : null}
        </Swiper>
      </div>
    </div>
  )
}
