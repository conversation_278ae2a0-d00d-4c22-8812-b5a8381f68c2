import { profileService } from '@/services/profile.service'
import { useQuery } from '@tanstack/react-query'

export const useGetInformationCategory = (id: string) => {
  return useQuery({
    queryKey: ['PageAllInformationhelpCenter', { id }],
    queryFn: () => {
      return profileService
        .getPageInformationByCategory(id)
        .then((response) => response)
        .catch(() => {
          return null
        })
    },
  })
}
