'use client'

import OnboardingSteps from './presentational/OnboardingSteps'
import CounselingCounterStatus from './presentational/CounselingCounterStatus'
import ActiveBalance from './presentational/ActiveBalance'
import ShareSocialMedia from './presentational/ShareSocialMedia'
import WeeklySchedule from './presentational/WeeklySchedule'
import Faq from './presentational/Faq'
import { AppModal } from '@/components/_common/Modal/AppModal'
import { useState } from 'react'
import PosterShare from './posterShare'
import { useSelector } from '@/store'
import { useGetOnboardingSteps } from './hook/useGetOnboardingSteps.hook'

const PsychologistDashboard = () => {
  const { data: OnboardingStepsData, refetch } = useGetOnboardingSteps()
  const { nickname, userIdentity } = useSelector((state) => state.PsychologistProfile)
  const [isOpenModal, setIsOpenModal] = useState<boolean>(false)
  const handleClickAccountInformation = () => {
    refetch()
    setIsOpenModal((prevIsOpenModal) => !prevIsOpenModal)
  }
  const credentialName = nickname ?? userIdentity?.email
  return (
    <>
      <h1 className="text-[#222222] text-[20px] md:text-[32px] font-bold md:mb-0 p-4 md:p-6 2xl:p-8">
        Salam sehat mental, <span className="block md:inline"></span>Kak {credentialName}!
      </h1>
      <section className="px-4 lg:px-6">
        <div className="flex flex-col md:flex-row gap-6 w-full pt-0">
          <div className="flex flex-col gap-6 w-full md:w-3/5 order-2 md:order-1 lg:p-0">
            <OnboardingSteps data={OnboardingStepsData} />
            <CounselingCounterStatus />
            <WeeklySchedule handleClickAccountInformation={handleClickAccountInformation} />
            <Faq />
          </div>
          <div className="flex flex-col gap-6 w-full md:w-2/5 order-1 md:order-2">
            {/* mobile */}
            <div className="md:hidden flex items-start gap-4 overflow-x-scroll">
              <div className="flex">
                <ActiveBalance />
              </div>
              <div className="flex">
                <ShareSocialMedia handleClickAccountInformation={handleClickAccountInformation} />
              </div>
            </div>
            {/* desktop */}
            <div className="hidden md:flex flex-col gap-6">
              <ActiveBalance />
              <ShareSocialMedia handleClickAccountInformation={handleClickAccountInformation} />
            </div>
          </div>
        </div>
      </section>
      {/* modal */}
      <AppModal
        className={`w-full`}
        open={isOpenModal}
        onClose={() => {
          handleClickAccountInformation()
        }}
        title={'Share akun ke social media'}
        showOverlay={true}
      >
        <PosterShare />
      </AppModal>
    </>
  )
}

export default PsychologistDashboard
