import Image from 'next/image'

type OurApproachesProps = {
  title: string
  subtitle: string
  content: any[]
}

export const OurApproaches = ({ title, subtitle, content }: OurApproachesProps) => {
  return (
    <div className="flex flex-col items-center gap-y-10 md:gap-y-15 pb-10 md:pb-15 px-4 md:px-0">
      <div className="flex flex-col items-center gap-y-4">
        <span className="text-subheading-md md:text-[38px] md:leading-[42px] text-gray-400 font-bold text-center">
          {title}
        </span>
        <span className="text-body-lg md:text-body-lg text-gray-300 font-medium text-center">{subtitle}</span>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 w-full">
        {content.length &&
          content.map((item, id) => {
            return (
              <div key={item.id} className={`flex flex-col items-center gap-y-4`}>
                <div className="relative h-[164px] w-[164px] flex justify-center">
                  <Image
                    src={item.image}
                    fill
                    alt="logo-mh"
                    style={{
                      objectFit: 'contain',
                      objectPosition: 'center',
                    }}
                  />
                </div>
                <span className="text-subheading-md md:text-heading-sm font-bold text-gray-400 text-center">
                  {item.label}
                </span>
                <span className="text-body-md md:text-body-lg font-medium text-gray-300 text-center">
                  {item.content}
                </span>
              </div>
            )
          })}
      </div>
    </div>
  )
}
