'use client'

import { ColumnDef } from '@tanstack/react-table'
import { UserPhoto } from '../../UserPhoto/UserPhoto'
import { AppMediumText } from '@/components/_common/ui'
import { Status } from '@/components/_common/Status/Status'
import { PsychologistProfile } from '@/interfaces/profile-service'

export type PsychologistProps = PsychologistProfile

export const columns: ColumnDef<PsychologistProps>[] = [
  {
    accessorKey: 'userIdentity',
    header: 'Pengguna',
    cell: ({ cell, row }) => {
      return (
        <div className="font-bold hover:text-main-100">
          <UserPhoto
            photo={cell.row.original['profilePhoto']}
            title={cell.row.original['fullName']}
            subTitle={
              cell.row.original['field']?.length
                ? cell.row.original['field']?.map((val: any) => val.name).join(', ')
                : 'Tidak Tersedia'
            }
          />
        </div>
      )
    },
  },
  // {
  //   accessorKey: 'balance',
  //   header: 'Saldo',
  //   cell: ({ cell, row }) => {
  //     return (
  //       <AppMediumText>
  //         {Intl.NumberFormat('id-ID', {
  //           style: 'currency',
  //           currency: 'IDR',
  //           maximumFractionDigits: 0,
  //           minimumFractionDigits: 0,
  //         }).format(Number(row.getValue('balance')))}
  //       </AppMediumText>
  //     )
  //   },
  // },
  {
    accessorKey: 'counselingCount',
    header: 'Konseling',
    cell: ({ row }) => {
      return <AppMediumText>{row.getValue('counselingCount')}</AppMediumText>
    },
  },
  {
    accessorKey: 'isActive',
    header: 'Status',
    cell: ({ cell, row }) => {
      return (
        <Status
          variant={cell.row.original?.['userIdentity']?.['isActive'] ? 'success' : 'disable'}
          label={cell.row.original?.['userIdentity']?.['isActive'] ? 'Aktif' : 'Nonaktif'}
        />
      )
    },
  },
]
