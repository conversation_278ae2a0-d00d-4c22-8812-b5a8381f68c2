import { Swiper, SwiperSlide } from 'swiper/react'
import { FreeMode, Navigation } from 'swiper/modules'
import 'swiper/css'
import 'swiper/css/pagination'
import { FeatureCard } from '@/components/home/<USER>/MentalHealingFeature/FeatureCard'

export const MobileTypeCounselling = ({ content }: { content: any[] }) => {
  return (
    <div className="max-w-screen w-full xl:max-w-[1120px] relative flex md:hidden flex-col gap-y-10 z-1 justify-center items-center">
      <div className="relative w-full">
        <Swiper
          slidesPerView={'auto'}
          navigation
          spaceBetween={8}
          freeMode={true}
          pagination={{
            clickable: true,
          }}
          modules={[FreeMode, Navigation]}
          className="mySwiperpsychologist"
        >
          {content.map((slide: any, id: number) => (
            <SwiperSlide
              key={slide.id}
              className="md:first:pl-0 md:first:pr-0 first:pl-4 last:pr-4"
              style={{
                width: 'max-content',
              }}
            >
              <div className="w-[328px] lg:w-[400px]">
                <FeatureCard isTypeCounselling {...slide} />
              </div>
            </SwiperSlide>
          ))}
        </Swiper>
      </div>
    </div>
  )
}
