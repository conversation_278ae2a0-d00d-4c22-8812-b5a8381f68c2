'use client'

import { useState } from 'react'
import * as yup from 'yup'
import { useRouter } from 'next/navigation'
import Breadcrumb from '@/components/breadcrumbs/Breadcrumbs'
import { HeaderContent } from '../../HeaderContent'
import { AdminForm } from './AdminForm'
import { useForm } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import Translation from '@/constans/Translation'
import { RemoveIndex } from '@/utils/type'
import { AppModal } from '@/components/_common/Modal/AppModal'
import ButtonPrimary from '@/components/_common/ButtonPrimary'
import { adminService } from '@/services/admin.service'
import { useToast } from '@/components/ui/use-toast'

const validationSchema = yup.object().shape({
  fullName: yup.string().required(Translation.RequiredFullName),
  nickname: yup.string().required(Translation.RequiredNickName),
  phoneNumber: yup.string().required(Translation.RequiredPhoneNumber),
  email: yup.string().required(Translation.RequiredEmail).email(Translation.ValidEmail),
  password: yup
    .string()
    .min(6, Translation.PasswordMinChar)
    .matches(/[A-Z]/, Translation.RequiredUppercase)
    .matches(/[a-z]/, Translation.RequiredLowercase)
    .matches(/\d/, Translation.RequiredNumber)
    .required(Translation.PasswordRequired),
})

export type AdminValuesType = RemoveIndex<yup.InferType<typeof validationSchema>>

export const AdminMain = () => {
  const router = useRouter()
  const { toast } = useToast()
  const [isOpenModal, setIsOpenModal] = useState<boolean>(false)
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const {
    register,
    handleSubmit,
    getValues,
    setValue,
    reset,
    formState: { errors, isLoading: isLoadingForm, isSubmitting },
  } = useForm({
    resolver: yupResolver(validationSchema),
    defaultValues: {
      email: '',
      password: '',
      fullName: '',
      nickname: '',
      phoneNumber: '',
    },
  })

  async function onSubmit(data: AdminValuesType) {
    setIsLoading(true)
    try {
      const formData = new FormData()

      Object.entries(data).forEach(([key, value]) => {
        formData.append(key, value.toString())
      })

      const createdPsikolog = await adminService.postAdmin(formData)

      if (!createdPsikolog) {
        throw new Error('API response is empty')
      }

      toast({
        variant: 'success',
        title: 'Submit data berhasil. Terimakasih',
      })

      reset()
      router.replace('/admin/user')
    } catch (error) {
      console.error('Error during submission:', error)
      toast({
        variant: 'danger',
        title: 'Terjadi kesalahan saat menyimpan data, silahkan ulangi beberapa saat lagi.',
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleModalCancel = () => {
    setIsOpenModal((prevIsOpenModal) => !prevIsOpenModal)
  }

  return (
    <>
      <Breadcrumb containerClasses="pb-2" pageName="Tambah Admin" />
      <HeaderContent title="Tambah Admin" />
      <AdminForm
        register={register}
        getValues={getValues}
        setValue={setValue}
        errors={errors}
        onSubmit={() => handleSubmit(onSubmit)()}
        onCancel={() => handleModalCancel()}
        isLoading={isSubmitting || isLoading || isLoadingForm}
        isDisabled={isSubmitting || isLoading || isLoadingForm}
      />

      <AppModal
        className={`w-full transition-all duration-500 ease-in-out ${isOpenModal ? 'opacity-100 scale-100' : 'opacity-0 scale-0'}`}
        open={isOpenModal}
        onClose={() => {
          handleModalCancel()
        }}
        title={'Batalkan proses tambah Admin?'}
        showOverlay={true}
      >
        <div className="flex flex-col bg-white gap-4">
          <div className="flex flex-col items-end gap-4">
            <div className="flex flex-col justify-center items-center gap-4">
              <p className="text-[16px] text-[#222222]">
                Data yang Anda sudah masukkan tidak akan tersimpan.
              </p>
            </div>
            <div className="flex gap-4 items-end justify-end mt-6 w-full md:w-[70%]">
              <ButtonPrimary
                className="min-w-[140px]"
                size="base"
                variant="outlined"
                color="gray"
                onClick={() => {
                  handleModalCancel()
                }}
              >
                Tidak
              </ButtonPrimary>
              <ButtonPrimary
                className="min-w-[140px]"
                size="base"
                variant="contained"
                onClick={() => router.push('/admin/user')}
              >
                Iya
              </ButtonPrimary>
            </div>
          </div>
        </div>
      </AppModal>
    </>
  )
}
