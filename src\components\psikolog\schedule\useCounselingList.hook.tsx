import { useToast } from '@/components/ui/use-toast'
import {
  CancelledStatus,
  CompletedStatus,
  PaymentStatus,
  UpcomingStatus,
  WaitConfirmationsStatus,
} from '@/constans/StaticOptions'
import { counsellingService } from '@/services/counselling.service'
import { ScheduleListProps } from '@/store/psikolog/schedule.reducer'
import { useQuery } from '@tanstack/react-query'

export const useCounselingList = (category: PaymentStatus[]) => {
  const { toast } = useToast()
  return useQuery({
    queryKey: ['CounselingPsychologistList', { category }],
    queryFn: () =>
      counsellingService
        .getCounsellingByCategory(category)
        .then((response) => {
          let counselingList: {
            upcomingCounselling: ScheduleListProps[]
            waitConfirmationCounselling: ScheduleListProps[]
            completeCounselling: ScheduleListProps[]
            cancelledCounselling: ScheduleListProps[]
          } = {
            upcomingCounselling: [],
            waitConfirmationCounselling: [],
            completeCounselling: [],
            cancelledCounselling: [],
          }
          if (JSON.stringify(CompletedStatus) === JSON.stringify(category)) {
            counselingList.completeCounselling = response.data ?? []
          }
          if (JSON.stringify(category) === JSON.stringify(UpcomingStatus)) {
            counselingList.upcomingCounselling = response.data ?? []
          }
          if (JSON.stringify(category) === JSON.stringify(CancelledStatus)) {
            counselingList.cancelledCounselling = response.data ?? []
          }
          if (JSON.stringify(category) === JSON.stringify(WaitConfirmationsStatus)) {
            counselingList.waitConfirmationCounselling = response.data ?? []
          }

          const additionalMeta = response?.additionalMeta
          const countPercategory = {
            total: response.meta.total,
            totalUpcoming: additionalMeta?.upcoming,
            totalCompleted: additionalMeta?.completed,
            totalCancelled: additionalMeta?.cancelled,
            totalWaitConfirmation: additionalMeta?.waiting,
          }
          return {
            counselingList,
            meta: countPercategory,
          }
        })
        .catch((error) => {
          toast({
            title: 'Gagal',
            description: 'Terjadi masalah dengan server, Silahkan hubungi Admin',
            variant: 'danger',
          })
        }),
  })
}
