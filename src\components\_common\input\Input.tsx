'use client'
import React, { forwardRef, useEffect, useId, useRef, useState } from 'react'
import AppInputInterface, { OptionTypeProps } from '@/interfaces/components/_common/input'
import { IIcons, SVGIcons } from '../icon'
import { twMerge } from 'tailwind-merge'
import EyeIcon from '@/assets/icons/eye.svg'
import EyeSlashIcon from '@/assets/icons/eye-dash.svg'
import style from './style.module.scss'

const AppInput = forwardRef<HTMLInputElement, AppInputInterface>(function AppInput(
  {
    type,
    placeholder,
    options,
    rows,
    width,
    className,
    inputClass,
    onChange,
    onKeyDown,
    value,
    name,
    errorMsg,
    maxChar,
    prefixIcon,
    suffixIcon,
    label,
    note,
    onForgotPassword,
    getLabel,
    getKey,
  }: AppInputInterface,
  ref
) {
  const [showPassword, setShowPassword] = useState<boolean>(false)
  const [counter, setCounter] = useState<number>(0)
  const id = useId()

  const toggleShowpassword = () => {
    setShowPassword((prev) => !prev)
  }

  const onChangeInput = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    if (maxChar) {
      e.target.value = e.target.value.substring(0, maxChar)
    }
    onChange(
      e as React.ChangeEvent<HTMLInputElement> &
        React.ChangeEvent<HTMLSelectElement> &
        React.ChangeEvent<HTMLTextAreaElement>
    )
  }

  useEffect(() => {
    maxChar && setCounter(value ? value.length : 0)
  }, [maxChar, value])

  const getLabelOpt = (option: any) => {
    if (type === 'select') {
      return getLabel ? getLabel(option) : option.label || option
    }
  }
  const getValueOpt = (option: any) => {
    if (type === 'select') {
      return getKey ? getKey(option) : option.value || option
    }
  }

  switch (type) {
    case 'text':
    case 'number':
    case 'password':
    case 'date':
    case 'email':
      return (
        <div
          className={twMerge(`form-container ${label ? 'grid gap-[8px]' : ''} ${className ? className : ''}`)}
        >
          {onForgotPassword ? (
            <div className="flex justify-between items-center">
              <label htmlFor={id} className="text-body-md font-bold text-gray-400">
                {label}
              </label>
              <label
                onClick={() => onForgotPassword()}
                htmlFor={id}
                className="text-caption-md font-medium cursor-pointer text-[#039EE9]"
              >
                Lupa password?
              </label>
            </div>
          ) : (
            <label htmlFor={id} className="text-body-md font-bold text-gray-400">
              {label}
            </label>
          )}
          <div className={`relative`}>
            <div className="flex flex-col items-start input-form w-full">
              {prefixIcon ? (
                <SVGIcons
                  className="absolute left-4 top-1/2 -translate-y-1/2 z-20 stroke-gray-200"
                  name={IIcons.Search}
                />
              ) : null}

              <input
                ref={ref}
                style={{
                  minWidth: `${width}px`,
                  borderColor: errorMsg ? 'rgb(239 68 68)' : 'rgb(235 235 235)',
                }}
                id={id}
                type={type === 'password' ? (showPassword ? 'text' : 'password') : type}
                placeholder={placeholder}
                // maxLength={type === 'password' ? 14 : undefined}
                // minLength={type === 'password' ? 8 : undefined}
                onChange={onChangeInput}
                onKeyDown={onKeyDown}
                value={value}
                name={name}
                className={twMerge(
                  `relative w-full bg-white z-10 py-3 pr-2 pl-4 border border-line-200 rounded-2xl text-gray-200 placeholder:text-gray-200 
                  focus:border-transparent focus:outline-none focus:ring focus:ring-main-100 focus:ring-opacity-40
                  ${type === 'password' || suffixIcon ? 'pr-12' : ''} ${prefixIcon ? 'pl-12' : ''}`
                )}
              />

              {suffixIcon ? (
                <SVGIcons
                  className="absolute right-2 top-1/2 -translate-y-1/2 z-10"
                  name={suffixIcon as IIcons}
                />
              ) : type === 'password' ? (
                <button type="button" onClick={toggleShowpassword}>
                  <span className="absolute right-2 top-1/2 -translate-y-1/2 z-10">
                    {showPassword ? <EyeSlashIcon /> : <EyeIcon />}
                  </span>
                </button>
              ) : null}

              {maxChar && (
                <div data-max className={`text-xs absolute top-2 right-3 z-10 hidden`}>
                  {counter} / {maxChar}
                </div>
              )}
            </div>
          </div>
          {errorMsg && <div className="text-xs text-red-500 text-left mt-1">{errorMsg}</div>}
          {note ? (
            <span
              className="text-caption-md font-medium text-gray-300"
              dangerouslySetInnerHTML={{ __html: note }}
            ></span>
          ) : null}
        </div>
      )
    case 'select':
      return (
        <div
          style={{ width: `${width}px` }}
          className={`form-container grid gap-y-2 ${className ? className : ''}`}
        >
          {label && (
            <label htmlFor={id} className="text-body-md font-bold text-gray-400">
              {label}
            </label>
          )}
          <div className="relative">
            <div className="w-full flex flex-col items-start input-form">
              <select
                style={{ width: `${width}px` }}
                value={value}
                onChange={onChange}
                className={twMerge(
                  `appearance-none w-full text-gray-200 py-3 px-3 pr-8 pl-4 border border-line-200 rounded-2xl focus:border-transparent focus:outline-none focus:ring focus:ring-main-100 focus:ring-opacity-40 ${inputClass}`
                )}
                id={id}
                name={name}
              >
                <option>Pilih {placeholder}</option>
                {options?.map((option: OptionTypeProps<string, any>, index: number) => {
                  return (
                    <option
                      className={`hover:bg-main-100 ${style['options']}`}
                      key={`input-primary-${index}`}
                      value={getValueOpt(option)}
                    >
                      {getLabelOpt(option)}
                    </option>
                  )
                })}
              </select>
              <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                <SVGIcons name={IIcons.ArrowDown} />
              </div>
            </div>
          </div>
          {errorMsg && <div className="text-xs text-red-500 text-left mt-1">{errorMsg}</div>}
        </div>
      )
    case 'textarea':
      return (
        <div className={`form-container ${className ? className : ''}`}>
          <label htmlFor={id} className="text-body-md font-bold text-gray-400">
            {label}
          </label>
          <div className="relative">
            <div className="flex flex-col items-start input-form w-full">
              <textarea
                onChange={onChangeInput}
                onKeyDown={onKeyDown}
                placeholder={placeholder}
                value={value ? value : ''}
                style={{ minWidth: `${width}px` }}
                rows={rows}
                id={id}
                name={name}
                className={`z-10 pt-5 pb-3 pr-2 pl-2 border rounded-md w-full focus:outline-main-100 placeholder-gray-100`}
              ></textarea>
              {/* <label htmlFor={`${id}`} className={`absolute top-10 left-1.5 z-20 input-label`}>
                <span className={`absolute bottom-0 left-1`}>{placeholder}</span>
              </label> */}
              {maxChar && (
                <div data-max className={`text-xs absolute bottom-2 right-3 z-10`}>
                  {counter} / {maxChar}
                </div>
              )}
            </div>
          </div>
          {errorMsg && <div className="text-xs text-red-500 text-left mt-1">{errorMsg}</div>}
          <span className="text-caption-md font-medium text-gray-300">{note}</span>
        </div>
      )
    default:
      return null
  }
})

AppInput.displayName = 'AppInput'
export default AppInput
