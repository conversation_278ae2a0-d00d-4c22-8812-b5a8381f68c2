export const Hidden = ({
  mdUp,
  mdDown,
  xsUp,
  xsDown,
  children,
}: {
  mdUp: boolean
  xsUp: boolean
  mdDown: boolean
  xsDown: boolean
  children: React.ReactNode
}) => {
  let className = ''
  if (mdUp) {
    className = 'md:hidden'
  }
  if (xsUp) {
    className = 'xs:hidden'
  }
  if (mdDown) {
    className = 'md:block'
  }
  if (xsDown) {
    className = 'xs:block'
  }
  if (mdUp) {
  }
  return <div className=""></div>
}
