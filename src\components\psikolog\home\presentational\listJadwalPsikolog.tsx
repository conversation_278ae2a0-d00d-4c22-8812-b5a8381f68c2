import { ScheduleListProps } from '@/store/psikolog/schedule.reducer'
import { formatStringToDate, formatStringToDay, formatStringToTimeOutput } from '@/utils/displayDate'

export default function ListJadwalPsikolog({ client, method, startTime, endTime }: ScheduleListProps) {
  const fullName = client?.fullName || ''
  const timeLabel = `${formatStringToTimeOutput({ date: startTime })} - ${formatStringToTimeOutput({ date: endTime })}`
  const dateLabelCard = formatStringToDate(startTime, true)
  const dayLabelCard = formatStringToDay(startTime, true)
  return (
    <>
      <div className="flex items-center gap-[15px]">
        {/* img profile */}
        <div className="w-[46px] h-[46px] md:w-[56px] md:h-[56px] rounded-[15px] flex flex-col items-center justify-center bg-[#E7F7FF] p-4">
          <span className="text-[#222222] text-[14px] font-bold">{dateLabelCard}</span>
          <span className="text-[#535353] text-[12px] uppercase">{dayLabelCard}</span>
        </div>
        {/* isi */}
        <div className="flex flex-col gap-[6px]">
          <p className="font-bold text-[14px] text-[#222222]">
            Konseling dengan <span className="text-[#039EE9]">{fullName}</span>
          </p>
          <div className="flex items-center gap-[12px]">
            <p className="text-[#535353] text-[14px]">{timeLabel}</p>
            <div className="w-[6px] h-[6px] bg-[#EBEBEB] rounded-full"></div>
            <p className="text-[#535353] text-[14px]">{method === 'Call' ? 'Call' : 'Video Call'}</p>
          </div>
        </div>
      </div>
    </>
  )
}
