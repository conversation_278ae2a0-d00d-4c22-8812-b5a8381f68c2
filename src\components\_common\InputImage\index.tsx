import Avatar, { ImageProps } from '@/components/navbar/Avatar'
import ButtonPrimary from '../ButtonPrimary'
import { IIcons } from '../icon'
import { forwardRef, useEffect, useState } from 'react'
import { StaticImport } from 'next/dist/shared/lib/get-img-props'

export type InputFileProps = {
  accept: string
  onChange: (e: File) => void
  onSubmit?: () => void
  inputRef: any
  className?: string
  icon?: IIcons
  maxFileSizeMb?: number
  name: string
  label: string
  preview?: string
  height?: number
  width?: number
  objectPosition?: string
  classinfo?: string
}

export const InputImage = forwardRef(function InputImage({
  onChange,
  onSubmit,
  icon,
  accept,
  inputRef,
  className = '',
  maxFileSizeMb = 1,
  label,
  name,
  preview = '',
  height = 72,
  width = 72,
  objectPosition,
  classinfo,
}: InputFileProps) {
  const styleInfo = classinfo ?? 'w-full text-caption-md font-medium text-gray-400'
  const [error, setError] = useState<string>('')

  const [fileName, setFileName] = useState<string | null>(null)
  const [imagePreview, setImagePreview] = useState<string | ArrayBuffer | null | undefined>(preview)

  function previewFile(e: any) {
    const reader = new FileReader()

    const selectedFile = e.target.files[0]
    if (selectedFile) {
      if (maxFileSizeMb && selectedFile.size > maxFileSizeMb * 1e6) {
        setError(`Upload failed. File melebihi kapasitas maksimal (${maxFileSizeMb}MB).`)
        return
      } else {
        setError('')
        reader.readAsDataURL(selectedFile)
      }
    }

    reader.onload = (readerEvent) => {
      if (selectedFile.type.includes('image')) {
        setImagePreview(readerEvent.target?.result || null)
      }
      setFileName(selectedFile.name || null)
    }
  }

  function clearFiles() {
    setError('')
    setFileName(null)
    setImagePreview(null)
  }

  useEffect(() => {
    setImagePreview(preview)
  }, [preview])

  const ImageDisplay = imagePreview != null ? imagePreview : '/images/empty-image.png'
  return (
    <div className="grid gap-y-2">
      <label className="text-body-md font-bold text-gray-400">{label}</label>
      <div className="flex flex-col xs:flex-col md:flex-row items-start xs:items-start md:items-center gap-4">
        <label
          className={`cursor-pointer flex items-center justify-center w-full md:w-auto ${
            icon ? undefined : 'bg-grey-default'
          } bg-opacity-50 ${className}`}
          htmlFor={name}
        >
          <div className="w-full md:w-auto">
            <Avatar
              image={ImageDisplay as ImageProps}
              height={height}
              width={width}
              scaleDown={!imagePreview}
              objectPosition={objectPosition}
            />
          </div>
          <input
            type="file"
            accept={accept}
            className="absolute w-0 h-0 hidden"
            name={name}
            onChange={(e) => {
              previewFile(e)
              if (e.target.files?.length) {
                onChange && onChange(e.target.files[0])
              }
            }}
            ref={inputRef}
          />
        </label>
        <div className="flex flex-col items-start gap-2">
          {fileName && <span className="w-full text-caption-md font-bold text-gray-400">{fileName}</span>}
          <span className={styleInfo}>Format dalam .jpg .png, maksimal {maxFileSizeMb}MB.</span>
          {fileName ? (
            <div className="flex gap-2">
              {onSubmit && (
                <ButtonPrimary variant="outlined" size="xs" onClick={() => onSubmit && onSubmit()}>
                  Simpan
                </ButtonPrimary>
              )}
              <ButtonPrimary variant="outlined" size="xs" onClick={() => inputRef.current.click()}>
                Ubah Foto
              </ButtonPrimary>
              <ButtonPrimary variant="outlined" size="xs" color="gray" onClick={() => clearFiles()}>
                Hapus
              </ButtonPrimary>
            </div>
          ) : (
            <ButtonPrimary variant="outlined" size="xs" onClick={() => inputRef.current.click()}>
              Pilih Foto
            </ButtonPrimary>
          )}
          {error ? <span className="text-red-500 text-xs">{error}</span> : null}
        </div>
      </div>
    </div>
  )
})
