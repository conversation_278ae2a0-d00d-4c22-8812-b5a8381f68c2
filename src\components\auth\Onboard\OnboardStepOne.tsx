import AppInput from '@/components/_common/input/Input'
import { RadioInput } from '@/components/_common/RadioInput/RadioInput'
import { DatePicker } from '@/components/ui/DatePicker'
import { UseFormGetValues, UseFormRegister, UseFormReturn, UseFormSetValue } from 'react-hook-form'
import { GenderOptions } from '@/constans/onboardOptions'

export type OnboardStepOneProps = {
  register: UseFormRegister<any>
  getValues: UseFormGetValues<any>
  setValue: UseFormSetValue<any>
  errors: UseFormReturn['formState']['errors']
  onSubmit: () => void
}
export const OnboardStepOne = ({ register, getValues, setValue, errors }: OnboardStepOneProps) => {
  return (
    <>
      <AppInput
        {...register('fullName')}
        className="pt-0"
        label="Nama <PERSON>"
        type="text"
        name="fullName"
        errorMsg={!!errors.fullName ? String(errors.fullName.message) : undefined}
      />
      <AppInput
        {...register('nickname')}
        className="pt-0"
        label="Nama <PERSON>"
        type="text"
        name="nickname"
        errorMsg={!!errors.nickname ? String(errors.nickname.message) : undefined}
      />
      <AppInput
        className="pt-0"
        label="No. Handphone"
        type="text"
        {...register('phoneNumber')}
        name="phoneNumber"
        errorMsg={!!errors.phoneNumber ? String(errors.phoneNumber.message) : undefined}
      />
      <AppInput
        {...register('email')}
        className="pt-0"
        label="Email"
        type="text"
        name="email"
        errorMsg={!!errors.email ? String(errors.email.message) : undefined}
      />
      <AppInput
        className="pt-0"
        label="Buat Password"
        type="password"
        {...register('password')}
        name="password"
        errorMsg={!!errors.password ? String(errors.password.message) : undefined}
      />
      <AppInput
        {...register('rePassword')}
        className="pt-0"
        label="Ulangi Password"
        type="password"
        name="rePassword"
        errorMsg={!!errors.rePassword ? String(errors.rePassword.message) : undefined}
      />

      <RadioInput
        options={GenderOptions}
        name={'gender'}
        value={getValues('gender')!}
        label="Jenis Kelamin"
        errorMsg={!!errors.gender ? String(errors.gender.message) : undefined}
        onChange={(val) => {
          setValue('gender', val, { shouldValidate: true })
        }}
      />

      <DatePicker
        placeholder=""
        className="py-3 h-[50px] w-full"
        date={getValues('birthDate') ? new Date(getValues('birthDate')!) : undefined}
        label="Tanggal Lahir"
        errorMsg={!!errors.birthDate ? String(errors.birthDate.message) : undefined}
        onSelect={(date) => setValue('birthDate', date, { shouldValidate: true })}
        captionLayout="dropdown"
        fromYear={new Date().getFullYear() - 100}
        toYear={new Date().getFullYear()}
      />
    </>
  )
}
