import Accordion from '@/components/_common/Accordion/accordion'
import { useGetFaq } from '../hook/useGetFaq.hook'
import { NoDataFound } from '@/components/_common/NoData/NoDataFound'

export default function Faq() {
  const { data: dataFAQ } = useGetFaq('DashboardPsychologist')
  return (
    <>
      <div className="flex flex-col gap-3 bg-white border border-[#EBEBEB] rounded-xl p-6">
        <div className="py-[12px] border-b border-[#EBEBEB]">
          <p className="font-bold text-[16px] md:text-[18px] text-[#242424]">Yang <PERSON></p>
        </div>
        <div className="flex flex-col gap-[16px]">
          {dataFAQ?.length ? (
            dataFAQ.map((item: any) => {
              return <Accordion key={item.id} header={item?.question} content={item?.answer} />
            })
          ) : (
            <NoDataFound />
          )}
        </div>
      </div>
    </>
  )
}
