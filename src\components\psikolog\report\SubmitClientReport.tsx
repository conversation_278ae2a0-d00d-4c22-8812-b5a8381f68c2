'use client'
import { ListInformation } from '@/components/_common/ListInformation'
import { HeaderContent } from '@/components/admin/HeaderContent'
import { useRouter, useSearchParams } from 'next/navigation'
import { IIcons } from '@/components/_common/icon'
import AppInput from '@/components/_common/input/Input'
import ButtonPrimary from '@/components/_common/ButtonPrimary'
import { Switch } from '@/components/ui/switch'
import { useEffect, useState } from 'react'
import { Separator } from '@/components/ui/separator'
import { useToast } from '@/components/ui/use-toast'
import { Controller, useForm } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import * as yup from 'yup'
import { MOMENT_INPUT_DATE_FORMAT } from '@/constans/date'
import { clientReportService } from '@/services/clientReport.service'
import { useGetCounselingDetails } from '@/hooks/useGetCounselingDetails.hook'
import { useGetScheduleByDate } from '@/hooks/useGetScheduleByDate.hook'
import moment from 'moment'
import useGetTimezoneLabel from '@/hooks/useGetTimezone.hook'
import { formatStringToFulldateOutput, formatStringToStartEndTimeOutput } from '@/utils/displayDate'
import useGetPsychologistTimezone from '@/hooks/useGetPsychologistTimezone.hook'
import { WeeklyCalendarPickerCustom } from '@/components/_common/WeeklyDatePicker/WeeklyCalendarCustom'
import { useGetPsychologistDateAvailability } from '@/hooks/useGetPsychologistDateAvailability.hook'

const validationSchema = yup.object().shape({
  anamnesis: yup.string().max(3000, 'Maksimal 3000 karakter').nullable(),
  intervention: yup.string().max(3000, 'Maksimal 3000 karakter').nullable(),
  notesForClient: yup.string().max(3000, 'Maksimal 3000 karakter').nullable(),
  task: yup.string().max(3000, 'Maksimal 3000 karakter').nullable(),
})

export const SubmitClientReportComponent = () => {
  const { toast } = useToast()
  const router = useRouter()
  const searchParam = useSearchParams()
  const clientName = searchParam.get('name')
  const counselingId = searchParam.get('counselingId')
  const [date, setDate] = useState<Date | undefined>(new Date())
  const [isChecked, setIsChecked] = useState<boolean>(false)
  const [selectedTime, setSelectedTime] = useState<string | null>('')
  const [selectedDuration, setSelectedDuration] = useState<number>(60)
  const [isEdit, setIsEdit] = useState<boolean>(false)
  const [isLoading, setIsLoading] = useState<boolean>(false)

  const {
    register,
    handleSubmit,
    getValues,
    setValue,
    reset,
    control,
    formState: { errors, isLoading: isLoadingForm, isSubmitting },
  } = useForm({
    resolver: yupResolver(validationSchema),
    defaultValues: {
      anamnesis: '',
      intervention: '',
      notesForClient: '',
      task: '',
    },
  })

  const { data: counselingDetails } = useGetCounselingDetails(counselingId || '')
  const { data: availableDates } = useGetPsychologistDateAvailability(counselingDetails?.psychologistId || '')
  const { data: dataSchedule } = useGetScheduleByDate(
    date
      ? moment(date).format(MOMENT_INPUT_DATE_FORMAT)
      : moment().add(1, 'days').format(MOMENT_INPUT_DATE_FORMAT),
    selectedDuration
  )

  useEffect(() => {
    if (counselingDetails?.clientReport) {
      reset({
        anamnesis: counselingDetails?.clientReport?.anamnesis,
        intervention: counselingDetails?.clientReport?.intervention,
        notesForClient: counselingDetails?.clientReport?.notesForClient,
        task: counselingDetails?.clientReport?.task,
      })

      const nextDate = counselingDetails?.clientReport?.nextCounselingDate
        ? moment(counselingDetails?.clientReport?.nextCounselingDate).toDate()
        : moment().add(1, 'days').toDate()
      const nextTime = counselingDetails?.clientReport?.nextCounselingDate
        ? moment(counselingDetails?.clientReport?.nextCounselingDate).format('HH:mm')
        : null
      setIsEdit(true)
      setIsChecked(counselingDetails?.clientReport?.nextCounselingAnswer)
      setDate(nextDate)
      setSelectedTime(nextTime)
      setSelectedDuration(counselingDetails?.clientReport?.nextCounselingDuration ?? 60)
    }
  }, [counselingDetails?.clientReport, reset])

  async function onSubmit(data: any) {
    setIsLoading(true)
    try {
      const formData = new FormData()
      for (let val in data) {
        formData.append(val, data[val])
      }
      if (isChecked) {
        if (date && selectedTime && selectedDuration) {
          const payloadNextDate = moment(date).format(MOMENT_INPUT_DATE_FORMAT)
          const payloadNextDateTime = moment(`${payloadNextDate} ${selectedTime}`).toISOString()
          console.log(payloadNextDateTime)
          formData.append('nextCounselingAnswer', true as any)
          formData.append('nextCounselingDate', payloadNextDateTime as any)
          formData.append('nextCounselingDuration', selectedDuration as any)
        } else {
          toast({
            variant: 'danger',
            title: 'Lengkapi data jadwal sesi lanjutan terlebih dahulu.',
          })
          setIsLoading(false)
          return
        }
      } else {
        formData.append('nextCounselingAnswer', false as any)
        formData.append('nextCounselingDate', null as any)
        formData.append('nextCounselingDuration', null as any)
      }
      if (isEdit) {
        await clientReportService.updateClientReport(formData, counselingId || '')
        toast({
          variant: 'success',
          title: 'Klien report berhasil disimpan',
        })
        setTimeout(() => {
          setIsLoading(false)
          router.back()
        }, 1000)
      } else {
        await clientReportService.createClientReport(formData, counselingId || '')
        toast({
          variant: 'success',
          title: 'Klien report berhasil disimpan',
        })
        setTimeout(() => {
          setIsLoading(false)
          router.back()
        }, 1000)
      }
    } catch (error) {
      toast({
        variant: 'danger',
        title: 'Klien report gagal disimpan.',
      })
      setTimeout(() => {
        setIsLoading(false)
        router.back()
      }, 1000)
    }
  }
  const psychologistTimezone = useGetPsychologistTimezone()
  const timeZoneLabel = useGetTimezoneLabel()

  const dateLabel = formatStringToFulldateOutput(counselingDetails?.startTime)
  const timeLabel = formatStringToStartEndTimeOutput({
    date: counselingDetails?.startTime,
    duration: counselingDetails?.duration,
    timezone: psychologistTimezone,
    timeLabel: timeZoneLabel,
    isUTC: true,
  })

  return (
    <>
      <HeaderContent className="mb-1" title={`Tulis Klien Report untuk ${clientName}`} />
      <div className="flex flex-col md:flex-row gap-2 mb-4">
        <span>Sesi konseling: </span>
        <ListInformation
          className="border-b-0 py-0 ml-0 md:ml-2 gap-2"
          listItem={[
            { label: dateLabel, icon: IIcons.Calendar },
            { label: timeLabel, icon: IIcons.Time },
            {
              label: counselingDetails?.method === 'Call' ? 'Call' : 'Video Call',
              icon: counselingDetails?.method === 'Call' ? IIcons.Call : IIcons.VideoCall,
            },
          ]}
        />
      </div>
      <div className="grid grid-cols-3 grid-rows-1">
        <div className="grid col-span-3 lg:col-span-2 gap-y-4">
          <>
            <Controller
              control={control}
              render={({ field }) => (
                <AppInput {...field} label="Keluhan/Anamnesa Klien" rows={3} type="textarea" maxChar={3000} />
              )}
              name="anamnesis"
              defaultValue=""
            />
            <Controller
              control={control}
              render={({ field }) => (
                <AppInput {...field} label="Penanganan/Intervensi" rows={3} type="textarea" maxChar={3000} />
              )}
              name="intervention"
              defaultValue=""
            />
            <Controller
              control={control}
              render={({ field }) => (
                <AppInput
                  {...field}
                  note="Catatan ini akan ditampilkan ke Klien"
                  label="Catatan untuk Klien"
                  rows={3}
                  type="textarea"
                  maxChar={3000}
                />
              )}
              name="notesForClient"
              defaultValue=""
            />
            <Controller
              control={control}
              render={({ field }) => (
                <AppInput
                  {...field}
                  note="Tugas ini akan ditampilkan ke Klien"
                  label="Tugas yang Diberikan"
                  rows={3}
                  type="textarea"
                  maxChar={3000}
                />
              )}
              name="task"
              defaultValue=""
            />

            <div className={`form-container grid gap-[8px] border-t border-line-200 mt-2`}>
              <div className={`relative`}>
                <div className="flex flex-col items-start input-form w-full">
                  <div className="flex flex-row items-center justify-between py-2">
                    <div className="mr-4">
                      <Switch checked={isChecked} onCheckedChange={() => setIsChecked((prev) => !prev)} />
                    </div>
                    <div className="flex flex-col space-y-0.5">
                      <label className="text-body-lg font-bold text-gray-400">Jadwalkan sesi lanjutan</label>
                      <span className="text-caption-md font-medium text-gray-300">
                        Klien dapat langsung proses booking sesuai dengan jadwal rekomendasi Anda.
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            {isChecked && (
              <div className="grid gap-2">
                <div className={`form-container grid gap-[8px]`}>
                  <label htmlFor={'weekly-date'} className="text-body-md font-bold text-gray-400">
                    Durasi Konseling
                  </label>
                  <div className={`relative`}>
                    <div className="flex flex-col items-start input-form w-full">
                      <div className="flex flex-row items-center justify-between gap-2">
                        {[
                          { label: '60 Menit', value: 60 },
                          { label: '120 Menit', value: 120 },
                        ].map((time, index) => {
                          return (
                            <ButtonPrimary
                              className={`rounded-sm ${selectedDuration === time.value && 'bg-[#039EE9]/5'}`}
                              textSize="text-body-md"
                              textColor="text-gray-400"
                              color={selectedDuration === time.value ? 'primary' : 'gray'}
                              key={index}
                              variant="outlined"
                              size="xs"
                              onClick={() => setSelectedDuration(time.value)}
                            >
                              {time.label}
                            </ButtonPrimary>
                          )
                        })}
                      </div>
                    </div>
                  </div>
                </div>
                <div className={`form-container grid gap-[8px]`}>
                  <label htmlFor={'weekly-date'} className="text-body-md font-bold text-gray-400">
                    Tanggal
                  </label>
                  <div className={`relative`}>
                    <div className="flex flex-col items-start input-form w-full">
                      <div className="flex flex-row items-center justify-between">
                        <WeeklyCalendarPickerCustom
                          date={date}
                          onSelect={setDate}
                          availableDates={availableDates ?? []}
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <div className={`form-container grid gap-[8px]`}>
                  <label htmlFor={'weekly-date'} className="text-body-md font-bold text-gray-400">
                    Waktu
                  </label>
                  <div className={`relative`}>
                    <div className="flex flex-col items-start input-form w-full">
                      <div className="flex flex-row flex-wrap items-center gap-2">
                        {(dataSchedule?.schedule?.[0]?.times ?? []).map((scheduleTime: any) => {
                          return (
                            <ButtonPrimary
                              className={`rounded-sm ${selectedTime === scheduleTime.time && 'bg-main-100/10'}`}
                              textSize="text-body-md"
                              textColor="text-gray-400"
                              key={scheduleTime.time}
                              variant="outlined"
                              size="xs"
                              type="button"
                              onClick={() => setSelectedTime(scheduleTime.time)}
                            >
                              {`${scheduleTime.time} ${timeZoneLabel}`}
                            </ButtonPrimary>
                          )
                        })}
                      </div>
                    </div>
                  </div>
                  <span className="text-caption-md font-medium text-gray-300 py-2">
                    Tidak menemukan waktu yang tersedia disini? Pastikan Anda sudah atur Jadwal Ketersediaan
                    dulu ya!
                  </span>
                </div>
              </div>
            )}
            <Separator orientation="horizontal" />
            <div className="flex justify-end gap-4 mt-4">
              <ButtonPrimary
                className="w-full lg:w-[160px]"
                variant="outlined"
                size="xs"
                color="gray"
                type="button"
                onClick={() => router.back()}
                disabled={isLoading || isSubmitting || isLoadingForm}
              >
                Batal
              </ButtonPrimary>
              <ButtonPrimary
                className="w-full lg:w-[160px]"
                variant="contained"
                size="xs"
                type="button"
                onClick={() => handleSubmit(onSubmit)()}
                isLoading={isLoading || isSubmitting || isLoadingForm}
                disabled={isLoading || isSubmitting || isLoadingForm}
              >
                Simpan
              </ButtonPrimary>
            </div>
          </>
        </div>
      </div>
    </>
  )
}
