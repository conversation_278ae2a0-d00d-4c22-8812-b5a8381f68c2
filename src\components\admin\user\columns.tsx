'use client'

import { ColumnDef } from '@tanstack/react-table'
import { UserPhoto } from '../../UserPhoto/UserPhoto'
import { AppMediumText } from '@/components/_common/ui'
import { Status } from '@/components/_common/Status/Status'
import { AdminProfile } from '@/interfaces/profile-service'

export const columns: ColumnDef<AdminProfile>[] = [
  {
    accessorKey: 'fullName',
    header: 'Admin',
    cell: ({ row }) => {
      return (
        <div className="font-bold hover:underline hover:text-main-100">
          <UserPhoto
            photo={row.original.profilePhoto}
            title={row.original.fullName}
            subTitle={row.original.nickname}
          />
        </div>
      )
    },
  },
  {
    accessorKey: 'userIdentity.role',
    header: 'Role',
    cell: ({ row }) => {
      return <AppMediumText>{row.original.userIdentity?.role}</AppMediumText>
    },
  },
  {
    accessorKey: 'userIdentity.email',
    header: 'Email',
    cell: ({ row }) => {
      return <AppMediumText>{row.original.userIdentity?.email}</AppMediumText>
    },
  },
  {
    accessorKey: 'phoneNumber',
    header: 'No. Handphone',
    cell: ({ row }) => {
      return <AppMediumText>{row.original.phoneNumber}</AppMediumText>
    },
  },
  {
    accessorKey: 'userIdentity.isActive',
    header: 'Status',
    cell: ({ row }) => {
      return (
        <Status
          variant={row.original.userIdentity?.isActive ? 'success' : 'disable'}
          label={row.original.userIdentity?.isActive ? 'Aktif' : 'Nonaktif'}
        />
      )
    },
  },
]
