import { IIcons, SVGIcons } from '@/components/_common/icon'
import AppInput from '@/components/_common/input/Input'

export default function FilterPenalty() {
  return (
    <div className="flex items-center justify-between gap-2 md:gap-0">
      {/* search */}
      <AppInput
        type="text"
        placeholder="Cari disini.."
        value=""
        onChange={() => {}}
        errorMsg={''}
        prefixIcon={IIcons.Search}
      />
      <div className="h-[48px] py-3 px-2 md:px-4 rounded-[15px] border border-[#EBEBEB] flex items-center gap-1">
        <SVGIcons name={IIcons.Calendar} className="" />
        <span className="text-[#222222] text-[11px] md:text-[14px]">10 Jul - 17 Jul 2024</span>
      </div>
    </div>
  )
}
