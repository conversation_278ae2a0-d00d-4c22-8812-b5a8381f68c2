'use client'

import { ColumnDef } from '@tanstack/react-table'
import { UserPhoto } from '../../UserPhoto/UserPhoto'
import { AppMediumText } from '@/components/_common/ui'
import { Status } from '@/components/_common/Status/Status'

export type ClientProps = {
  profilePhoto: string
  fullName: string
  userIdentity: {
    email: string
    isActive: boolean
  }
}

export const columns: ColumnDef<ClientProps>[] = [
  {
    accessorKey: 'userIdentity',
    header: 'Pengguna',
    cell: ({ cell, row }) => {
      return (
        <div className="font-bold hover:text-main-100">
          <UserPhoto
            photo={cell.row.original['profilePhoto']}
            title={cell.row.original['fullName']}
            subTitle={cell.row.original?.['userIdentity']?.['email']}
          />
        </div>
      )
    },
  },
  {
    accessorKey: 'balance',
    header: 'Saldo',
    cell: ({ row }) => {
      return (
        <AppMediumText>
          {Intl.NumberFormat('id-ID', {
            style: 'currency',
            currency: 'IDR',
            maximumFractionDigits: 0,
            minimumFractionDigits: 0,
          }).format(Number(row.getValue('balance')))}
        </AppMediumText>
      )
    },
  },
  {
    accessorKey: 'konseling',
    header: 'Konseling',
    cell: ({ row }) => {
      return <AppMediumText>{row.getValue('konseling')}</AppMediumText>
    },
  },
  {
    accessorKey: 'journaling',
    header: 'Journaling',
    cell: ({ row }) => {
      return <AppMediumText>{row.getValue('journaling')}</AppMediumText>
    },
  },
  {
    accessorKey: 'isActive',
    header: 'Status',
    cell: ({ cell, row }) => {
      return (
        <Status
          variant={cell.row.original?.['userIdentity']?.['isActive'] ? 'success' : 'disable'}
          label={cell.row.original?.['userIdentity']?.['isActive'] ? 'Aktif' : 'Nonaktif'}
        />
      )
    },
  },
]
