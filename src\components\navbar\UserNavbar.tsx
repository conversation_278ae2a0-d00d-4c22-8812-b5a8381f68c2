'use client'
import Image from 'next/image'
import Link from 'next/link'
import ButtonPrimary from '../_common/ButtonPrimary'
import { UserMenuItems } from '@/constans/UserNavbarMenu'
import { IIcons, SVGIcons } from '../_common/icon'
import { Routes } from '@/constans/routes'
import { useEffect, useRef } from 'react'
import { usePathname, useRouter } from 'next/navigation'
import { isActiveLink } from '@/utils/linkActiveChecker'
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from '../ui/sheet'
import { getValidAuthRole, getValidAuthTokens } from '@/lib/cookies'
import TokenService from '@/services/token.sevice'
import { Feature } from '../../../config'
import { useSelector } from '@/store'
import Avatar from './Avatar'
import { Notification } from './Notification'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '../ui/DropdownMenu'

export default function UserNavbar() {
  const router = useRouter()
  const ref = useRef<any>(null)
  const pathname = usePathname()
  const showAppointmentOnWebsite = Feature.showAppointmentOnWebsite
  const { user } = useSelector((state) => state.Authentication)

  const isActive = (item: any) => {
    const active = isActiveLink(pathname, item.route)
    if (active) return true
    if (item.children) {
      return item.children.some((child: any) => isActive(child))
    }
    return false
  }

  const token = getValidAuthTokens()
  const role = getValidAuthRole()
  const isAuthenticated = token && role

  // Filter menu items based on authentication status
  const getFilteredMenuItems = () => {
    if (isAuthenticated) {
      // For logged-in users, only show Home and Psikolog
      return UserMenuItems.filter((menu) => menu.route === '/home' || menu.route === '/search-psikolog')
    }
    // For non-authenticated users, show all menu items
    return UserMenuItems
  }

  const filteredMenuItems = getFilteredMenuItems()

  useEffect(() => {
    const debounce = (fn: any) => {
      let frame: any
      return (...params: any) => {
        if (frame) {
          cancelAnimationFrame(frame)
        }
        frame = requestAnimationFrame(() => {
          fn(...params)
        })
      }
    }

    const storeScroll = () => {
      document.documentElement.dataset.scroll = String(window.scrollY)
      if (window.scrollY > 0) {
        if (ref.current && ref.current.classList && !ref.current.classList.contains('lg:backdrop-filter')) {
          ref.current.classList.add(
            'lg:backdrop-filter',
            'lg:backdrop-blur-lg',
            'lg:bg-opacity-30',
            'lg:border-0',
            'bg-white',
            'shadow-md'
          )
        }
      } else {
        if (ref.current?.classList?.contains('lg:backdrop-filter')) {
          ref.current.classList.remove(
            'lg:backdrop-filter',
            'lg:backdrop-blur-lg',
            'lg:bg-opacity-30',
            'lg:border-0',
            'bg-white',
            'shadow-md'
          )
        }
      }
    }

    document.addEventListener('scroll', debounce(storeScroll), { passive: true })
    storeScroll()
  })

  return (
    <header className="w-full lg:w-[930px] xl:w-[1120px]">
      <div
        ref={ref}
        className="rounded-none h-[54px] lg:rounded-full lg:h-desktopUserNavbar flex lg:bg-white border-b-0 lg:shadow-md border-line-200 xs:backdrop-filter-none xs:backdrop-blur-none xs:bg-opacity-100"
      >
        <div className="grow place-content-center p-3">
          <Link href="/home">
            <Image width={176} height={32} src={'/icons/mh-logo-with-title.svg'} alt="Logo" priority />
          </Link>
        </div>
        <div className="lg:flex hidden items-center text-gray-400 relative">
          <ul className="flex h-full items-center space-x-6 relative">
            {filteredMenuItems.map((menu) => {
              const isItemActive = isActive(menu)
              if (!menu.showInDesktop) return null

              return (
                <Link key={menu.route} href={menu.route} className="h-full w-full group">
                  <li
                    className={'flex text-center felx-row w-full h-full justify-center items-center'}
                    style={{
                      width: `${menu.width}px`,
                    }}
                  >
                    <div
                      className={`text-body-lg hover:cursor-pointer group-hover:font-bold group-hover:text-main-100 ${isItemActive ? 'text-main-100 font-bold' : 'font-medium'}`}
                    >
                      {menu.label}
                    </div>
                    <div
                      style={{
                        width: `${menu.width}px`,
                      }}
                      className={`absolute group-hover:border-b-4 rounded-t-full group-hover:border-main-100 bottom-0 ${isItemActive && 'border-b-4 border-main-100'}`}
                    ></div>
                  </li>
                </Link>
              )
            })}
            {isAuthenticated && (
              <Link href="/transaksi" className="h-full w-full group">
                <li
                  className={'flex text-center felx-row w-full h-full justify-center items-center'}
                  style={{
                    width: '80px',
                  }}
                >
                  <div
                    className={`text-body-lg hover:cursor-pointer group-hover:font-bold group-hover:text-main-100 ${isActive({ route: '/transaksi' }) ? 'text-main-100 font-bold' : 'font-medium'}`}
                  >
                    Transaksi
                  </div>
                  <div
                    style={{
                      width: '80px',
                    }}
                    className={`absolute group-hover:border-b-4 rounded-t-full group-hover:border-main-100 bottom-0 ${isActive({ route: '/transaksi' }) && 'border-b-4 border-main-100'}`}
                  ></div>
                </li>
              </Link>
            )}
          </ul>
        </div>
        <div className="grow place-content-center flex items-center justify-end">
          <div className="flex justify-end gap-x-[12px]">
            <div className="place-content-center pr-2 flex lg:hidden">
              <Sheet>
                <SheetTrigger asChild>
                  <button
                    aria-controls="sidebar"
                    onClick={(e) => {
                      e.stopPropagation()
                      // handleClickSidebar()
                    }}
                  >
                    <SVGIcons name={IIcons.Burger} className="w-6 h-6" />
                  </button>
                </SheetTrigger>
                <SheetContent side={'top'} className="z-9999 px-3 pt-3 pb-6">
                  <SheetHeader>
                    <SheetTitle className="flex items-center justify-between">
                      <Link href="/">
                        <Image
                          width={176}
                          height={32}
                          src={'/icons/mh-logo-with-title.svg'}
                          alt="Logo"
                          priority
                        />
                      </Link>
                      {isAuthenticated && (
                        <div className="flex items-center gap-x-2">
                          <Avatar
                            image={user?.photoUrl || ''}
                            alt={user?.firebaseCurrentUser?.displayName || ''}
                            width={34}
                            height={34}
                          />
                          <span className="font-medium text-gray-800">
                            {user?.firebaseCurrentUser?.displayName}
                          </span>
                        </div>
                      )}
                    </SheetTitle>
                  </SheetHeader>
                  <div className="grid gap-4 pt-4">
                    {filteredMenuItems.map((menu) => {
                      const isItemActive = isActive(menu)
                      if (!menu.showInDesktop) return null
                      return (
                        <Link
                          key={menu.route}
                          href={menu.route}
                          className="grid grid-cols-1 items-center gap-4"
                        >
                          <div
                            className={`text-body-sm hover:cursor-pointer group-hover:font-bold group-hover:text-main-100 ${isItemActive ? 'text-main-100 font-bold' : 'font-medium'}`}
                          >
                            {menu.label}
                          </div>
                        </Link>
                      )
                    })}
                    {/*mobile*/}
                    {isAuthenticated && (
                      <Link href="/transaksi" className="grid grid-cols-1 items-center gap-4">
                        <div
                          className={`text-body-sm hover:cursor-pointer group-hover:font-bold group-hover:text-main-100 ${isActive({ route: '/transaksi' }) ? 'text-main-100 font-bold' : 'font-medium'}`}
                        >
                          Transaksi
                        </div>
                      </Link>
                    )}
                    {isAuthenticated ? (
                      <ButtonPrimary
                        className="rounded-full"
                        variant="contained"
                        size="sm"
                        onClick={() => {
                          TokenService.removeLocalTokenAndRole()
                          setTimeout(() => {
                            router.push(Routes.Login)
                          }, 500)
                        }}
                      >
                        Keluar
                      </ButtonPrimary>
                    ) : (
                      <Link href="/auth/login" target="_blank" passHref>
                        <ButtonPrimary className="rounded-full" variant="contained" size="sm">
                          Masuk/Daftar
                        </ButtonPrimary>
                      </Link>
                    )}
                  </div>
                </SheetContent>
              </Sheet>
            </div>
            <div className="hidden lg:flex items-center gap-x-6 p-3">
              {isAuthenticated ? (
                <>
                  <Notification onShowAll="#" />
                  <div className="w-[1px] h-6 bg-line-200" />
                  <DropdownMenu>
                    <DropdownMenuTrigger className="flex items-center gap-x-2 outline-none">
                      <SVGIcons name={IIcons.ArrowDown} className="w-4 h-4" />

                      <Avatar
                        image={user?.photoUrl || ''}
                        alt={user?.firebaseCurrentUser?.displayName || ''}
                        width={34}
                        height={34}
                      />
                      <span className="font-medium text-gray-800">
                        {user?.firebaseCurrentUser?.displayName}
                      </span>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-48">
                      <DropdownMenuItem onClick={() => router.push('/profile')}>Akun Saya</DropdownMenuItem>
                      <DropdownMenuItem
                        className="text-red-600"
                        onClick={() => {
                          TokenService.removeLocalTokenAndRole()
                          setTimeout(() => {
                            router.push(Routes.Login)
                          }, 500)
                        }}
                      >
                        Keluar
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </>
              ) : (
                <Link href="/auth/login" target="_blank" passHref>
                  <ButtonPrimary className="min-w-[143px] rounded-full" variant={'contained'} size="xs">
                    Masuk/Daftar
                  </ButtonPrimary>
                </Link>
              )}
            </div>
          </div>
        </div>
      </div>
    </header>
  )
}
