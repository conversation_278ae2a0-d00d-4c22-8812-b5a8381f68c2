import { useState } from 'react'
import { SVGIcons, IIcons } from '@/components/_common/icon'
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuLabel,
} from '@/components/ui/DropdownMenu'
import { Button } from '@/components/ui/button'
import ButtonPrimary from '@/components/_common/ButtonPrimary'
import { Checkbox } from '@/components/ui/checkbox'
import { useDispatch } from '@/store'
import { cn } from '@/lib/utils'
import { avaibilityService } from '@/services/avaibility.service'
import { useToast } from '@/components/ui/use-toast'

type ActionProps = {
  day: number
  onAdd: () => void
  onCopy: () => void
  hideCopyButton?: boolean
}

const DAY_LIST = [
  { id: 1, title: 'Senin', isChecked: false },
  { id: 2, title: 'Selasa', isChecked: false },
  { id: 3, title: '<PERSON><PERSON>', isChecked: false },
  { id: 4, title: '<PERSON><PERSON>', isChecked: false },
  { id: 5, title: 'Ju<PERSON>', isChecked: false },
  { id: 6, title: 'Sabtu', isChecked: false },
  { id: 0, title: 'Minggu', isChecked: false },
]
const AvaibilityAction = ({
  day,
  onAdd,
  onCopy,
  hideCopyButton,
  className,
  refetch,
}: ActionProps & { className?: string; refetch?: () => void }) => {
  const { toast } = useToast()
  const dispatch = useDispatch()
  const [list, setList] = useState(DAY_LIST)
  const [isOpen, setIsOpen] = useState(false)

  const handleChecked = (checked: string | boolean, key: string) => {
    setList((prev) => prev.map((val) => (val.title === key ? { ...val, isChecked: !!checked } : val)))
  }

  const handleSubmit = async () => {
    try {
      const destinationDay: number[] = []
      list.map((val) => (val.isChecked ? destinationDay.push(val.id) : null))
      const payload = {
        copy: day,
        paste: destinationDay,
      }
      await avaibilityService.cloneAvaibility(payload)
      toast({
        title: 'Berhasil',
        description: 'Jadwal berhasil disalin',
        variant: 'success',
      })
      refetch && refetch()
    } catch (error: any) {
      const errorMessage =
        error?.response?.data?.message ||
        error?.message ||
        'Terjadi kesalahan saat menyalin jadwal. Silahkan coba lagi'
      toast({
        title: 'Gagal',
        description: errorMessage,
        variant: 'danger',
      })
    }
  }

  return (
    <div
      className={cn(
        `flex flex-row items-center ${!hideCopyButton ? 'w-[60px]' : 'w-auto'} h-[46px] gap-x-4`,
        className
      )}
    >
      <span className="cursor-pointer" onClick={() => onAdd()}>
        <SVGIcons name={IIcons.Add} />
      </span>
      {hideCopyButton ? null : (
        <DropdownMenu
          modal={false}
          onOpenChange={() => {
            !isOpen && setList((prev) => prev.map((val) => ({ ...val, isChecked: false })))
            setIsOpen((prev) => !prev)
          }}
          open={isOpen}
        >
          <DropdownMenuTrigger className="[&>svg]:data-[state=open]:rotate-180" asChild>
            <Button variant="outline" className="flex items-center p-0 border-0 hover:bg-transparent">
              <SVGIcons name={IIcons.Copy} />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="grid p-4 gap-y-4">
            <DropdownMenuLabel className="px-0">Salin ke hari lain</DropdownMenuLabel>
            {list.map((item) => {
              return (
                <div key={item.title} className="flex justify-between items-center space-x-2">
                  <label
                    htmlFor={item.title}
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    {item.title}
                  </label>
                  <Checkbox
                    checked={item.id === day ? true : item.isChecked}
                    disabled={item.id === day}
                    onCheckedChange={(checked) => handleChecked(checked, item.title)}
                    className="rounded-[4px] disabled:opacity-50 disabled:data-[state=checked]:bg-gray-100 disabled:data-[state=checked]:border-0"
                    id={item.title}
                  />
                </div>
              )
            })}
            <ButtonPrimary variant="contained" size="xs" className="w-full" onClick={() => handleSubmit()}>
              Terapkan
            </ButtonPrimary>
          </DropdownMenuContent>
        </DropdownMenu>
      )}
    </div>
  )
}

export default AvaibilityAction
