import { LoadingCardWithAvatar } from './LoadingCard'
import LoadingGlobalTable from './LoadingGlobalTable'

export const LoadingDetailPsychologist = () => {
  return (
    <div className="grid grid-flow-row-dense grid-cols-6 grid-rows-1 gap-2">
      <div className="col-span-6 xs:col-span-6 sm:col-span-6 xl:col-span-4 flex items-center gap-x-6 p-2 xs:p-3 sm:p-4 xl:p-6">
        <LoadingCardWithAvatar />
      </div>
      <div className="w-full items-center p-2 col-span-6 xs:col-span-6 sm:col-span-3 xl:col-span-1 xs:p-3 sm:p-4 xl:p-6">
        <div className="bg-white border border-line-200 rounded-md w-full mx-auto h-full">
          <div className="animate-pulse flex flex-col gap-2 p-2">
            <div className="flex-1 space-y-6 py-1 px-4">
              <div className="space-y-3">
                <div className="grid grid-cols-3 gap-4">
                  <div className="h-5 bg-slate-200 rounded col-span-2"></div>
                  <div className="h-5 bg-slate-200 rounded col-span-1"></div>
                </div>
                <div className="h-3 bg-slate-200 rounded"></div>
                <div className="flex gap-4">
                  <div className="h-3 bg-slate-200 rounded w-3"></div>
                  <div className="h-3 bg-slate-200 rounded w-1/3"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="w-full items-center p-2 col-span-6 xs:col-span-6 sm:col-span-3 xl:col-span-1 xs:p-3 sm:p-4 xl:p-6">
        <div className="bg-white border border-line-200 rounded-md w-full mx-auto h-full">
          <div className="animate-pulse flex flex-col gap-2 p-2">
            <div className="flex-1 space-y-6 py-1 px-4">
              <div className="space-y-3">
                <div className="grid grid-cols-3 gap-4">
                  <div className="h-5 bg-slate-200 rounded col-span-2"></div>
                  <div className="h-5 bg-slate-200 rounded col-span-1"></div>
                </div>
                <div className="h-3 bg-slate-200 rounded"></div>
                <div className="flex gap-4">
                  <div className="h-3 bg-slate-200 rounded w-3"></div>
                  <div className="h-3 bg-slate-200 rounded w-1/3"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="col-span-6 xs:col-span-6 sm:col-span-6 xl:col-span-6 flex items-center gap-x-6 p-2 xs:p-3 sm:p-4 xl:p-6">
        <LoadingGlobalTable />
      </div>
    </div>
  )
}
