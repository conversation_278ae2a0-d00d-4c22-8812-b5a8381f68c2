<p align="center">
  <a href="https://mentalhealing.id" target="_blank" rel="noopener noreferrer">
    <img width="180" src="https://mentalhealing.id/wp-content/uploads/2020/08/cropped-Logo-Mentalhealing.id-Blue-no-border-1-190x44.png" alt="Mental Healing">
  </a>
</p>
<div align="center">
  <p>Next.js + Tailwind CSS + TypeScript.</p>
  <p>Made by <a href="https://mentalhealing.id">Mental Healing</a></p>
</div>

## Getting started

1. First, clone the repo to your local
   ```bash
   git clone https://github.com/ariardian/mental-healing.git
   # or
   <NAME_EMAIL>:ariardian/mental-healing.git
   ```
2. Instal dependencies
   It is encouraged to use **npm** or **yarn** so the husky hooks can work properly.

   ```bash
   npm install
   ```

   or

   ```bash
   yarn install
   ```

3. Run the development server
   ```bash
   npm run dev
   #or
   yarn dev
   ```
4. Open the http://localhost:3000 to see the result

## Features

This repository is packed with:

- ⚡️ Next.js 14 with App Router
- ⚛️ React 18
- ✨ TypeScript
- 💨 Tailwind CSS 3
- 📈 Import components using `@/` prefix
- 📏 ESLint
- 💖 Prettier
