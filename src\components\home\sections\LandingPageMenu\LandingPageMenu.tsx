'use client'
import { Card } from '@/components/_common/ui'
import { Swiper, SwiperSlide, useSwiper } from 'swiper/react'
import { FreeMode, Navigation } from 'swiper/modules'
import 'swiper/css'
// import 'swiper/css/pagination'
import Image from 'next/image'
import ArrowLeftIcon from '@/assets/icons/arrow-left.svg'
import ArrowRightIcon from '@/assets/icons/arrow-right.svg'
import { useRef } from 'react'

const SlideContent = [
  { id: 1, icon: '/icons/client/category/love.png', label: '<PERSON><PERSON><PERSON><PERSON>' },
  { id: 2, icon: '/icons/client/category/work.png', label: '<PERSON><PERSON><PERSON><PERSON><PERSON>' },
  { id: 3, icon: '/icons/client/category/family.png', label: '<PERSON><PERSON><PERSON><PERSON>' },
  { id: 4, icon: '/icons/client/category/worry.png', label: 'Ke<PERSON>masan' },
  { id: 5, icon: '/icons/client/category/stress.png', label: 'Stress' },
  { id: 6, icon: '/icons/client/category/depress.png', label: 'Depresi' },
  { id: 7, icon: '/icons/client/category/mood.png', label: '<PERSON><PERSON><PERSON> & <PERSON><PERSON>' },
  { id: 8, icon: '/icons/client/category/education.png', label: 'Pendidikan' },
  { id: 9, icon: '/icons/client/category/trauma.png', label: 'Trauma' },
]

export const LandingPageMenu = ({
  label,
  labelMobile,
  onSelect,
}: {
  label?: string
  labelMobile?: string
  onSelect?: (value: string) => void
}) => {
  const heading = label ?? 'Spesialisasi'
  const headingMobile = labelMobile || label || 'Spesialisasi'
  const refPrev = useRef<any>(null)
  const refNext = useRef<any>(null)

  function SlideNextButton() {
    const swiper = useSwiper()
    return (
      <span
        ref={refNext}
        className="rounded-full border border-line-200 p-1 cursor-pointer"
        onClick={() => swiper.slideNext()}
      >
        <ArrowRightIcon className="w-6 h-6" />
      </span>
    )
  }
  function SlidePrevButton() {
    const swiper = useSwiper()
    return (
      <span
        ref={refPrev}
        className="rounded-full border border-line-200 p-1 cursor-pointer"
        onClick={() => swiper.slidePrev()}
      >
        <ArrowLeftIcon className="w-6 h-6" />
      </span>
    )
  }

  const handleClick = (slide: any) => {
    onSelect && onSelect(slide.label)
  }

  return (
    <div className="z-1 max-w-[1120px] md:px-4 relative w-full">
      <Card className="p-0 xs:p-0 sm:p-0 md:p-4 bg-white w-full grid gap-y-2 border-0 md:border">
        <div className="flex justify-between p-4 md:p-0">
          <span className="hidden md:flex text-heading-md font-bold">{heading}</span>
          <span className="flex md:hidden text-subheading-md font-bold">{headingMobile}</span>
          <div className="flex gap-x-2 mb-2 md:mb-0">
            <div className="hidden md:flex gap-x-2">
              <span
                className="rounded-full border border-line-200 p-1 cursor-pointer w-[34px] h-[34px]"
                onClick={() => refPrev.current.click()}
              >
                <ArrowLeftIcon className="w-6 h-6" />
              </span>
              <span
                className="rounded-full border border-line-200 p-1 cursor-pointer w-[34px] h-[34px]"
                onClick={() => refNext.current.click()}
              >
                <ArrowRightIcon className="w-6 h-6" />
              </span>
            </div>
            <span className="text-main-100 font-bold">Lihat semua</span>
          </div>
        </div>
        <div className="relative w-full max-w-full max-h-screen min-h-0 min-w-0">
          <Swiper
            slidesPerView={'auto'}
            navigation
            spaceBetween={8}
            freeMode={true}
            pagination={{
              clickable: false,
            }}
            modules={[FreeMode, Navigation]}
            className="mySwiper"
          >
            <div className="hidden">
              <SlideNextButton />
              <SlidePrevButton />
            </div>
            {SlideContent.map((slide) => (
              <SwiperSlide
                key={slide.id}
                className="md:first:pl-0 md:first:pr-0 first:pl-4 last:pr-4"
                style={{ width: 'fit-content' }}
              >
                <div
                  className="h-[40px] flex p-2 items-center border border-line-200 rounded-sm cursor-pointer"
                  onClick={() => handleClick(slide)}
                >
                  <Image src={slide.icon} alt="mh-icon" height={24} width={24} />
                  <span className="ml-1 text-gray-400 text-body-sm font-medium text-nowrap">
                    {' '}
                    {slide.label}
                  </span>
                </div>
              </SwiperSlide>
            ))}
          </Swiper>
        </div>
      </Card>
    </div>
  )
}
