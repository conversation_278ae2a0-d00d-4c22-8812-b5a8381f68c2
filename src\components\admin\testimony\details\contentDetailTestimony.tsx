import ButtonPrimary from '@/components/_common/ButtonPrimary'
import { IIcons, SVGIcons } from '@/components/_common/icon'

export default function ContentDetailTestimony({
  date,
  time,
  via,
  testimoni,
  create,
  onRemovetestimony,
}: {
  date: string
  time: string
  via: string
  testimoni: string
  create: string
  onRemovetestimony: () => void
}) {
  return (
    <div className="py-4 border-t border-[#EBEBEB] flex flex-col gap-4">
      {/* konseling */}
      <div className="flex flex-col gap-1">
        <span className="text-[14px] text-[#535353]">Konseling</span>
        <div className="flex items-center gap-3 md:gap-6 flex-wrap">
          <div className="flex items-center gap-1">
            <SVGIcons className="mr-2" name={IIcons.Calendar} />
            <span>{date}</span>
          </div>
          <div className="flex items-center gap-1">
            <SVGIcons className="mr-2" name={IIcons.Clock} />
            <span>{time}</span>
          </div>
          <div className="flex items-center gap-1">
            <SVGIcons className="mr-2" name={IIcons.Call} />
            <span>{via}</span>
          </div>
        </div>
      </div>
      {/* testimoni */}
      <div className="flex flex-col gap-1">
        <span className="text-[14px] text-[#535353]">Testimoni</span>
        <span className="text-[16px] text-[#222222]">{testimoni}</span>
      </div>
      {/* dibuat pada */}
      <span className="text-[14px] text-[#535353]">Testimoni diberikan pada: {create}</span>
      {/* btn Hapus */}
      <ButtonPrimary
        className="w-[143px]"
        variant="outlined"
        color="gray"
        size="sm"
        onClick={() => onRemovetestimony()}
      >
        Hapus
      </ButtonPrimary>
    </div>
  )
}
