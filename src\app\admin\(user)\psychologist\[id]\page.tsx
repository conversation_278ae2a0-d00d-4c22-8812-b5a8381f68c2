import Breadcrumb from '@/components/breadcrumbs/Breadcrumbs'
import { Container } from '@/components/_common/ui'
import DetailPsikologComponent from '@/components/admin/psikolog/DetailPsikologComponent'
import { Suspense } from 'react'

const DetailPsikolog = async ({ params }: { params: Promise<{ id: string }> }) => {
  const idPsychologist = (await params).id
  return (
    <Suspense>
      <Breadcrumb containerClasses="pb-2" pageName="Detail Psikolog" />
      <Container>
        <DetailPsikologComponent idPsychologist={idPsychologist} />
      </Container>
    </Suspense>
  )
}

export function generateStaticParams() {
  return [{ id: 'test-id' }]
}

export default DetailPsikolog
