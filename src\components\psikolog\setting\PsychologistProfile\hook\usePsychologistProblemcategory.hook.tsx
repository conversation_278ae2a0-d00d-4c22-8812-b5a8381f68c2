import { useToast } from '@/components/ui/use-toast'
import { psychologistService } from '@/services/psychologist.service'
import { useSelector } from '@/store'
import { AuthRole } from '@/store/auth/auth.action'
import { useQuery } from '@tanstack/react-query'

export const usePsychologistProblemcategory = () => {
  const { user } = useSelector((state) => state.Authentication)
  const { toast } = useToast()
  return useQuery({
    queryKey: ['PsychologistProblemCategory'],
    queryFn: () => {
      if (user?.role === AuthRole.PSIKOLOG) {
        return psychologistService
          .getPsychologistProblemCategory()
          .then((response) => {
            return response?.map((val: any) => ({ ...val, label: val.problemCategory, value: val.id }))
          })
          .catch((error) => {
            toast({
              title: 'Gagal',
              description: '<PERSON><PERSON><PERSON><PERSON> masalah dengan server, Silahkan hubungi Admin',
              variant: 'danger',
            })
          })
      }
    },
  })
}
