import { LevelEducationOptions } from '@/constans/StaticOptions'
import { AppSelect } from '../Select/AppSelect'
import AppInput from '../input/Input'
import { DatePicker } from '@/components/ui/DatePicker'
import { useCallback, useEffect, useState } from 'react'
import { IIcons, SVGIcons } from '../icon'
import { useToast } from '@/components/ui/use-toast'

export type EducationHistoryProps = {
  id?: string
  level?: string
  // major: string
  graduationYear?: string
  university?: string
}

const initData = {
  id: '',
  level: '',
  // major: '',
  graduationYear: '',
  university: '',
}

export const EducationInput = ({
  education,
  onSubmit,
}: {
  education: EducationHistoryProps[]
  onSubmit: (val: EducationHistoryProps[]) => void
}) => {
  const { toast } = useToast()
  const [educationData, setEducationData] = useState<EducationHistoryProps[]>([])

  useEffect(() => {
    education &&
      setEducationData(
        education.map((val, index) => ({ ...val, id: val.id ?? String(new Date().valueOf()) + index }))
      )
  }, [education])

  const handleAddEducation = () => {
    const isInvalid = validationCheck()
    if (!isInvalid) {
      const newData = [...educationData, { ...initData, id: String(new Date().valueOf()) }]
      setEducationData(newData)
      onSubmit(newData)
    } else {
      toast({
        variant: 'danger',
        description: 'Silahkan lengkapi data pendidikan terlebih dahulu.',
      })
    }
  }

  const validationCheck = useCallback(() => {
    return educationData.some(
      (val) =>
        val.graduationYear === '' ||
        val.level === '' ||
        val.university === '' ||
        !!val.graduationYear === false ||
        !!val.level === false ||
        !!val.university === false
    )
  }, [educationData])

  // useEffect(() => {
  //   const isInvalid = validationCheck()
  //   if (!isInvalid) {
  //     onSubmit(educationData)
  //   }
  // }, [educationData, onSubmit, validationCheck])

  const handleRemoveEducation = (id: string) => {
    const filterEducationHistory = educationData.filter((val) => val.id !== id)
    setEducationData(filterEducationHistory)
    onSubmit(filterEducationHistory)
  }

  const handleInputchange = (id: string, key: string, value: string | Date | undefined) => {
    const updatedEducationHistory = educationData.map((val) => {
      if (val.id === id) {
        return { ...val, [key]: value }
      }
      return val
    })
    setEducationData(updatedEducationHistory)
    onSubmit(updatedEducationHistory)
  }

  return (
    <div className="flex flex-col gap-4">
      {educationData.map((data) => {
        return (
          <div key={data.id} className="flex flex-col gap-4">
            <div className="flex w-full flex-col">
              <div className="flex justify-between items-center mb-2">
                <label className="text-gray-400 font-bold text-body-md">Pendidikan</label>
                <span onClick={() => handleRemoveEducation(data.id ?? '')} className="cursor-pointer">
                  <SVGIcons name={IIcons.Close} />
                </span>
              </div>
              <AppSelect
                options={LevelEducationOptions || []}
                onChange={(val) => handleInputchange(data.id ?? '', 'level', val)}
                value={data.level ? String(data.level) : ''}
                className="h-[50px]"
                name="level"
                placeholder="Tingkat Pendidikan"
              />
            </div>
            <AppInput
              placeholder="Nama Universitas"
              type="text"
              value={data.university}
              onChange={(e) => handleInputchange(data.id ?? '', 'university', e.target.value)}
            />
            <div className="flex w-full flex-col">
              <div className="flex justify-between items-center mb-2">
                <label className="text-gray-200 text-body-md">Tahun Lulus</label>
              </div>
              <DatePicker
                placeholder="Tahun Lulus"
                className="py-3 h-[50px] w-full"
                date={data.graduationYear ? new Date(data.graduationYear) : undefined}
                onSelect={(date) => handleInputchange(data.id ?? '', 'graduationYear', date)}
                captionLayout="dropdown"
                fromYear={new Date().getFullYear() - 100}
                toYear={new Date().getFullYear()}
              />
            </div>
          </div>
        )
      })}
      <div
        id="addPendidikan"
        className="py-4 border-t border-[#EBEBEB] cursor-pointer"
        onClick={handleAddEducation}
      >
        <span className={`text-[#039EE9]`}>{'+ Tambah Data Pendidikan'}</span>
      </div>
    </div>
  )
}
