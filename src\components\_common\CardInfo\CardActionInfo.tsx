import { twMerge } from 'tailwind-merge'
import { Card } from '../ui'

type CardActionInfoType = {
  className?: string
  title: string
  value: string
  label: string
  handleAction: () => void
}

export const CardActionInfo = ({ className, title, value, label, handleAction }: CardActionInfoType) => {
  return (
    <Card className={twMerge(`p-6 w-full ${className ?? ''}`)}>
      <div className="flex flex-col gap-y-3">
        <span className="font-sm">{title}</span>
        <p className="text-sm font-bold">{value}</p>
        <p className="text-sm font-bold text-main-100 cursor-pointer" onClick={() => handleAction()}>
          {label}
        </p>
      </div>
    </Card>
  )
}
