'use client'
import { useRouter } from 'next/navigation'
import { IIcons, SVGIcons } from '../_common/icon'

type IconName = keyof typeof IIcons // mendefinisikan tipe dari icon

export default function CardSetting({
  icon,
  title,
  desc,
  route,
}: {
  icon: IIcons
  title: string
  desc: string
  route: string
}) {
  const router = useRouter()

  return (
    <button
      onClick={() => router.push(route)}
      className="flex flex-col gap-2 hover:bg-[#ebebeb] cursor-pointer bg-white rounded-[12px] border border-[#EBEBEB] p-4 w-full lg:w-1/3"
    >
      <SVGIcons name={icon} className="w-6 h-6" />
      <p className="text-[#222222] font-bold text-[16px] text-start">{title}</p>
      <p className="text-[#222222] text-[14px] text-start">{desc}</p>
    </button>
  )
}
