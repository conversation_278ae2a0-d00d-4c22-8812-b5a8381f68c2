import ButtonPrimary from '@/components/_common/ButtonPrimary'
import { IIcons } from '@/components/_common/icon'
import { ListInformation } from '@/components/_common/ListInformation'
import { AppBigText } from '@/components/_common/ui'
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion'
import React from 'react'
import { ClientReportItemContent } from './ClientReportContent'
import { ClientReportProps } from '@/services/clientReport.service'
import {
  formatStringToFulldateOutput,
  formatStringToStartEndTimeOutput,
  formatStringToTimeOutput,
} from '@/utils/displayDate'
import { ScheduleListProps } from '@/store/psikolog/schedule.reducer'
import { LoadingAccordionClientReport } from '@/components/loading/LoadingAccordionClientReport'
import { NoDataFound } from '@/components/_common/NoData/NoDataFound'
import useGetPsychologistTimezone from '@/hooks/useGetPsychologistTimezone.hook'
import useGetTimezoneLabel from '@/hooks/useGetTimezone.hook'

export type ClientReportItem = {
  id: string
  psychologistId: string
  clientId: string
  startTime: string
  endTime: string
  startTimeByClient: null | string
  endTimeByClient: null | string
  startTimeByPsychologist: null | string
  endTimeByPsychologist: null | string
  complaint: string
  expectation: string
  status: string
  description: string
  googleMeetUrl: string
  googleCalendarEventId: string
  method: string
  location: string
  messageForClient: null | string
  messageForPsychologist: null | string
  duration: number
  cancelReason: null | string
  createdAt: string
  createdBy: string
  modifiedAt: string
  modifiedBy: string
  client: {
    id: string
    userIdentityId: string
    fullName: string
    nickname: string
    birthDate: string
    gender: string
    phoneNumber: string
    birthOrder: string
    religion: string
    ethnicity: string
    domicile: string
    maritalStatus: string
    education: string
    occupation: string
    workplace: null | string
    profilePhoto: string
    balance: number
    joinDate: string
    endDate: null | string
    createdAt: string
    createdBy: null | string
    modifiedAt: string
    modifiedBy: null | string
  }
  clientReport: ClientReportProps
}

export type ActionItemProps = {
  isLoadingApp?: boolean
  itemList: ClientReportItem[]
  onCreateClientReport?: (arg: any) => void
  onChangeClientReport?: (arg: any) => void
}
type ClientReportItemProps = ActionItemProps & {
  itemList: any[]
}

export const ClientReportItem = ({
  isLoadingApp,
  itemList,
  onCreateClientReport,
  onChangeClientReport,
}: ClientReportItemProps) => {
  const listOfContent = itemList && itemList.length > 0 ? itemList : []
  const [value, setValue] = React.useState(listOfContent[0]?.id)

  const showCreateClientReportButton = (item: ClientReportItem) => onCreateClientReport && !item.clientReport
  const actionFirstButton = onCreateClientReport && onCreateClientReport
  const showChangeClientReportButton = (item: ClientReportItem) =>
    !!onChangeClientReport && !!item.clientReport

  const labelFirstButton = onCreateClientReport ? 'Tulis Klien Report' : ''

  const psychologistTimezone = useGetPsychologistTimezone()
  const timeZoneLabel = useGetTimezoneLabel()

  return (
    <Accordion type="single" collapsible className="w-full" value={value} onValueChange={setValue}>
      {isLoadingApp ? (
        <LoadingAccordionClientReport />
      ) : listOfContent?.length ? (
        listOfContent.map((list) => {
          const dateLabel = formatStringToFulldateOutput(list?.startTime)
          const timeLabel = formatStringToStartEndTimeOutput({
            date: list?.startTime,
            duration: list?.duration,
            timezone: psychologistTimezone,
            timeLabel: timeZoneLabel,
            isUTC: true,
          })
          const isHasDetail = !!list.clientReport

          return (
            <AccordionItem
              key={list.id}
              value={list.id}
              className="p-6 my-4 gap-y-4 border border-line-200 rounded-card"
            >
              <AccordionTrigger
                label={isHasDetail ? (value === list.id ? 'Tutup' : 'Lihat Detail') : ''}
                className={`py-0 hover:no-underline items-baseline ${
                  !isHasDetail ? '[&>span>svg]:!hidden' : ''
                } ${value === list.id && isHasDetail ? 'border-b border-line-200' : ''}`}
              >
                <div className="grid justify-items-start">
                  <AppBigText bold>
                    Konseling untuk <span className="text-main-100">{list.client.fullName}</span>
                  </AppBigText>
                  <div className="grid">
                    <ListInformation
                      className="border-b-0 gap-4"
                      listItem={[
                        { label: dateLabel, icon: IIcons.Calendar },
                        { label: timeLabel, icon: IIcons.Time },
                        { label: list.method === 'Call' ? 'Call' : 'Video Call', icon: IIcons.Call },
                      ]}
                    />
                  </div>
                </div>
              </AccordionTrigger>
              {isHasDetail && (
                <AccordionContent className="py-4">
                  <ClientReportItemContent {...list} />
                </AccordionContent>
              )}
              <div className="flex gap-3">
                {showCreateClientReportButton(list) && (
                  <ButtonPrimary
                    size="sm"
                    variant="contained"
                    onClick={() => onCreateClientReport && onCreateClientReport(list)}
                  >
                    {labelFirstButton}
                  </ButtonPrimary>
                )}
                {showChangeClientReportButton(list) && (
                  <ButtonPrimary
                    className="col-span-2 md:col-span-1 lg:col-span-3 xl:col-span-2"
                    color="gray"
                    size="sm"
                    variant="outlined"
                    onClick={() => onChangeClientReport && onChangeClientReport(list)}
                  >
                    Ubah Klien Report
                  </ButtonPrimary>
                )}
              </div>
            </AccordionItem>
          )
        })
      ) : (
        <NoDataFound />
      )}
    </Accordion>
  )
}
