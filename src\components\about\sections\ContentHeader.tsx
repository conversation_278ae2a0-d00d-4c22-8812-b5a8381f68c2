import ButtonPrimary from '@/components/_common/ButtonPrimary'
import styles from './style.module.scss'
import Link from 'next/link'

type ContentHeaderProps = {
  heading: string
  subHeading: string
  linkVideo: string
  label1: string
  label2: string
}

export const ContentHeader = ({ heading, subHeading, linkVideo, label1, label2 }: ContentHeaderProps) => {
  return (
    <>
      <div className="flex flex-col md:flex-row gap-y-10 sm:gap-y-3 md:gap-y-5 lg:gap-y-10 gap-x-10 max-w-[1120px] h-[611px] pt-15 lg:pt-32">
        <div className="flex flex-col w-full gap-y-6 xs:gap-y-6 sm:gap-y-3 md:gap-y-6 px-4">
          <div className="text-[#004262]">
            <span className="text-heading-md md:text-[38px] md:leading-[42px] font-bold tracking-tight text-[#004262]">
              {heading}
            </span>
            <div className="text-body-lg text-[#0170A5] max-w-[646px] tracking-tight">{subHeading}</div>
          </div>
          <div className="flex flex-col md:flex-row m gap-x-2 gap-y-3 sm:gap-y-1">
            <Link
              href={
                'https://api.whatsapp.com/send/?phone=6285173025865&text=Hi+Mental+Healing%2C+%28isi+pesan+kamu+disini%29&type=phone_number&app_absent=0'
              }
              target="_blank"
            >
              <ButtonPrimary
                textSize="text-body-lg"
                className="rounded-full shadow-sm w-fit text-nowrap"
                variant={'contained'}
                size="sm"
              >
                {label1}
              </ButtonPrimary>
            </Link>
            <span className="text-[#004262] font-bold flex justify-start items-center text-body-lg">
              {label2}
            </span>
          </div>
        </div>
        <div className={`relative ${styles['responsive']}`}>
          <iframe
            height={315}
            width={560}
            src="https://www.youtube.com/embed/VL5_Yt71384?si=BukT3FTBRPwRQ-Tt"
            title="Mental Healing"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
            referrerPolicy="strict-origin-when-cross-origin"
            style={{ aspectRatio: '16 / 9' }}
            allowFullScreen
          ></iframe>
        </div>
      </div>
    </>
  )
}
