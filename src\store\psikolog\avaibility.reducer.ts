import { createSlice, PayloadAction, current } from '@reduxjs/toolkit'

import { AvaibilityItemProps } from '@/components/psikolog/avaibility/useAvaibilityList.hook'

export type SpecificDateProps = {
  date: string
  availability: AvaibilityItemProps[]
}

export const initialSpecificDate: SpecificDateProps[] = []

type PsikologAvaibilityProps = {
  specificDateAvaibility: SpecificDateProps[]
}

const AvaibilitySlice = createSlice({
  name: 'psikolog-avaibility',
  initialState: {
    specificDateAvaibility: initialSpecificDate,
  } as PsikologAvaibilityProps,
  reducers: {
    removeSpecificDate(state, action: PayloadAction<string>) {
      state.specificDateAvaibility = state.specificDateAvaibility.filter((val) => val.date !== action.payload)
    },
  },
})

export const { removeSpecificDate } = AvaibilitySlice.actions
export default AvaibilitySlice.reducer
