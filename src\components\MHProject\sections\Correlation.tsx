type CorrelationProps = {
  title: string
  subtitle: string
  content: any[]
}

export const Correlation = ({ title, subtitle, content }: CorrelationProps) => {
  return (
    <div className="flex flex-col items-center gap-y-10 px-4">
      <div className="flex flex-col items-center gap-y-4">
        <span className="text-subheading-md md:text-[38px] md:leading-[42px] text-gray-400 font-bold text-center">
          {title}
        </span>
        <span className="text-body-md md:text-body-lg md:leading-[22px] text-gray-300 font-medium text-center">
          {subtitle}
        </span>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {content.length &&
          content.map((item) => {
            return (
              <div
                key={item.id}
                className="flex flex-col gap-y-2 justify-start border border-main-100 rounded-lg p-6"
              >
                <span className="text-main-100 text-[38px] font-bold text-center">{item.heading}</span>
                <span className="text-body-lg text-gray-400 font-medium">{item.content}</span>
                <span className="text-caption-md font-medium text-gray-200">{item.author}</span>
              </div>
            )
          })}
      </div>
    </div>
  )
}
