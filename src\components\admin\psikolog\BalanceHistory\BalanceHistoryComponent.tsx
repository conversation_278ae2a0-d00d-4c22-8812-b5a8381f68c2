'use client'

import { useC<PERSON>back, useEffect, useMemo, useState } from 'react'
import { DateRange } from 'react-day-picker'
import moment from 'moment-timezone'

// Components imports
import { Card } from '@/components/_common/ui'
import ButtonPrimary from '@/components/_common/ButtonPrimary'
import { DatePickerWithRange } from '@/components/ui/DateRangePicker'
import { Button } from '@/components/ui/button'
import { useToast } from '@/components/ui/use-toast'
import { Checkbox } from '@/components/ui/checkbox'
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent } from '@/components/ui/DropdownMenu'
import { IIcons, SVGIcons } from '@/components/_common/icon'

// Services and hooks
import { balanceService } from '@/services/balance.service'
import { useGetPsychologistBalance } from '@/components/psikolog/income/hook/useGetPsychologistBalance.hook'
import { useGetProfile } from '@/hooks/useGetProfile.hook'
import { useBalanceHistory } from './hooks/useBalanceHistory'
import { TransactionList } from './TransactionList'

// Constants
import { WithdrawalFormDialog } from './WithdrawalFormDialog'
import { AddFundsModal } from './AddFundsModal'

const FILTER_BALANCE = [
  { id: 0, title: 'Semua Transaksi', key: 'ALL' },
  { id: 1, title: 'Komisi dari konseling', key: 'INCOME' },
  { id: 2, title: 'Penarikan Saldo', key: 'OUTCOME' },
] as const

interface BalanceHistoryProps {
  id: string
}

export default function BalanceHistoryComponent({ id }: BalanceHistoryProps) {
  // States
  const [isOpen, setIsOpen] = useState(false)
  const [isWithdrawalOpen, setIsWithdrawalOpen] = useState(false)
  const [isAddFundsModal, setIsAddFundsModalOpen] = useState(false)
  const [dateRange, setDateRange] = useState<DateRange | undefined>()
  const [selectedTransactionType, setSelectedTransactionType] = useState<string>('ALL')

  // Hooks
  const { toast } = useToast()
  const { data: balanceData } = useGetPsychologistBalance()
  const { data: dataBankAccount } = useGetProfile()
  const { isLoading, transactionData } = useBalanceHistory({
    id,
    selectedTransactionType,
  })

  const lastUpdateInfo = useMemo(() => {
    if (!balanceData?.lastUpdate) return ''
    const date = moment.utc(new Date(balanceData.lastUpdate))
    const isUpdateToday = date.clone().diff(moment(), 'days') === 0
    return isUpdateToday ? `hari ini, ${date.format('HH:mm')}` : date.format('LLL')
  }, [balanceData?.lastUpdate])

  const handleChecked = useCallback((checked: boolean, key: string) => {
    const filterItem = FILTER_BALANCE.find((item) => String(item.id) === key)
    if (!filterItem) return

    setSelectedTransactionType((prev) => {
      if (checked) {
        return filterItem.key
      } else {
        return 'ALL'
      }
    })
  }, [])

  const handleDateRangeChange = useCallback((date: DateRange) => {
    setDateRange(date)
  }, [])

  // Set initial date range after component mounts
  useEffect(() => {
    const today = new Date()
    setDateRange({
      from: today,
      to: today,
    })
  }, [])

  return (
    <div className="pt-6 flex flex-col gap-6 w-full lg:w-[70%]">
      <div className="flex gap-x-4">
        <ButtonPrimary
          className="min-w-[143px] w-[190px] rounded-[15px]"
          variant="contained"
          size="base"
          onClick={() => setIsWithdrawalOpen(true)}
        >
          Penarikan Dana
        </ButtonPrimary>

        <ButtonPrimary
          className="min-w-[143px] w-[190px] rounded-[15px]"
          variant="outlined"
          size="base"
          onClick={() => setIsAddFundsModalOpen(true)}
        >
          Tambah Dana
        </ButtonPrimary>
      </div>

      <Card className="grid col-span-12 sm:col-span-12 xl:col-span-9 p-4 xs:p-4 sm:p-4 xl:p-6">
        <div className="flex flex-col">
          <div className="flex flex-wrap gap-y-4 gap-x-0 sm:gap-4 mb-4 justify-between">
            <div className="flex flex-wrap w-full sm:w-auto gap-y-4 gap-x-0 sm:gap-4">
              <DropdownMenu modal={false} onOpenChange={setIsOpen} open={isOpen}>
                <DropdownMenuTrigger className="[&>svg]:data-[state=open]:rotate-180" asChild>
                  <Button
                    variant="outline"
                    className="flex items-center gap-x-2 h-auto min-h-[50px] rounded-2xl w-full sm:w-auto"
                  >
                    <span>
                      {FILTER_BALANCE.find((item) => item.key === selectedTransactionType)?.title ||
                        'Semua Transaksi'}
                    </span>
                    <SVGIcons name={IIcons.ArrowDown} />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="grid p-4 gap-y-4">
                  {FILTER_BALANCE.map((item) => (
                    <Button
                      key={item.id}
                      variant="ghost"
                      className="w-full justify-start"
                      onClick={() => setSelectedTransactionType(item.key)}
                    >
                      {item.title}
                    </Button>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>

            <DatePickerWithRange
              formatDate={{ from: 'd LLL', to: 'd LLL' }}
              className="h-[42px] sm:h-[55px] xs:w-auto"
              onSelectDate={handleDateRangeChange}
              valueDate={dateRange}
            />
          </div>
        </div>

        <TransactionList transactions={transactionData.data} isLoading={isLoading} />
      </Card>

      <WithdrawalFormDialog
        isOpen={isWithdrawalOpen}
        onClose={() => setIsWithdrawalOpen(false)}
        psychologistId={id}
      />
      <AddFundsModal
        isOpen={isAddFundsModal}
        onClose={() => setIsAddFundsModalOpen(false)}
        psychologistId={id}
      />
    </div>
  )
}
