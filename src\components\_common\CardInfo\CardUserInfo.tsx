'use client'
import React from 'react'
import { Card, H2 } from '../ui'
import Image from 'next/image'
import { twMerge } from 'tailwind-merge'
import { AvatarWithInfo } from './AvatarWithInfo'

type UserInfoType = {
  image: string
  className?: string
  heading: string
  subHeading?: string | React.ReactNode
  alt?: string
}

export const CardUSerInfo = ({ className, image, heading, subHeading, alt }: UserInfoType) => {
  return (
    <Card className={twMerge(`p-6 w-full ${className ?? ''}`)}>
      <AvatarWithInfo wrapClassName="flex" className="h-[92px] w-[92px]" image={image} alt={alt}>
        <div className="text-wrap grid gap-y-[11px]">
          <H2>{heading}</H2>
          {subHeading}
        </div>
      </AvatarWithInfo>
    </Card>
  )
}
