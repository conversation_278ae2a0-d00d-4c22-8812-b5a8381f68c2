'use client'
import { CounselingData } from '@/hooks/useCounselings'
import CardTransaksi from './cardTransaksi'
import { PaymentStatus } from '@/constans/StaticOptions'
import { formatDate, formatTime } from '../../utils/utilsDate'

interface ContentRiwayatProps {
  title: string
  title2: string
  title3: string
  historyData: CounselingData[]
  loading: boolean
}

export default function ContentRiwayat({ title, title2, title3, historyData, loading }: ContentRiwayatProps) {
  if (loading) {
    return <div className="flex justify-center my-8">loading..</div>
  }

  if (historyData.length === 0) {
    return (
      <div className="text-center my-8 px-4">
        <p className="text-gray-500">Belum ada riwayat konseling.</p>
      </div>
    )
  }

  // Filter cancelled data into cancelled and payment expired
  const cancelledCounseling = historyData.filter((item) =>
    [
      PaymentStatus.CANCELLED_BY_PSYCHOLOGIST,
      PaymentStatus.CANCELLED_BY_ADMIN,
      PaymentStatus.REJECTED_BY_PSYCHOLOGIST,
    ].includes(item.status as PaymentStatus)
  )

  const expiredPaymentData = historyData.filter((item) => item.status === PaymentStatus.EXPIRED_PAYMENT)

  return (
    <div className="flex flex-col gap-4 mt-3 px-4 md:px-0">
      {/* Completed counseling */}
      {historyData.length > 0 && (
        <div className="mb-2">
          <h3 className="text-sm font-medium text-gray-500 mb-3">{title}</h3>
          {historyData.map((item) => {
            const startDate = new Date(item.startTime)
            const formattedDate = formatDate(startDate)
            const formattedStartTime = formatTime(new Date(item.startTime))
            const formattedEndTime = formatTime(new Date(item.endTime))

            return (
              <CardTransaksi
                key={item.id}
                id={item.id}
                title={title}
                buttonType="outlined"
                buttonTitle="Jadwalkan Lagi"
                buttonTitle2=""
                textButtonColor=""
                numberButton="1"
                date={formattedDate}
                time={`${formattedStartTime} - ${formattedEndTime} WIB`}
                method={item.method}
                psychologistName={item.psychologist.fullName}
                psychologistPhoto={item.psychologist.profilePhoto}
              />
            )
          })}
        </div>
      )}

      {/* Cancelled counseling */}
      {cancelledCounseling.length > 0 && (
        <div className="mb-2">
          <h3 className="text-sm font-medium text-gray-500 mb-3">{title2}</h3>
          {cancelledCounseling.map((item) => {
            const startDate = new Date(item.startTime)
            const formattedDate = formatDate(startDate)
            const formattedStartTime = formatTime(new Date(item.startTime))
            const formattedEndTime = formatTime(new Date(item.endTime))

            return (
              <CardTransaksi
                key={item.id}
                id={item.id}
                title={title2}
                buttonType="outlined"
                buttonTitle="Jadwalkan Lagi"
                buttonTitle2=""
                textButtonColor=""
                numberButton="1"
                date={formattedDate}
                time={`${formattedStartTime} - ${formattedEndTime} WIB`}
                method={item.method}
                psychologistName={item.psychologist.fullName}
                psychologistPhoto={item.psychologist.profilePhoto}
              />
            )
          })}
        </div>
      )}

      {/* Expired payment */}
      {expiredPaymentData.length > 0 && (
        <div className="mb-2">
          <h3 className="text-sm font-medium text-gray-500 mb-3">{title3}</h3>
          {expiredPaymentData.map((item) => {
            const startDate = new Date(item.startTime)
            const formattedDate = formatDate(startDate)
            const formattedStartTime = formatTime(new Date(item.startTime))
            const formattedEndTime = formatTime(new Date(item.endTime))

            return (
              <CardTransaksi
                key={item.id}
                id={item.id}
                title={title3}
                buttonType="outlined"
                buttonTitle="Jadwalkan Lagi"
                buttonTitle2=""
                textButtonColor=""
                numberButton="1"
                date={formattedDate}
                time={`${formattedStartTime} - ${formattedEndTime} WIB`}
                method={item.method}
                psychologistName={item.psychologist.fullName}
                psychologistPhoto={item.psychologist.profilePhoto}
              />
            )
          })}
        </div>
      )}
    </div>
  )
}
