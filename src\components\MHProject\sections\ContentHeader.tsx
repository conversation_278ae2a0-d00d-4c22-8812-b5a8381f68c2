import TickIcon from '@/assets/icons/tick-list.svg'
import ButtonPrimary from '@/components/_common/ButtonPrimary'
import Link from 'next/link'

type ContentHeaderProps = {
  tag: string
  title: string
  list: string[]
  label1: string
  label2: string
}

export const ContentHeader = ({ tag, title, list, label1, label2 }: ContentHeaderProps) => (
  <div className="flex flex-col max-w-[1120px] w-full gap-y-4 px-4 pt-15 md:pt-0">
    <div className="text-[#004262]">
      <span className="font-bold">{tag}</span>
      <div className="text-heading-md md:text-[50px] md:leading-[62px] font-bold max-w-[646px] tracking-tight">
        {title}
      </div>
    </div>
    <div className="grid text-[#0170A5] gap-y-1">
      {list.map((item) => {
        return (
          <span key={item} className="flex text-body-lg">
            <TickIcon className="w-6 h-6 mr-2" />
            {item}
          </span>
        )
      })}
    </div>
    <div className="flex flex-col md:flex-row m gap-x-2">
      <Link href={'#assestment-form'}>
        <ButtonPrimary
          textSize="text-body-lg"
          className="rounded-full shadow-sm w-fit"
          variant={'contained'}
          size="sm"
        >
          {label1}
        </ButtonPrimary>
      </Link>
      <span className="text-[#004262] font-bold flex justify-start items-center text-body-lg">{label2}</span>
    </div>
  </div>
)
