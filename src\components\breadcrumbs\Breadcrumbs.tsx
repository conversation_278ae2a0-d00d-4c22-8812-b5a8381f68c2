'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import React, { ReactNode } from 'react'
import { IIcons, SVGIcons } from '../_common/icon'
import { translateBreadcrumbTitle } from '@/utils/breadcrumb'

type BreadcrumbProps = {
  pageName?: string
  homeElement?: ReactNode
  separator?: ReactNode
  containerClasses?: string
  listClasses?: string
  activeClasses?: string
  capitalizeLinks?: boolean
}

const Breadcrumb = ({
  pageName,
  homeElement,
  separator,
  containerClasses,
  listClasses,
  activeClasses,
  capitalizeLinks = true,
}: BreadcrumbProps) => {
  const paths = usePathname()
  const pathNames = paths.split('/').filter((path) => path)

  const homeElementBreadcrumb = homeElement ? homeElement : null
  const separatorBreadcrumb = separator ? (
    separator
  ) : (
    <span className="mx-5 text-gray-500 dark:text-gray-300 rtl:-scale-x-100">
      <SVGIcons name={IIcons.ArrowRight} />
    </span>
  )
  const listItemStyles = listClasses ?? 'text-xs font-bold hover:underline'
  const activeStyles = activeClasses ? activeClasses : 'font-medium text-gray-200 hover:no-underline'
  return (
    <>
      <ul className={`flex items-center overflow-x-auto whitespace-nowrap ${containerClasses}`}>
        {homeElementBreadcrumb && (
          <>
            <li className={listItemStyles}>
              <Link href={'/'}>{homeElementBreadcrumb}</Link>
            </li>
            {pathNames.length > 0 && separatorBreadcrumb}
          </>
        )}
        {pathNames.map((link, index) => {
          const href = `/${pathNames.slice(0, index + 1).join('/')}`
          const itemClasses =
            paths === href
              ? `${listItemStyles} ${activeStyles} text-gray-200`
              : `${listItemStyles} text-main-100`
          const itemLink = capitalizeLinks ? link[0].toUpperCase() + link.slice(1, link.length) : link
          const itemTitle = paths === href && pageName ? pageName : itemLink
          return (
            <React.Fragment key={index}>
              {paths === href && pageName ? (
                <li className={itemClasses}>{translateBreadcrumbTitle(itemTitle)}</li>
              ) : (
                <li className={itemClasses}>
                  <Link href={href}>{translateBreadcrumbTitle(itemTitle)}</Link>
                </li>
              )}
              {pathNames.length !== index + 1 && separatorBreadcrumb}
            </React.Fragment>
          )
        })}
      </ul>
    </>
  )
}

export default Breadcrumb
