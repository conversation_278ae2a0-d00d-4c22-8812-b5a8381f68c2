'use client'
import TabItem from '@/components/_common/tabs/TabItem'
import TabList from '@/components/_common/tabs/TabList'
import { HeaderContent } from '@/components/admin/HeaderContent'
import PersonalDataPsikolog from './PersonalDataPsikolog'
import SecurityRoot from './SecurityRoot'
import { useState } from 'react'
import { useGetProfile } from '@/hooks/useGetProfile.hook'
import { AdminProfile } from '@/interfaces/profile-service'

export const SettingPageComponent = () => {
  const [activeTabIndex, setActiveTabIndex] = useState<number>(0)
  const { data: setting, refetch } = useGetProfile()

  const SettingTab = [
    {
      label: 'Profil Pribadi',
      content: <PersonalDataPsikolog {...(setting as AdminProfile)} />,
    },
    {
      label: 'Login & Keamanan',
      content: <SecurityRoot {...(setting as AdminProfile)} />,
    },
  ]

  return (
    <div>
      <HeaderContent title={'Pengaturan'} />
      <div className="gap-4 mt-4">
        <TabList
          onClickTabs={(index) => {
            console.log(index)
            setActiveTabIndex(index)
          }}
          className="sticky top-navbar z-20 bg-white"
          activeTabIndex={activeTabIndex}
        >
          {SettingTab.map((setting, index) => {
            return (
              <TabItem className="bg-main-100" key={index} label={setting.label}>
                {setting.content}
              </TabItem>
            )
          })}
        </TabList>
      </div>
    </div>
  )
}

export default SettingPageComponent
