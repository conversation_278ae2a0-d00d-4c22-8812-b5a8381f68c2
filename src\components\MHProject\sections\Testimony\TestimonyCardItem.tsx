import QuoteIcon from '@/assets/icons/quote.svg'
import { AvatarWithInfo } from '@/components/_common/CardInfo/AvatarWithInfo'
import { TestimonyResponse } from './Testimony'

export const TestimonyCardItem = ({ id, testimony, clientName }: TestimonyResponse) => {
  const psychologist = clientName.split('-')
  return (
    <div
      key={id}
      className="p-3 bg-white flex flex-col justify-center gap-y-3 border border-line-200 rounded-lg"
    >
      <div className="flex flex-col gap-y-1">
        <QuoteIcon />
        <div
          className="text-body-sm font-medium text-gray-400 flex flex-col gap-y-4"
          dangerouslySetInnerHTML={{ __html: testimony }}
        ></div>
      </div>
      <AvatarWithInfo
        className="w-[42px] h-[42px]"
        image={''}
        heading={<span className="text-body-md font-bold text-[#222222]">{psychologist[0]}</span>}
        subHeading={<span className="text-caption-md font-medium text-gray-300">{psychologist[1]}</span>}
      />
    </div>
  )
}
