import { ReactNode } from 'react'

type TableProps = {
  children?: ReactNode
  className?: string
  onClickRow?: (row: any) => void
}

type TableCellProps = TableProps & {
  colSpan?: number
  align?: 'left' | 'right' | 'center'
}

export const Table = ({ children, className = '' }: TableProps) => {
  return (
    <table className={`min-w-full border-y border-line-200 divide-y divide-line-200 ${className ?? ''}`}>
      {children ?? null}
    </table>
  )
}
export const TableBody = ({ children, className }: TableProps) => {
  return <tbody className={`bg-white divide-y divide-line-200 ${className ?? ''}`}>{children ?? null}</tbody>
}
export const TableCell = ({ children, className, colSpan, align }: TableCellProps) => {
  return (
    <td
      colSpan={colSpan ?? 0}
      className={`px-4 py-4 text-sm font-medium whitespace-nowrap ${align === 'right' ? 'text-right' : align === 'center' ? 'text-center' : 'text-left'} ${className ?? ''}`}
    >
      {children ?? null}
    </td>
  )
}
export const TableHead = ({ children, className }: TableProps) => {
  return (
    <th
      scope="col"
      className={`py-3.5 px-4 text-sm font-bold text-left uppercase bg-line-100 text-gray-400 ${className ?? ''}`}
    >
      {children ?? null}
    </th>
  )
}
export const TableHeader = ({ children, className }: TableProps) => {
  return <thead className={`bg-gray-50 dark:bg-gray-800 ${className ?? ''}`}>{children ?? null}</thead>
}
export const TableRow = ({ children, className, onClickRow }: TableProps) => {
  return (
    <tr
      className={`${className ?? ''}`}
      onClick={(e) => {
        onClickRow && onClickRow(e)
      }}
    >
      {children ?? null}
    </tr>
  )
}
