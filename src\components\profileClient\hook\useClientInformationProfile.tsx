'use client'
import { useState, useEffect } from 'react'
import { profileService } from '@/services/profile.service'

export interface UserIdentity {
  id: string
  email: string
  role: string
  firebaseUid: string
  isEmailVerified: boolean
  isActive: boolean
  createdAt: string
  modifiedAt: string
  userConfig: {
    ENABLE_PIN: boolean
    NOTIFICATION_NEWS_UPDATE: boolean
    NOTIFICATION_COUNSELING_STATUS: boolean
  }
  onboardingSteps: null | string
  onboardingCompleteAt: null | string
  hasPin: boolean
}

interface BankAccount {
  id?: string
  accountNumber?: string
  accountName?: string
  bankName?: string
}

export interface ProfileInformation {
  id: string
  userIdentityId: string
  fullName: string
  nickname: string
  birthDate: string
  gender: string
  phoneNumber: string
  birthOrder: string
  religion: string
  ethnicity: string
  domicile: string
  maritalStatus: string
  education: string
  occupation: string
  workplace: string | null
  profilePhoto: string
  joinDate: string
  endDate: null | string
  createdAt: string
  modifiedAt: string
  userIdentity: UserIdentity
  bankAccount: BankAccount[]
}

export interface UpdateProfileData {
  fullName?: string
  nickname?: string
  birthDate?: string | Date
  gender?: string
  phoneNumber?: string
  birthOrder?: string
  religion?: string
  ethnicity?: string
  domicile?: string
  maritalStatus?: string
  education?: string
  occupation?: string
  workplace?: string | null
  profilePhoto?: File | null
}

export const useProfileInformation = () => {
  const [profileData, setProfileData] = useState<ProfileInformation | null>(null)
  const [loading, setLoading] = useState<boolean>(true)
  const [error, setError] = useState<Error | null>(null)
  const [updateLoading, setUpdateLoading] = useState<boolean>(false)
  const [updateSuccess, setUpdateSuccess] = useState<boolean>(false)
  const [updateError, setUpdateError] = useState<Error | null>(null)

  const fetchProfileInformation = async () => {
    try {
      setLoading(true)
      const response = await profileService.clientGetProfileInformation()
      setProfileData(response.data)
      setError(null)
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to fetch profile information'))
      console.error('Error fetching profile information:', err)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchProfileInformation()
  }, [])

  const updateProfileInformation = async (data: UpdateProfileData) => {
    try {
      setUpdateLoading(true)
      setUpdateSuccess(false)
      setUpdateError(null)

      // Create FormData object for the multipart/form-data request
      const formData = new FormData()

      // Add all non-null fields to the FormData
      Object.entries(data).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          // Convert Date objects to ISO strings for the API
          if (key === 'birthDate' && value instanceof Date) {
            formData.append(key, value.toISOString())
          } else {
            formData.append(key, value as any)
          }
        }
      })

      // Make the API call
      const response = await profileService.clientUpdateProfile(formData)

      if (response.success) {
        // Refetch the profile data to get the updated information
        await fetchProfileInformation()
        setUpdateSuccess(true)
      } else {
        throw new Error('Update failed')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update profile information'
      setUpdateError(new Error(errorMessage))
      console.error('Error updating profile information:', err)
    } finally {
      setUpdateLoading(false)
    }
  }

  return {
    profileData,
    loading,
    error,
    updateProfileInformation,
    updateLoading,
    updateSuccess,
    updateError,
  }
}
