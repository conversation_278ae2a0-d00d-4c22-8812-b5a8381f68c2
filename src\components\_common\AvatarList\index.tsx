import { cn } from '@/lib/utils'
import Image from 'next/image'

type AvatarListProps = {
  imageList: string[]
  itemClass?: string
  containerClass?: string
  shownItem?: number
  size?: number
}

export const AvatarList = ({
  imageList,
  containerClass,
  itemClass,
  shownItem = 2,
  size = 10,
}: AvatarListProps) => {
  return (
    <div className={`flex items-center ${containerClass}`}>
      {imageList && imageList.length > 0
        ? imageList.map(
            (value, index) =>
              index < shownItem && (
                <Image
                  key={index}
                  width={size}
                  height={size}
                  src={value}
                  alt="Avatar"
                  className={cn(
                    `object-cover w-6 h-6 -mx-1 border-2 border-white rounded-full shrink-0`,
                    itemClass
                  )}
                />
              )
          )
        : null}
      {imageList?.length && imageList.length > shownItem ? (
        <p
          className={cn(
            'flex items-center justify-center w-6 h-6 -mx-1 text-xs bg-blue-100 border-2 border-white rounded-full',
            itemClass
          )}
        >
          {`+${imageList.length - shownItem}`}
        </p>
      ) : null}
    </div>
  )
}
