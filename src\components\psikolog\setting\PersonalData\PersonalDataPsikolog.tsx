'use client'

import { useForm } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import * as yup from 'yup'
import { useToast } from '@/components/ui/use-toast'
import { useEffect, useState } from 'react'
import { PsychologistProfile } from '@/interfaces/profile-service'
import { profileService } from '@/services/profile.service'
import { FormSetting } from '../FormSetting'
import AppInput from '@/components/_common/input/Input'
import { DatePicker } from '@/components/ui/DatePicker'
import moment from 'moment'
import { RadioInput } from '@/components/_common/RadioInput/RadioInput'
import { AppSelect } from '@/components/_common/Select/AppSelect'
import {
  EthnicityoptionsList,
  GenderOptions,
  MaritalStatusOptions,
  Otheroptions,
  ReligionOptionsList,
} from '@/constans/onboardOptions'
import { DomicileView } from './DomicileView'
import { DomiciliesOptions } from '@/components/auth/Onboard/DomiciliesOptions'

const validationSchema = yup.object().shape({
  nickname: yup.string().nullable(),
  fullName: yup.string().nullable(),
  birthDate: yup.date().nullable(),
  gender: yup.string().nullable(),
  domicile: yup.string().nullable(),
  birthOrder: yup.string().nullable(),
  ethnicity: yup.string().nullable(),
  ethnicityOther: yup.string().when('ethnicity', {
    is: (val: string) => val === Otheroptions,
    then: () => yup.string().required('Silahkan lengkapi data suku bangsa.'),
  }),
  religion: yup.string().nullable(),
  religionOther: yup.string().when('religion', {
    is: (val: string) => val === Otheroptions,
    then: () => yup.string().required('Silahkan lengkapi data agama.'),
  }),
  occupation: yup.string().nullable(),
  workplace: yup.string().nullable(),
  maritalStatus: yup.string().nullable(),
  childTo: yup.string().nullable(),
  totalSibling: yup.string().nullable(),
  province: yup.string().nullable(),
  regencies: yup.string().nullable(),
  districts: yup.string().nullable(),
  subDistricts: yup.string().nullable(),
})

type InferTypeProfile = yup.InferType<typeof validationSchema>
type KeysType = keyof InferTypeProfile

const PersonalDataPsikolog = ({
  nickname,
  fullName,
  birthDate,
  gender,
  domicile,
  birthOrder,
  ethnicity,
  religion,
  occupation,
  workplace,
  maritalStatus,
}: PsychologistProfile) => {
  const { toast } = useToast()
  const [isLoadingForm, setIsLoadingForm] = useState<boolean>(false)
  const [fieldEdit, setFieldEdit] = useState<string | null>(null)
  const EthnicityoptionsListWithoutOther = [...EthnicityoptionsList]
    .filter((val) => val.value !== Otheroptions)
    .map((item) => item.value)
  const ReligionOptionsListWithoutOther = [...ReligionOptionsList]
    .filter((val) => val.value !== Otheroptions)
    .map((item) => item.value)

  const {
    register,
    handleSubmit,
    trigger,
    getValues,
    setValue,
    reset,
    resetField,
    formState: { errors, isLoading, isSubmitting },
  } = useForm({
    resolver: yupResolver(validationSchema),
    defaultValues: {
      nickname: '',
      fullName: '',
      birthDate: null,
      gender: '',
      domicile: '',
      birthOrder: '',
      ethnicity: '',
      ethnicityOther: '',
      religion: '',
      religionOther: '',
      occupation: '',
      workplace: '',
      maritalStatus: '',
    },
  })

  useEffect(() => {
    reset({
      nickname: nickname ?? '',
      fullName: fullName ?? '',
      birthDate: birthDate ? new Date(birthDate) : null,
      gender: gender ?? '',
      domicile: domicile ?? '',
      birthOrder: birthOrder ?? '',
      ethnicity: !EthnicityoptionsListWithoutOther.includes(ethnicity) ? Otheroptions : (ethnicity ?? ''),
      ethnicityOther: !EthnicityoptionsListWithoutOther.includes(ethnicity) ? ethnicity : '',
      religion: !ReligionOptionsListWithoutOther.includes(religion) ? Otheroptions : (religion ?? ''),
      religionOther: !ReligionOptionsListWithoutOther.includes(religion) ? religion : '',
      occupation: occupation ?? '',
      workplace: workplace ?? '',
      maritalStatus: maritalStatus ?? '',
      childTo: birthOrder ? birthOrder.split('/')[0] : '',
      totalSibling: birthOrder ? birthOrder.split('/')[1] : '',
      province: domicile ? domicile.split(',')[0]?.replace(/\s/g, '') : '',
      regencies: domicile ? domicile.split(',')[1]?.replace(/\s/g, '') : '',
      districts: domicile ? domicile.split(',')[2]?.replace(/\s/g, '') : '',
      subDistricts: domicile ? domicile.split(',')[3]?.replace(/\s/g, '') : '',
    })
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    birthDate,
    birthOrder,
    domicile,
    ethnicity,
    fullName,
    gender,
    maritalStatus,
    nickname,
    occupation,
    religion,
    reset,
    workplace,
  ])

  const handleOnSubmit = async (key: string) => {
    setIsLoadingForm(true)
    if (key === 'ethnicity' || key === 'religion') {
      if (key === 'ethnicity') {
        const isValid = await trigger(['ethnicity', 'ethnicityOther'])
        if (!isValid) {
          setIsLoadingForm(false)
          return
        }
      } else {
        const isValid = await trigger(['religion', 'religionOther'])
        if (!isValid) {
          setIsLoadingForm(false)
          return
        }
      }
    }
    const isValid = await trigger(
      key === 'domicile' ? ['province', 'regencies', 'districts', 'subDistricts'] : [key as any]
    )
    if (isValid) {
      if (
        (key === 'domicile' &&
          getValues('province') !== '' &&
          getValues('regencies') !== '' &&
          getValues('districts') !== '' &&
          getValues('subDistricts') !== '' &&
          !!getValues('province') &&
          !!getValues('regencies') &&
          !!getValues('districts') &&
          !!getValues('subDistricts')) ||
        key !== 'domicile'
      ) {
        try {
          const formData = new FormData()
          if (key === 'ethnicity' && getValues('ethnicity') === Otheroptions) {
            formData.append('ethnicity', getValues('ethnicityOther')!)
          } else if (key === 'religion' && getValues('religion') === Otheroptions) {
            formData.append('religion', getValues('religionOther')!)
          } else {
            formData.append(key, getValues(key as any))
          }
          const result = await profileService.updatePsychologistProfile(formData)
          if (key === 'ethnicity') {
            if (getValues('ethnicity') !== Otheroptions) {
              resetField('ethnicity' as KeysType, { defaultValue: result?.[key] })
              resetField('ethnicityOther' as KeysType, { defaultValue: '' })
            } else {
              resetField('ethnicity' as KeysType, { defaultValue: result?.[key] })
              resetField('ethnicityOther' as KeysType, { defaultValue: result?.[key] })
            }
          } else if (key === 'religion') {
            if (getValues('religion') !== Otheroptions) {
              resetField('religion' as KeysType, { defaultValue: result?.[key] })
              resetField('religionOther' as KeysType, { defaultValue: '' })
            } else {
              resetField('religion' as KeysType, { defaultValue: result?.[key] })
              resetField('religionOther' as KeysType, { defaultValue: result?.[key] })
            }
          } else {
            resetField(key as KeysType, { defaultValue: result?.[key] })
          }
          toast({
            variant: 'success',
            title: 'Perbaharui data profil berhasil',
          })
          setIsLoadingForm(false)
          setFieldEdit(null)
        } catch (error) {
          toast({
            variant: 'danger',
            title: 'Perbaharui data profil gagal',
          })
          setFieldEdit(null)
          setIsLoadingForm(false)
        }
      } else {
        setIsLoadingForm(false)
        toast({
          variant: 'danger',
          title: 'Silahkan lengkapi data terlebih dahulu.',
        })
      }
    } else {
      setIsLoadingForm(false)
    }
  }

  return (
    <div className="grid gap-y-6">
      <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
        <div className="col-span-2">
          <FormSetting
            viewComponent={getValues('nickname') ?? ''}
            editComponent={
              <AppInput
                {...register('nickname')}
                className="pt-0"
                type="text"
                value={getValues('nickname')!}
                onChange={(val) => {
                  setValue('nickname', val.target.value, { shouldValidate: true })
                }}
                name="nickname"
                label="Nama Panggilan"
                errorMsg={!!errors.nickname ? String(errors.nickname.message) : undefined}
                placeholder="nickname"
              />
            }
            label="Nama Panggilan"
            isEdit={fieldEdit === 'nickname'}
            isLoading={isLoadingForm}
            onEditButton={() => setFieldEdit('nickname')}
            onCancel={() => {
              setFieldEdit(null)
              resetField('nickname')
            }}
            onSubmit={() => handleOnSubmit('nickname')}
          />
          <FormSetting
            viewComponent={getValues('fullName') ?? ''}
            editComponent={
              <AppInput
                {...register('fullName')}
                className="pt-0"
                type="text"
                value={getValues('fullName')!}
                onChange={(val) => {
                  setValue('fullName', val.target.value, { shouldValidate: true })
                }}
                name="fullName"
                label="Nama Lengkap"
                errorMsg={!!errors.fullName ? String(errors.fullName.message) : undefined}
                placeholder="fullName"
              />
            }
            label="Nama Lengkap"
            isEdit={fieldEdit === 'fullName'}
            isLoading={isLoadingForm}
            onEditButton={() => setFieldEdit('fullName')}
            onCancel={() => {
              setFieldEdit(null)
              resetField('fullName')
            }}
            onSubmit={() => handleOnSubmit('fullName')}
          />
          <FormSetting
            viewComponent={
              getValues('birthDate') ? moment(getValues('birthDate')).format('DD MMMM YYYY') : '-'
            }
            editComponent={
              <DatePicker
                placeholder=""
                className="py-3 h-[50px] w-full"
                date={getValues('birthDate') ? new Date(getValues('birthDate')!) : undefined}
                label="Tanggal Lahir"
                errorMsg={!!errors.birthDate ? String(errors.birthDate.message) : undefined}
                onSelect={(date) => setValue('birthDate', date, { shouldValidate: true })}
                captionLayout="dropdown"
                fromYear={new Date().getFullYear() - 100}
                toYear={new Date().getFullYear()}
              />
            }
            label="Tanggal Lahir"
            isEdit={fieldEdit === 'birthDate'}
            isLoading={isLoadingForm}
            onEditButton={() => setFieldEdit('birthDate')}
            onCancel={() => {
              setFieldEdit(null)
              resetField('birthDate')
            }}
            onSubmit={() => handleOnSubmit('birthDate')}
          />
          <FormSetting
            viewComponent={getValues('gender') ?? '-'}
            editComponent={
              <RadioInput
                options={GenderOptions}
                name={'gender'}
                value={getValues('gender')!}
                label="Jenis Kelamin"
                errorMsg={!!errors.gender ? String(errors.gender.message) : undefined}
                onChange={(val) => {
                  setValue('gender', val, { shouldValidate: true })
                }}
              />
            }
            label="Jenis Kelamin"
            isEdit={fieldEdit === 'gender'}
            isLoading={isLoadingForm}
            onEditButton={() => setFieldEdit('gender')}
            onCancel={() => {
              setFieldEdit(null)
              resetField('gender')
            }}
            onSubmit={() => handleOnSubmit('gender')}
          />
          <FormSetting
            viewComponent={
              <DomicileView
                domicile={getValues('domicile') ? (getValues('domicile')?.split(',') as string[]) : []}
              />
            }
            editComponent={
              <div className="grid gap-2">
                <DomiciliesOptions
                  register={register}
                  getValues={getValues}
                  setValue={setValue}
                  errors={errors}
                  onSubmit={() => {}}
                  onFinalSetValue={(val: string) => setValue('domicile', val, { shouldValidate: true })}
                />
              </div>
            }
            label="Domisili"
            isEdit={fieldEdit === 'domicile'}
            isLoading={isLoadingForm}
            onEditButton={() => setFieldEdit('domicile')}
            onCancel={() => {
              setFieldEdit(null)
              resetField('domicile')
            }}
            onSubmit={() => handleOnSubmit('domicile')}
          />
          <FormSetting
            viewComponent={
              getValues('birthOrder')
                ? `${(getValues('birthOrder') ?? '0/0').split('/').join(' dari ')} bersaudara`
                : ''
            }
            editComponent={
              <div className="grid gap-2">
                <label className="text-body-md font-bold text-gray-400">Urutan Bersaudara</label>
                <div className="flex gap-4 items-center">
                  <AppInput
                    {...register('childTo')}
                    className="pt-0 max-w-[105px]"
                    type="number"
                    name="childTo"
                    onChange={(val) => {
                      setValue('childTo', val.target.value, { shouldValidate: true })
                      const currentBirthOrder = getValues('birthOrder') ?? '0/0'
                      const splitBirthOrder = currentBirthOrder.split('/')
                      setValue('birthOrder', `${val.target.value}/${splitBirthOrder[1]}`, {
                        shouldValidate: true,
                      })
                    }}
                    errorMsg={!!errors.childTo ? String(errors.childTo.message) : undefined}
                    placeholder="Anak ke"
                  />
                  <span>Dari</span>
                  <AppInput
                    {...register('totalSibling')}
                    className="pt-0 grow"
                    type="number"
                    name="totalSibling"
                    onChange={(val) => {
                      setValue('totalSibling', val.target.value, { shouldValidate: true })
                      const currentBirthOrder = getValues('birthOrder') ?? '0/0'
                      const splitBirthOrder = currentBirthOrder.split('/')
                      setValue('birthOrder', `${splitBirthOrder[0]}/${val.target.value}`, {
                        shouldValidate: true,
                      })
                    }}
                    errorMsg={!!errors.totalSibling ? String(errors.totalSibling.message) : undefined}
                    placeholder="Jumlah Saudara"
                  />
                </div>
              </div>
            }
            label="Urutan Bersaudara"
            isEdit={fieldEdit === 'birthOrder'}
            isLoading={isLoadingForm}
            onEditButton={() => setFieldEdit('birthOrder')}
            onCancel={() => {
              setFieldEdit(null)
              resetField('birthOrder')
            }}
            onSubmit={() => handleOnSubmit('birthOrder')}
          />
          <FormSetting
            viewComponent={getValues('maritalStatus') ?? ''}
            editComponent={
              <RadioInput
                options={MaritalStatusOptions}
                name={'maritalStatus'}
                value={getValues('maritalStatus')!}
                label="Status Pernikahan"
                errorMsg={!!errors.maritalStatus ? String(errors.maritalStatus.message) : undefined}
                onChange={(val) => {
                  setValue('maritalStatus', val, { shouldValidate: true })
                }}
              />
            }
            label="Status Pernikahan"
            isEdit={fieldEdit === 'maritalStatus'}
            isLoading={isLoadingForm}
            onEditButton={() => setFieldEdit('maritalStatus')}
            onCancel={() => {
              setFieldEdit(null)
              resetField('maritalStatus')
            }}
            onSubmit={() => handleOnSubmit('maritalStatus')}
          />
          <FormSetting
            viewComponent={
              getValues('ethnicity') === Otheroptions ||
              !EthnicityoptionsListWithoutOther.includes(getValues('ethnicity') ?? '')
                ? getValues('ethnicityOther')
                : (getValues('ethnicity') ?? '')
            }
            editComponent={
              <>
                <AppSelect
                  {...register('ethnicity')}
                  options={EthnicityoptionsList || []}
                  onChange={(val) => {
                    setValue('ethnicity', val, { shouldValidate: true })
                  }}
                  value={
                    getValues('ethnicity') === Otheroptions ||
                    !EthnicityoptionsListWithoutOther.includes(getValues('ethnicity') ?? '')
                      ? Otheroptions
                      : getValues('ethnicity')
                        ? String(getValues('ethnicity'))
                        : ''
                  }
                  className="h-[50px]"
                  label="Suku Bangsa"
                  name="ethnicity"
                  placeholder="Pilih Suku Bangsa"
                  errorMsg={!!errors.ethnicity ? String(errors.ethnicity.message) : undefined}
                />
                {(getValues('ethnicity') === Otheroptions ||
                  !EthnicityoptionsListWithoutOther.includes(getValues('ethnicity') ?? '')) && (
                  <AppInput
                    {...register('ethnicityOther')}
                    className="pt-4"
                    type="text"
                    name="ethnicityOther"
                    placeholder="Silahkan masukan suku bangsa lainnya"
                    errorMsg={!!errors.ethnicityOther ? String(errors.ethnicityOther.message) : undefined}
                  />
                )}
              </>
            }
            label="Suku Bangsa"
            isEdit={fieldEdit === 'ethnicity'}
            isLoading={isLoadingForm}
            onEditButton={() => setFieldEdit('ethnicity')}
            onCancel={() => {
              setFieldEdit(null)
              resetField('ethnicity')
              resetField('ethnicityOther')
            }}
            onSubmit={() => handleOnSubmit('ethnicity')}
          />
          <FormSetting
            viewComponent={
              getValues('religion') === Otheroptions ||
              !ReligionOptionsListWithoutOther.includes(getValues('religion') ?? '')
                ? getValues('religionOther')
                : (getValues('religion') ?? '')
            }
            editComponent={
              <>
                <AppSelect
                  {...register('religion')}
                  options={ReligionOptionsList || []}
                  onChange={(val) => {
                    setValue('religion', val, { shouldValidate: true })
                  }}
                  value={
                    getValues('religion') === Otheroptions ||
                    !ReligionOptionsListWithoutOther.includes(getValues('religion') ?? '')
                      ? Otheroptions
                      : getValues('religion')
                        ? String(getValues('religion'))
                        : ''
                  }
                  className="h-[50px]"
                  label="Agama"
                  name="religion"
                  placeholder="Pilih Agama"
                  errorMsg={!!errors.religion ? String(errors.religion.message) : undefined}
                />
                {(getValues('religion') === Otheroptions ||
                  !ReligionOptionsListWithoutOther.includes(getValues('religion') ?? '')) && (
                  <AppInput
                    {...register('religionOther')}
                    className="pt-4"
                    type="text"
                    name="religionOther"
                    placeholder="Silahkan masukan agama lainnya"
                    errorMsg={!!errors.religionOther ? String(errors.religionOther.message) : undefined}
                  />
                )}
              </>
            }
            label="Agama"
            isEdit={fieldEdit === 'religion'}
            isLoading={isLoadingForm}
            onEditButton={() => setFieldEdit('religion')}
            onCancel={() => {
              setFieldEdit(null)
              resetField('religion')
              resetField('religionOther')
            }}
            onSubmit={() => handleOnSubmit('religion')}
          />
        </div>
      </div>
    </div>
  )
}

export default PersonalDataPsikolog
