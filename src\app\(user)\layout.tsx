'use client'
import ClickOutside from '@/components/_common/ClickOutside'
import UserNavbar from '@/components/navbar/UserNavbar'
import { useState } from 'react'

import { BottomNavigationMenu } from '@/components/navbar/BottomNavigationMenu'
import { usePathname, useSearchParams } from 'next/navigation'
import { UserMenuItems } from '@/constans/UserNavbarMenu'
import { Feature } from '../../../config'

export default function UserLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  const searchParam = useSearchParams()
  const isMobile = searchParam.get('isMobile')
  const [sidebarOpen, setSidebarOpen] = useState<boolean>(false)
  const pathname = usePathname()
  const isAuth = pathname.includes('/auth')
  const bg = isAuth ? 'bg-[#E7F7FF]' : 'bg-white'
  const showAppointmentOnWebsite = Feature.showAppointmentOnWebsite
  return (
    <>
      <div className={`relative flex flex-1 flex-col min-h-screen items-center ${bg}`}>
        {!isMobile && (
          <ClickOutside
            className="sticky flex w-full items-center justify-center top-0 lg:top-[33px] z-20"
            onClick={() => setSidebarOpen(false)}
          >
            <UserNavbar />
          </ClickOutside>
        )}
        {children}
      </div>
      {!isMobile && showAppointmentOnWebsite && (
        <div className="mt-bottomUserMenu flex md:hidden">
          <BottomNavigationMenu menuList={UserMenuItems} />
        </div>
      )}
    </>
  )
}
