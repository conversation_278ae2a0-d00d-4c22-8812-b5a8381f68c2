{"name": "mental-healing-web", "version": "0.1.0", "private": true, "scripts": {"dev": "cross-env NODE_OPTIONS='--inspect' next dev", "build": "next build", "start": "SET NODE_ENV=production && node server.js", "lint": "next lint --max-warnings 0", "prettier": "prettier -w ."}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@next/third-parties": "^14.2.13", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@reduxjs/toolkit": "^2.2.7", "@tanstack/react-query": "^5.50.1", "@tanstack/react-table": "^8.19.2", "axios": "^1.7.5", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "1.0.0", "cookies-next": "^4.2.1", "date-fns": "^3.6.0", "embla-carousel-react": "^8.2.0", "firebase": "^10.13.0", "html-to-image": "^1.11.13", "html2canvas": "^1.4.1", "lottie-react": "^2.4.0", "moment": "^2.30.1", "moment-timezone": "^0.5.46", "next": "14.2.4", "react": "^18", "react-day-picker": "^8.10.1", "react-dom": "^18", "react-fast-marquee": "^1.6.5", "react-hook-form": "^7.53.0", "react-redux": "^9.1.2", "redux-persist": "^6.0.0", "sass": "^1.78.0", "sharp": "^0.33.4", "swiper": "^11.1.12", "tailwind-merge": "^2.4.0", "tailwindcss-animate": "^1.0.7", "yup": "^1.4.0"}, "devDependencies": {"@faker-js/faker": "^8.4.1", "@svgr/webpack": "^8.1.0", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "cross-env": "^7.0.3", "eslint": "^8", "eslint-config-next": "14.2.4", "next-react-svg": "^1.2.0", "postcss": "^8", "prettier": "^3.3.2", "tailwindcss": "^3.4.1", "typescript": "^5"}}