import plugin from 'tailwindcss/plugin'
import type { Config } from 'tailwindcss'
import { fontFamily } from 'tailwindcss/defaultTheme'

const config = {
  darkMode: ['class'],
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
    './src/**/*.{ts,tsx}',
  ],
  prefix: '',
  theme: {
    screens: {
      xs: '375px', // Mobile view
      sm: '640px', // Tablet view or landscape mobile
      md: '768px', //lanscape tablet
      lg: '1024px', // laptop view
      '2lg': '1100px', // specific
      xl: '1280px', // desktop view
      MHmax: '1440px',
    },
    container: {
      center: true,
      padding: '2rem',
      screens: {
        '2xl': '1400px',
      },
    },
    extend: {
      fontFamily: {
        sans: ['var(--font-quicksand)', ...fontFamily.sans],
      },
      fontSize: {
        'heading-lg': ['32px', '40px'],
        'heading-md': ['28px', '36px'],
        'heading-sm': ['26px', '32px'],
        'subheading-md': ['20px', '26px'],
        'body-lg': ['16px', '22px'],
        'body-md': ['14px', '20px'],
        'body-sm': ['14px', '18px'],
        'caption-md': ['12px', '16px'],
        'caption-sm': ['11px', '14px'],
      },
      colors: {
        transparent: 'transparent',
        current: 'currentColor',
        main: {
          50: '#E7F7FF',
          100: '#039EE9',
          200: '#0F8BC6',
          300: '#0870A1',
        },
        line: {
          100: '#F4F4F4',
          200: '#EBEBEB',
        },
        gray: {
          100: '#C0C0C0',
          200: '#737373',
          300: '#535353',
          400: '#222222',
        },
        success: {
          100: '#179141',
          200: '#137838',
          300: '#0F602E',
        },
        danger: {
          100: '#E42B3B',
          200: '#C9212D',
          300: '#A81D25',
        },
        info: {
          100: '#4781F3',
          200: '#3B70D9',
          300: '#2E5DC5',
        },
        warning: {
          100: '#FED748',
          200: '#FDC52F',
          300: '#FBBF24',
        },
        border: '#EBEBEB',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        primary: {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))',
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))',
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))',
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))',
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))',
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))',
        },
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))',
        },
      },
      borderRadius: {
        full: '9999px',
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
        card: '15px',
      },
      spacing: {
        10: '2.5rem',
        15: '3.75rem',
        72.5: '18.125rem',
        sidebar: '200px',
        navbar: '4.75rem',
        mobileUserNavbar: '54px',
        desktopUserNavbar: '64px',
        bottomUserMenu: '66px',
        desktopSliderhHeaderContent: '134px',
        mobileSliderhHeaderContent: '74px',
        MHmax: '1440px',
      },
      maxWidth: {
        2.5: '0.625rem',
        3: '0.75rem',
        4: '1rem',
        7: '1.75rem',
        9: '2.25rem',
        10: '2.5rem',
        10.5: '2.625rem',
        11: '2.75rem',
        13: '3.25rem',
        14: '3.5rem',
        15: '3.75rem',
        16: '4rem',
        22.5: '5.625rem',
        25: '6.25rem',
        30: '7.5rem',
        34: '8.5rem',
        35: '8.75rem',
        40: '10rem',
        42.5: '10.625rem',
        44: '11rem',
        45: '11.25rem',
        60: '15rem',
        70: '17.5rem',
        90: '22.5rem',
        94: '23.5rem',
        125: '31.25rem',
        132.5: '33.125rem',
        142.5: '35.625rem',
        150: '37.5rem',
        180: '45rem',
        203: '50.75rem',
        230: '57.5rem',
        242.5: '60.625rem',
        270: '67.5rem',
        280: '70rem',
        292.5: '73.125rem',
      },
      maxHeight: {
        35: '8.75rem',
        70: '17.5rem',
        90: '22.5rem',
        550: '34.375rem',
        300: '18.75rem',
      },
      minWidth: {
        22.5: '5.625rem',
        42.5: '10.625rem',
        47.5: '11.875rem',
        75: '18.75rem',
      },
      zIndex: {
        999999: '999999',
        99999: '99999',
        9999: '9999',
        999: '999',
        99: '99',
        9: '9',
        1: '1',
      },
      opacity: {
        65: '.65',
      },
      boxShadow: {
        default: '0px 8px 13px -3px rgba(0, 0, 0, 0.07)',
        card: '0px 1px 3px rgba(0, 0, 0, 0.12)',
        'card-2': '0px 1px 2px rgba(0, 0, 0, 0.05)',
        switcher:
          '0px 2px 4px rgba(0, 0, 0, 0.2), inset 0px 2px 2px #FFFFFF, inset 0px -1px 1px rgba(0, 0, 0, 0.1)',
        'switch-1': '0px 0px 5px rgba(0, 0, 0, 0.15)',
        1: '0px 4px 10px 0px rgba(0,0,15,0.5)',
      },
      keyframes: {
        'accordion-down': {
          from: { height: '0' },
          to: { height: 'var(--radix-accordion-content-height)' },
        },
        'accordion-up': {
          from: { height: 'var(--radix-accordion-content-height)' },
          to: { height: '0' },
        },
      },
      animation: {
        'accordion-down': 'accordion-down 0.2s ease-out',
        'accordion-up': 'accordion-up 0.2s ease-out',
      },
    },
  },
  // plugins: [require('tailwindcss-animate')],
  plugins: [
    require('tailwindcss-animate'),
    plugin(function ({ addUtilities }) {
      addUtilities({
        '.scrollbar-hide': {
          /* IE and Edge */
          '-ms-overflow-style': 'none',

          /* Firefox */
          'scrollbar-width': 'none',

          /* Safari and Chrome */
          '&::-webkit-scrollbar': {
            display: 'none',
          },
        },
        '.shadow-card': {
          boxShadow: '0px 2px 15px #0000001F',
        },
      })
    }),
  ],
} satisfies Config

export default config
