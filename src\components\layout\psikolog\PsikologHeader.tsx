import { IIcons, SVGIcons } from '@/components/_common/icon'
import Image from 'next/image'

type PsikologHeaderProps = {
  isOpen: boolean | undefined
  setOpen: (arg0: boolean) => void
}
const PsikologHeader = ({ isOpen, setOpen }: PsikologHeaderProps) => {
  return (
    <nav className="relative mt-8">
      <div className="container py-4 mx-auto md:flex md:justify-between md:items-center">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-x-6">
            <Image width={48} height={48} className="rounded-full" src="https://" alt="" />

            <span className="text-lg font-bold capitalize whitespace-nowrap">Selamat siang, Hilmi</span>
          </div>
        </div>
        <div
          x-cloak
          className={`${isOpen ? 'translate-x-0 opacity-100 ' : 'opacity-0 -translate-x-full'} gap-x-2 absolute inset-x-0 z-20 w-full px-6 py-4 transition-all duration-300 ease-in-out bg-white dark:bg-gray-800 md:mt-0 md:p-0 md:top-0 md:relative md:bg-transparent md:w-auto md:opacity-100 md:translate-x-0 md:flex md:items-center`}
        >
          <div className="flex flex-col md:flex-row md:mx-6">
            <button className="bg-white border border-mh-gray-300 p-2 rounded-lg">
              <div>
                <SVGIcons name={IIcons.Bell} />
              </div>
            </button>
          </div>

          <div className="flex justify-center md:block">
            <button className="bg-white border border-mh-gray-300 p-2 rounded-lg">
              <div>
                <SVGIcons name={IIcons.User} />
              </div>
            </button>
          </div>
        </div>

        {/* <!-- Mobile Menu open: "block", Menu closed: "hidden" --> */}
        {/* <div
          className={`${isOpen ? 'translate-x-0 opacity-100 ' : 'opacity-0 -translate-x-full'} absolute inset-x-0 z-20 w-full px-6 py-4 transition-all duration-300 ease-in-out bg-white dark:bg-gray-800 md:mt-0 md:p-0 md:top-0 md:relative md:opacity-100 md:translate-x-0 md:flex md:items-center md:justify-between`}
        ></div> */}
      </div>
    </nav>
  )
}

export default PsikologHeader
