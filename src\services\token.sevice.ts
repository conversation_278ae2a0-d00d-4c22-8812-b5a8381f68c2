import { deleteAuthCookies, getValidAuthTokens } from '@/lib/cookies'

const localStorageName = 'persist:root'

const getLocalAuthStorage = () => {
  try {
    const storage = JSON.parse(localStorage.getItem(localStorageName) ?? '')
    const auth = storage
    return auth
  } catch (error) {
    console.log(error)
    return {}
  }
}

const getLocalAccessToken = () => {
  const token = getValidAuthTokens()
  return token
}

const getLocalUser = () => {
  const auth = getLocalAuthStorage()
  return auth?.user
}

const deleteLocalStorage = () => {
  localStorage.removeItem(localStorageName)
}

const removeLocalTokenAndRole = () => {
  deleteAuthCookies()
  deleteLocalStorage()
}

const TokenService = {
  getLocalAuthStorage,
  getLocalAccessToken,
  getLocalUser,
  removeLocalTokenAndRole,
}

export default TokenService
