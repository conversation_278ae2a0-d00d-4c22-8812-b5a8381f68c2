'use client'
import { StaticImport } from 'next/dist/shared/lib/get-img-props'
import Image from 'next/image'
import { AvatarFallback, AvatarImage, Avatar as AvatarComponent } from '../ui/avatar'
import { useEffect, useState } from 'react'
export type ImageProps = string | StaticImport
type AvatarProps = {
  image: ImageProps
  width?: number
  height?: number
  scaleDown?: boolean
  alt?: string
  objectPosition?: string
}

const Avatar = ({ image, width = 34, height = 34, scaleDown, alt, objectPosition }: AvatarProps) => {
  const [initial, setInitial] = useState<string>('')
  const getInitialName = (name: string) => {
    if (name.length) {
      let StringName = name.split(' '),
        initials = StringName[0].substring(0, 1).toUpperCase()

      if (StringName.length > 1) {
        initials += StringName[StringName.length - 1].substring(0, 1).toUpperCase()
      }
      return initials
    }
  }

  useEffect(() => {
    try {
      const initial = getInitialName(alt || '')
      if (initial) {
        setInitial(initial)
      }
    } catch (error) {}
  }, [alt])

  return (
    <AvatarComponent
      style={{
        height: height,
        width: width,
      }}
    >
      <AvatarImage asChild src={(image ?? '') as string}>
        <Image
          src={image}
          width={width}
          height={height}
          style={{
            height: height,
            width: width,
            objectPosition: objectPosition ?? 'top',
            objectFit: scaleDown ? 'scale-down' : 'cover',
            background: '#f1f1f1',
          }}
          alt="avatar-image"
          className={`${scaleDown ? 'object-scale-down' : 'object-cover'} rounded-full ring-1 ring-line-200`}
        />
      </AvatarImage>
      <AvatarFallback>{initial}</AvatarFallback>
    </AvatarComponent>
  )
}

export default Avatar
