import axios from 'axios'
import { signInWithPopup } from 'firebase/auth'
import TokenService from '@/services/token.sevice'
import { logout } from '@/store/auth/auth.reducer'

let dispatch: (arg0: any) => any

export const injectDispatch = (_dispatch: any) => {
  dispatch = _dispatch
}

const onRejectedAPI = async (err: any) => {
  if (err.response && err.response.status === 401) {
    if (dispatch) {
      dispatch(logout())
      TokenService.removeLocalTokenAndRole()
      window.location.reload()
    }
  }

  return Promise.reject(err)
}

export const httpRequest = async ({ method, url, data, token, headers, isDownload = false }: any) => {
  const localToken = TokenService.getLocalAccessToken()
  const customHeaders = { ...headers, ...{ Authorization: 'Bearer ' + (token || localToken) } }
  const config = {
    method,
    url,
    data,
    headers: customHeaders,
  }
  const axiosInstance = axios.create()
  axiosInstance.interceptors.response.use((res) => res, onRejectedAPI)
  const res = await axiosInstance(config)
  return isDownload ? res : res.data
}
