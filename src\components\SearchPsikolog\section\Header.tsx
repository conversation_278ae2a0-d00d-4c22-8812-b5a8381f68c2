import Image from 'next/image'

export const Header = ({ title, titleMobile }: { title: string; titleMobile?: string }) => {
  return (
    <div className="flex items-center justify-center -mt-[64px] w-full relative">
      <div className="h-[239px] md:h-[419px] w-full relative bg-[#E6F5FD]">
        <Image
          src="/ilustration/header.svg"
          alt="header"
          fill
          className="object-scale-down object-bottom lg:object-cover lg:object-center"
        />
      </div>
      <div className="z-1 absolute w-full h-full max-w-MHmax xl:px-40">
        <div className="z-1 w-full h-full">
          <span className="hidden md:flex w-full h-full justtify-left md:justify-start items-center text-heading-sm px-4 xl:px-0 md:text-[50px] text-[#004262] font-bold">
            {title ?? ''}
          </span>
          <span className="md:hidden flex w-full h-full justtify-left md:justify-center items-center text-heading-sm px-4 xl:px-0 md:text-[50px] text-[#004262] font-bold">
            {titleMobile ?? 'Psikolog'}
          </span>
        </div>
      </div>
    </div>
  )
}
