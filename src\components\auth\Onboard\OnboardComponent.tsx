'use client'
import Title from '../Title'
import WrapperAuth from '../WrapperAuth'
import ButtonPrimary from '@/components/_common/ButtonPrimary'

import { yupResolver } from '@hookform/resolvers/yup'
import { useForm } from 'react-hook-form'
import * as yup from 'yup'
import { useRouter } from 'next/navigation'
import { Routes } from '@/constans/routes'
import { RemoveIndex } from '@/utils/type'
import { firebaseAuth } from '@/lib/firebase/config'
import { dispatch, useSelector } from '@/store'
import { format } from 'date-fns'
import { INPUT_DATE_FORMAT } from '@/constans/date'
import { authService } from '@/services/auth.service'
import { refreshToken } from '@/lib/firebase/auth'
import { setAuthInformation, setShowEmailVerification } from '@/store/auth/auth.reducer'
import { useEffect, useState } from 'react'
import SubTitle from '../SubTitle'
import { OnboardStepOne } from './OnboardStepOne'
import Translation from '@/constans/Translation'
import { OnboardStepTwo } from './OnboardStepTwo'
import { Otheroptions } from '@/constans/onboardOptions'
import { AuthRole } from '@/store/auth/auth.action'
import { getValidAuthRole, getValidAuthTokens } from '@/lib/cookies'
import { useToast } from '@/components/ui/use-toast'

const validationSchema = yup.object().shape({
  email: yup.string().required(Translation.RequiredEmail).email(Translation.ValidEmail),
  password: yup
    .string()
    .min(6, Translation.PasswordMinChar)
    .matches(/[A-Z]/, Translation.RequiredUppercase)
    .matches(/[a-z]/, Translation.RequiredLowercase)
    .matches(/\d/, Translation.RequiredNumber)
    .required(Translation.PasswordRequired),
  rePassword: yup.string().oneOf([yup.ref('password')], Translation.RepasswordMustMatch),
  fullName: yup.string().nullable(),
  nickname: yup.string().nullable(),
  birthDate: yup.date().nullable(),
  gender: yup.string().nullable(),
  phoneNumber: yup.string().nullable(),
  birthOrder: yup.string().nullable(),
  religion: yup.string().nullable(),
  religionOther: yup.string().nullable(),
  ethnicity: yup.string().nullable(),
  ethnicityOther: yup.string().nullable(),
  domicile: yup.string().nullable(),
  maritalStatus: yup.string().nullable(),
  education: yup.string().nullable(),
  occupation: yup.string().nullable(),
  occupationOther: yup.string().nullable(),
  joinDate: yup.string().nullable(),
  endDate: yup.string().nullable(),
  profilePhoto: yup.string().nullable(),
  bankName: yup.string().nullable(),
  bankAccount: yup.string().nullable(),
  bankAccountName: yup.string().nullable(),
  childTo: yup.string().nullable(),
  totalSibling: yup.string().nullable(),
  province: yup.string().nullable(),
  regencies: yup.string().nullable(),
  districts: yup.string().nullable(),
  subDistricts: yup.string().nullable(),
})

export type OnboardType = RemoveIndex<yup.InferType<typeof validationSchema>>

const OnboardComponent = () => {
  const { toast } = useToast()
  const router = useRouter()
  const [onBoardStep, setOnBoardStep] = useState<number>(1)
  const { user, isCompleteOnboard } = useSelector((state) => state.Authentication)
  const token = getValidAuthTokens()
  const userRole = getValidAuthRole()

  const {
    register,
    handleSubmit,
    trigger,
    getValues,
    setValue,
    reset,
    formState: { errors, isLoading, isSubmitting },
  } = useForm({
    resolver: yupResolver(validationSchema),
    defaultValues: {
      email: '',
      password: '',
      rePassword: '',
      fullName: '',
      nickname: '',
      birthDate: null,
      gender: '',
      phoneNumber: '',
      birthOrder: '',
      religion: '',
      ethnicity: '',
      domicile: '',
      maritalStatus: '',
      education: '',
      occupation: '',
      joinDate: '',
      endDate: '',
      profilePhoto: '',
      bankName: '',
      bankAccount: '',
      bankAccountName: '',
      childTo: null,
      totalSibling: null,
    },
  })

  useEffect(() => {
    if (token) {
      if (user?.firebaseCurrentUser) {
        const data = user?.firebaseCurrentUser
        reset({
          email: data?.email ?? '',
          profilePhoto: user.photoUrl ?? '',
          fullName: data?.displayName ?? '',
        })
      }
    } else {
      router.replace(Routes.Login)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  useEffect(() => {
    if (!!token) {
      if (isCompleteOnboard && userRole) {
        if (userRole === AuthRole.ADMIN) {
          router.push(Routes.AdminHome)
        }
        if (userRole === AuthRole.PSIKOLOG) {
          router.push(Routes.PsychologistHome)
        }
        if (userRole === AuthRole.CLIENT) {
          router.push(Routes.UserHome)
        }
      } else {
        router.push(Routes.Onboard)
      }
    }
  }, [token, isCompleteOnboard, router, userRole])

  async function onSubmit(data: OnboardType) {
    try {
      const payload = {
        password: data.password ?? '',
        fullName: data.fullName ?? '',
        nickname: data.nickname ?? '',
        birthDate: data.birthDate ? format(data.birthDate, INPUT_DATE_FORMAT) : '',
        gender: data.gender ?? '',
        phoneNumber: data.phoneNumber ?? '',
        religion: data.religion === Otheroptions ? (data.religionOther ?? '') : (data.religion ?? ''),
        ethnicity: data.ethnicity === Otheroptions ? (data.ethnicityOther ?? '') : (data.ethnicity ?? ''),
        domicile: data.domicile ?? '',
        maritalStatus: data.maritalStatus ?? '',
        education: data.education ?? '',
        occupation: data.occupation === Otheroptions ? (data.occupationOther ?? '') : (data.occupation ?? ''),
        // joinDate: data.joinDate ?? '',
        // endDate: data.endDate ?? '',
        profilePhoto: data.profilePhoto ?? '',
        bankName: data.bankName ?? '',
        bankAccount: data.bankAccount ?? '',
        bankAccountName: data.bankAccountName ?? '',
        birthOrder: `${data.childTo ?? '1'}/${data.totalSibling ?? '1'}`,
        sipp: '', // IMPORTANT => need update later for psikolog onboard
      }
      const userAuthOnboard = await authService.onboardingAuthPost(payload).then((response) => {
        return response
      })
      const refreshTokenResult = await refreshToken(userAuthOnboard?.token ?? '')
      const getIdTokenResult = await firebaseAuth.currentUser?.getIdTokenResult()

      toast({
        variant: 'success',
        title: 'Email berhasil terverifikasi, silakan login di aplikasi.',
      })
      dispatch(setShowEmailVerification(true))
      dispatch(
        setAuthInformation({
          token: refreshTokenResult?.token ?? (token || ''),
          role: String(getIdTokenResult?.claims?.role ? getIdTokenResult?.claims?.role : userRole),
          isCompleteOnboard: getIdTokenResult?.claims?.role ? true : isCompleteOnboard,
          id: getIdTokenResult?.claims?.id ? String(getIdTokenResult?.claims?.id) : user?.id,
        })
      )
    } catch (error) {
      toast({
        variant: 'danger',
        title: 'Terjadi kesalahan saat menyimpan data, silahkan ulangi beberapa saat lagi.',
      })
      // setErrorSubmit(t("Wrong email or password"));
      // dispatch(setLoadingBackdrop(false));
      // setIsLoading(false);
    } finally {
      // dispatch(setLoadingBackdrop(false));
      // setIsLoading(false);
    }
  }

  const handleNext = async () => {
    if (onBoardStep === 1) {
      const isValid = await trigger([
        'fullName',
        'nickname',
        'birthDate',
        'gender',
        'phoneNumber',
        'email',
        'password',
        'rePassword',
      ])
      if (isValid) {
        setOnBoardStep((prev) => prev + 1)
      } else {
        return
      }
    } else {
      handleSubmit(onSubmit)()
    }
  }

  const handleBack = () => {
    if (onBoardStep > 1) {
      setOnBoardStep((prev) => prev - 1)
    }
  }

  useEffect(() => {
    if (isCompleteOnboard) router.push(Routes.UserHome)
  }, [isCompleteOnboard, router])

  return (
    <WrapperAuth>
      <div className="grid gap-y-4">
        <Title
          title="Lengkapi Data"
          subTitle="Mulai langkah kecil Anda untuk lebih sehat mental dan menjadi versi terbaik diri. "
        />
        <SubTitle heading="Informasi Akun" subHeading={`Langkah ${onBoardStep} dari 2`} />
        {onBoardStep === 1 && (
          <OnboardStepOne
            register={register}
            getValues={getValues}
            setValue={setValue}
            errors={errors}
            onSubmit={() => trigger()}
          />
        )}
        {onBoardStep === 2 && (
          <OnboardStepTwo
            register={register}
            getValues={getValues}
            setValue={setValue}
            errors={errors}
            onSubmit={() => trigger()}
          />
        )}

        <div className="flex justify-end space-x-2">
          {onBoardStep > 1 && (
            <ButtonPrimary
              variant="outlined"
              size="xs"
              className="p-3 min-w-[140px] space-x-2"
              onClick={() => handleBack()}
              color="gray"
            >
              Kembali
            </ButtonPrimary>
          )}
          <ButtonPrimary
            isLoading={isSubmitting || isLoading}
            disabled={isSubmitting || isLoading}
            variant="contained"
            size="xs"
            className="p-3 min-w-[140px] space-x-2"
            onClick={() => handleNext()}
          >
            {onBoardStep === 1 ? 'Selanjutnya' : 'Simpan'}
          </ButtonPrimary>
        </div>
      </div>
    </WrapperAuth>
  )
}

export default OnboardComponent
