'use client'
import TabItem from '@/components/_common/tabs/TabItem'
import TabList from '@/components/_common/tabs/TabList'
import { Card } from '@/components/_common/ui'
import { ColumnDef } from '@tanstack/react-table'
import { HeaderContent } from '../HeaderContent'
import { DateRange } from 'react-day-picker'
import { useEffect, useRef, useState } from 'react'
import { ListingTestimony } from './ListingTestimony'
import { ADMIN_TESTIMONY_API } from '@/constans/API_PATH'
import { UserPhoto } from '../../UserPhoto/UserPhoto'
import { InfoDataDisplay } from '@/components/datagrid/InfoDataDisplay'
import Link from 'next/link'
import { AppMediumText } from '@/components/_common/ui'
import { useGetProfile } from '@/hooks/useGetProfile.hook'
import 'moment/locale/id'
import { useSelector } from '@/store'
import { formatStringToFulldateOutput, formatStringToStartEndTimeOutput } from '@/utils/displayDate'
import { defaultTimezone } from '@/constans/StaticOptions'

export const TestimonyMain = () => {
  const refRefetch = useRef<any>()
  const { testimony } = useSelector((state: any) => state.Meta)
  const [rangeDate, setRangeDate] = useState<DateRange | undefined>({ from: undefined, to: undefined })
  const [pageFilter, setPageFilter] = useState<any[]>([])

  const meta = testimony.meta

  const columns: ColumnDef<any>[] = [
    {
      accessorKey: 'id',
      header: 'Testimony ID',
      cell: ({ cell, row }) => {
        return (
          <Link
            className="underline hover:text-main-100"
            href={`/admin/client-report/${row.id}?name=${cell.row.original['psikolog']}&email=${cell.row.original['psikolog']}`}
          >
            {row.getValue('id')}
          </Link>
        )
      },
    },
    {
      accessorKey: 'rating',
      header: 'Rating',
      cell: ({ cell, row }) => {
        return <AppMediumText>{row.getValue('rating') ? `${row.getValue('rating')}/5` : '-'}</AppMediumText>
      },
    },
    {
      accessorKey: 'client',
      header: 'Klien',
      cell: ({ cell, row }) => {
        return (
          <div className="font-bold hover:underline hover:text-main-100">
            <UserPhoto
              photo={cell.row.original?.client?.profilePhoto}
              title={cell.row.original?.client?.fullName}
            />
          </div>
        )
      },
    },
    {
      accessorKey: 'psychologist',
      header: 'Psikolog',
      cell: ({ cell, row }) => {
        return (
          <div className="font-bold hover:text-main-100">
            <UserPhoto
              photo={cell.row.original?.psychologist?.profilePhoto}
              title={cell.row.original?.psychologist?.fullName}
              subTitle={
                cell.row.original?.psychologist?.field?.length
                  ? cell.row.original?.psychologist?.field?.map((val: any) => val.name).join(', ')
                  : 'Tidak Tersedia'
              }
            />
          </div>
        )
      },
    },
    {
      accessorKey: 'counseling',
      header: 'Jadwal',
      cell: ({ cell, row }) => {
        const date = cell.row.original?.counseling?.startTime
          ? formatStringToFulldateOutput(cell.row.original?.counseling?.startTime)
          : ''
        const time = cell.row.original?.counseling?.startTime
          ? formatStringToStartEndTimeOutput({
              date: cell.row.original?.counseling?.startTime,
              duration: cell.row.original?.counseling?.duration,
              timezone: defaultTimezone,
              timeLabel: 'WIB',
              isUTC: true,
            })
          : ''
        return <InfoDataDisplay title={date} subTitle={time} />
      },
    },
  ]

  const CounsellingData = [
    {
      label: `Tersedia (${meta?.additionalMeta?.complete ?? 0})`,
      content: (
        <ListingTestimony
          refRefetch={refRefetch}
          fetchPath={ADMIN_TESTIMONY_API}
          pageFilter={pageFilter}
          actions={['delete']}
          rangeDate={rangeDate}
          columns={columns as unknown as ColumnDef<any, any>}
        />
      ),
    },
    {
      label: `Menunggu Diisi (${meta?.additionalMeta?.incomplete ?? 0})`,
      content: (
        <ListingTestimony
          refRefetch={refRefetch}
          fetchPath={ADMIN_TESTIMONY_API}
          pageFilter={pageFilter}
          actions={['requestTestimony']}
          rangeDate={rangeDate}
          columns={columns as unknown as ColumnDef<any, any>}
        />
      ),
    },
  ]
  const [activeTabIndex, setActiveTabIndex] = useState<number>(0)

  useEffect(() => {
    refRefetch.current && refRefetch.current()
  }, [activeTabIndex])

  return (
    <>
      <HeaderContent title="Testimoni" />
      <Card className="border-0 p-0 xs:p-0 sm:border sm:p-6">
        <TabList
          className="sticky top-navbar z-30 bg-white"
          onClickTabs={(index) => {
            setActiveTabIndex(index)
            if (index === 0) {
              setPageFilter([{ key: 'rating', operator: 'in', value: [1, 2, 3, 4, 5] }])
            } else {
              setPageFilter([{ key: 'rating', operator: 'equal', value: null }])
            }
          }}
          activeTabIndex={activeTabIndex}
        >
          {CounsellingData.map((counselling, index) => {
            return (
              <TabItem key={index} label={counselling.label}>
                {counselling.content}
              </TabItem>
            )
          })}
        </TabList>
      </Card>
    </>
  )
}
