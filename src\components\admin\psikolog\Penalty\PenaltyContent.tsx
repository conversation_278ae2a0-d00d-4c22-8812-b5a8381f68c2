export default function PenaltyContent({ title, id, date }: { title: string; id: string; date: string }) {
  return (
    <div className="flex flex-col">
      <div className="flex items-center justify-between py-4 border-b border-[#EBEBEB]">
        <div className="flex flex-col gap-1">
          <span className="text-[#222222] text-[12px] md:text-[14px]">{title}</span>
          <div className="flex items-center gap-1">
            <span className="text-[#535353] text-[12px]">ID Konseling:</span>
            <span className="text-[#039EE9] text-[12px]">{id}</span>
          </div>
        </div>
        <span className="text-[#222222] text-[12px] md:text-[14px]">{date}</span>
      </div>
    </div>
  )
}
