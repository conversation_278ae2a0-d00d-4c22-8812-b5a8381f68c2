import { ReactNode } from 'react'
import { twMerge } from 'tailwind-merge'
import { ShowMore } from '../ShowMore/ShowMore'

export const LabelValue = ({
  label,
  value,
  displayRows,
  labelClass,
  valueClass,
}: {
  label: string
  value: string | ReactNode
  displayRows?: boolean
  labelClass?: string
  valueClass?: string
}) => {
  return (
    <>
      <div
        className={`${displayRows ? 'col-span-3' : 'col-span-1'} ${labelClass ?? 'text-body-lg font-medium text-gray-200'}`}
      >
        {label}
      </div>
      <div
        className={`${displayRows ? 'col-span-3' : 'col-span-2'} ${valueClass ?? 'text-body-lg font-medium text-gray-400'}`}
      >
        {typeof value === 'string' ? <ShowMore id="show-more-value" text={value} /> : value}
      </div>
    </>
  )
}
