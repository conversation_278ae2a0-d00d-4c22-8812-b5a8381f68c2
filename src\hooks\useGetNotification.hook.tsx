import { profileService } from '@/services/profile.service'
import { useQuery } from '@tanstack/react-query'

export const useGetNotification = () => {
  return useQuery({
    queryKey: ['Notification'],
    queryFn: () => {
      return profileService
        .getNotifications()
        .then((response) => {
          return response?.data
        })
        .catch((error) => {
          return null
        })
    },
  })
}
