import { useToast } from '@/components/ui/use-toast'
import { counsellingService } from '@/services/counselling.service'
import { dashboardService } from '@/services/dashboard.service'
import { useQuery } from '@tanstack/react-query'

export const useGetCounselingDetails = () => {
  const { toast } = useToast()
  return useQuery({
    queryKey: ['CounselingDetails'],
    queryFn: () =>
      dashboardService
        .adminGetTotalCounselingCounter()
        .then((response) => response)
        .catch((error) => {
          toast({
            description: 'Terjadi masalah dengan server, Silahkan hubungi Admin',
            variant: 'danger',
          })
        }),
  })
}
