import { useToast } from '@/components/ui/use-toast'
import { psychologistService } from '@/services/psychologist.service'
import { useSelector } from '@/store'
import { AuthRole } from '@/store/auth/auth.action'
import { useQuery } from '@tanstack/react-query'

export const usePsychologistField = () => {
  const { user } = useSelector((state) => state.Authentication)
  const { toast } = useToast()
  return useQuery({
    queryKey: ['Psychologist<PERSON><PERSON>'],
    queryFn: () => {
      if (user?.role === AuthRole.ADMIN || user?.role === AuthRole.SUPERADMIN) {
        return psychologistService
          .getPsychologistField()
          .then((response) => {
            return response?.map((val: any) => ({ ...val, label: val.name, value: val.id }))
          })
          .catch((error) => {
            toast({
              title: 'Gagal',
              description: '<PERSON><PERSON><PERSON><PERSON> masalah dengan server, Silahkan hubungi Admin',
              variant: 'danger',
            })
          })
      }
    },
  })
}
