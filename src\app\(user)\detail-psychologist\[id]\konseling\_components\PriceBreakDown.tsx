import ButtonPrimary from '@/components/_common/ButtonPrimary'
import { IIcons, SVGIcons } from '@/components/_common/icon'

interface PriceBreakdownProps {
  displayData: {
    price: number
    duration: number
    discount: number
    voucherPromo: number
    totalAfterDiscount: number
    finalPrice: number
  }
  voucherApplied: boolean
  isLoading: boolean
  openVoucherModal: () => void
  onRemoveVoucher: () => void
  onClickPayment: () => void
}

const PriceBreakdown = ({
  displayData,
  voucherApplied,
  isLoading,
  openVoucherModal,
  onRemoveVoucher,
  onClickPayment,
}: PriceBreakdownProps) => {
  return (
    <div className="p-6 border rounded-lg">
      <h2 className="font-semibold text-lg mb-6">Rincian <PERSON></h2>

      <div className="space-y-3 mb-6">
        <div className="flex justify-between">
          <p className="text-gray-500">Konseling {displayData.duration} menit</p>
          <p>Rp{displayData.price?.toLocaleString('id-ID')}</p>
        </div>

        <div className="flex justify-between text-red-500">
          <p>Diskon</p>
          <p>-Rp{displayData.discount?.toLocaleString('id-ID')}</p>
        </div>

        <div className="flex justify-between text-red-500">
          <p>Promo Voucher</p>
          <p>-Rp{displayData.voucherPromo?.toLocaleString('id-ID')}</p>
        </div>

        <div
          className="flex items-center justify-between mt-1 cursor-pointer text-main-100"
          onClick={openVoucherModal}
        >
          <div className="flex items-center">
            <SVGIcons name={IIcons.Document} className="mr-2" />
            <p className="text-sm">Lebih hemat pakai voucher</p>
          </div>
          <SVGIcons name={IIcons.ArrowRight} />
        </div>

        <div className="flex justify-between font-semibold pt-2">
          <p>Total</p>
          <p>Rp{displayData.totalAfterDiscount?.toLocaleString('id-ID')}</p>
        </div>

        <div className="flex justify-between text-gray-500 text-sm">
          <p>Admin Fee</p>
          <p className="text-green-500">Gratis</p>
        </div>

        <div className="flex justify-between font-bold text-lg">
          <p>Total Pembayaran</p>
          <p className="text-main-100">Rp{displayData.finalPrice?.toLocaleString('id-ID')}</p>
        </div>
      </div>

      {voucherApplied && (
        <div className="flex items-center mb-4 p-3 bg-main-50 rounded-lg">
          <div className="flex-shrink-0 text-main-100 mr-3">
            <SVGIcons name={IIcons.Repeat} className="h-6 w-6" />
          </div>
          <div className="flex-1">
            <p className="font-semibold">Voucher Promo berhasil dipakai!</p>
            <p className="text-gray-600">
              Hore, kamu hemat Rp{displayData.voucherPromo?.toLocaleString('id-ID')}
            </p>
          </div>
          <button className="text-gray-500" onClick={onRemoveVoucher}>
            <SVGIcons name={IIcons.Close} className="h-5 w-5" />
          </button>
        </div>
      )}

      <div className="mb-4 p-4 rounded-lg border flex items-center">
        <div className="flex-shrink-0 mr-3">
          <SVGIcons name={IIcons.Shield} className="text-main-100 h-6 w-6" />
        </div>
        <div className="flex-1">
          <p>Rahasia konselingmu 100% terjaga disini.</p>
          <p className="text-sm">
            Dengan ini kamu menyetujui{' '}
            <a href="#" className="text-main-100">
              syarat dan ketentuan
            </a>
          </p>
        </div>
      </div>

      <ButtonPrimary
        variant="contained"
        className="w-full py-6"
        onClick={onClickPayment}
        disabled={isLoading}
      >
        {isLoading ? 'Memproses...' : 'Pilih Pembayaran'}
      </ButtonPrimary>
    </div>
  )
}

export default PriceBreakdown
