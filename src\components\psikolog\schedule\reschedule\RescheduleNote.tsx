import ButtonPrimary from '@/components/_common/ButtonPrimary'
import { IIcons } from '@/components/_common/icon'
import AppInput from '@/components/_common/input/Input'
import { ListInformation } from '@/components/_common/ListInformation'
import { AppBigText } from '@/components/_common/ui'
import useGetPsychologistTimezone from '@/hooks/useGetPsychologistTimezone.hook'
import useGetTimezoneLabel from '@/hooks/useGetTimezone.hook'
import { dispatch, useSelector } from '@/store'
import { setRescheduleDetail, setRescheduleStep } from '@/store/psikolog/schedule.reducer'
import { formatStringToFulldateOutput, formatStringToStartEndTimeOutput } from '@/utils/displayDate'

export const RescheduleNote = ({
  clientName,
  counselingDuration,
  onSubmit,
}: {
  counselingDuration: number
  clientName: string
  onSubmit?: () => void
}) => {
  const { rescheduleDetail, rescheduleStep } = useSelector((state) => state.psikologSchedule)
  const { date, time } = rescheduleDetail

  const handleConfirm = () => {
    onSubmit && onSubmit()
  }

  const psychologistTimezone = useGetPsychologistTimezone()
  const timeZoneLabel = useGetTimezoneLabel()
  const dateLabel = formatStringToFulldateOutput(date ?? '')
  const timeLabel = formatStringToStartEndTimeOutput({
    date: date && time ? `${date} ${time}` : '',
    duration: counselingDuration,
    timezone: psychologistTimezone,
    timeLabel: timeZoneLabel,
    isUTC: true,
  })

  return (
    <>
      <div className="grid gap-2">
        <AppBigText>Pastikan jadwal konseling dengan {clientName} sudah sesuai</AppBigText>
        <ListInformation
          className="py-0 pb-2"
          listItem={[
            { label: dateLabel, icon: IIcons.Calendar },
            { label: timeLabel, icon: IIcons.Time },
          ]}
        />
      </div>
      <div className="grid grid-cols-4 grid-rows-1 gap-4 border-b border-line-200 pb-4 items-center">
        <span className="col-span-4 font-bold">Kirim pesan kepada {clientName}</span>
        <div className="col-span-4">
          <AppInput
            type="textarea"
            placeholder="Beritahukan alasan mengapa Anda harus mengubah jadwal konseling."
            value={rescheduleDetail.note || ''}
            onChange={(event) =>
              dispatch(setRescheduleDetail({ ...rescheduleDetail, note: event.target.value }))
            }
            rows={3}
          />
        </div>
      </div>
      <div className="flex justify-end items-center gap-2">
        <ButtonPrimary
          onClick={() => dispatch(setRescheduleStep(1))}
          className="rounded-sm"
          variant="outlined"
          size="xs"
          color="gray"
        >
          Kembali Pilih Jadwal
        </ButtonPrimary>
        <ButtonPrimary onClick={() => handleConfirm()} className="rounded-sm" variant="contained" size="xs">
          Ajukan Jadwal Baru
        </ButtonPrimary>
      </div>
    </>
  )
}
