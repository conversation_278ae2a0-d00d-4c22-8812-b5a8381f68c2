import { useToast } from '@/components/ui/use-toast'
import { dashboardService } from '@/services/dashboard.service'
import { useSelector } from '@/store'
import { AuthRole } from '@/store/auth/auth.action'
import { useQuery } from '@tanstack/react-query'
import { AnyNode } from 'postcss'

export const useGetDashboardSchedule = (startTime: string, endTime: string) => {
  const { user } = useSelector((state) => state.Authentication)
  const { toast } = useToast()
  return useQuery({
    queryKey: ['DashboardPsychologistSchedule', { startTime, endTime }],
    queryFn: () => {
      if (user?.role === AuthRole.PSIKOLOG) {
        return dashboardService
          .getCounselingByRangeTime(startTime, endTime)
          .then((response) => {
            let dataCounseling: any[] = []
            let scheduleStatus: any[] = []
            response.map((item: any) => {
              dataCounseling = [...dataCounseling, ...item.counselings]
              scheduleStatus = [
                ...scheduleStatus,
                { date: item.date, hasSchedule: item?.counselings?.length > 0 ? true : false },
              ]
            })
            return {
              scheduleStatus,
              scheduleList: dataCounseling,
              isNoCounselingSchedule: dataCounseling.length > 0 ? false : true,
            }
          })
          .catch((error) => {
            toast({
              title: 'Gagal',
              description: 'Terjadi masalah dengan server, Silahkan hubungi Admin',
              variant: 'danger',
            })
          })
      } else if (user?.role === AuthRole.ADMIN) {
        return dashboardService
          .adminGetCounselingByRangeTime(startTime, endTime)
          .then((response) => {
            let dataCounseling: any[] = []
            let scheduleStatus: any[] = []
            response.map((item: any) => {
              dataCounseling = [...dataCounseling, ...item.counselings]
              scheduleStatus = [
                ...scheduleStatus,
                { date: item.date, hasSchedule: item?.counselings?.length > 0 ? true : false },
              ]
            })
            return {
              scheduleStatus,
              scheduleList: dataCounseling,
              isNoCounselingSchedule: dataCounseling.length > 0 ? false : true,
            }
          })
          .catch((error) => {
            toast({
              title: 'Gagal',
              description: 'Terjadi masalah dengan server, Silahkan hubungi Admin',
              variant: 'danger',
            })
          })
      }
    },
  })
}
