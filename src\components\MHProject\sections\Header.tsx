import Image from 'next/image'
import React from 'react'

export const Header = ({ children, ilustration }: { children: React.ReactNode; ilustration: string }) => {
  return (
    <div className="flex -mt-[64px] w-full relative">
      <div className="h-[747px] md:h-[840px] w-full relative bg-[#E6F5FD]">
        <Image
          src={ilustration ?? '/ilustration/hero-header.svg'}
          alt="header"
          fill
          className="object-scale-down object-bottom md:object-cover md:object-center"
        />
      </div>
      <div className="z-1 absolute w-full h-full">
        <div className="flex w-full h-full justify-center mt-15 md:mt-0 md:items-center">{children}</div>
      </div>
    </div>
  )
}
