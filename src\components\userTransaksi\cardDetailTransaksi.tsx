'use client'

import React, { useState } from 'react'
import { IIcons, SVGIcons } from '@/components/_common/icon'
import InfoCardTransaksi from './infoCardTransaksi'
import ProfileCardTransaksi from './profileCardTransaksi'
import InfoDetail from './infoDetail'
import { format } from 'date-fns'
import { id } from 'date-fns/locale'
import { useMemo } from 'react'
import { useUpdateCounselingNote } from '@/hooks/useUpdateNoteCounseling'
import { useProblemCategories } from '@/app/(user)/detail-psychologist/hook/useGetProblemCategory'
import NotesModal from './UpdateNoteModal'

interface DetailTransactionData {
  id: string
  startTime: string
  endTime: string
  complaint: string
  expectation: string
  method: string
  duration: number
  createdAt: string
  problemCategory: Array<{
    problemCategory: string
  }>
  psychologist: {
    fullName: string
    profilePhoto: string
  }
}

interface CardDetailTransaksiProps {
  data: DetailTransactionData
}

export default function CardDetailTransaksi({ data }: CardDetailTransaksiProps) {
  const [accordionOpen, setAccordionOpen] = useState(false)
  const { categories: problemCategories, loading: loadingCategories } = useProblemCategories()
  const {
    isModalOpen,
    noteData,
    openModal,
    closeModal,
    updateNoteData,
    toggleCategory,
    clearNoteData,
    updateCounselingNote,
    isLoading,
  } = useUpdateCounselingNote(data.id)

  const formatDate = (date: string) => {
    return format(new Date(date), 'EEEE, d MMMM yyyy', { locale: id })
  }

  const formatTime = (date: string) => {
    return format(new Date(date), 'HH:mm', { locale: id }) + ' WIB'
  }

  const handleOpenUpdateModal = () => {
    openModal({
      problemCategory: data.problemCategory,
      complaint: data.complaint,
      expectation: data.expectation,
    })
  }

  const categoryOptions = useMemo(() => {
    return problemCategories.map((category) => ({
      id: category.id,
      name: category.problemCategory,
      icon: category.iconUrl || 'Add', // Fallback to Add icon if no iconUrl
    }))
  }, [problemCategories])

  const details = [
    {
      id: 1,
      title: 'Jenis Kegiatan',
      content: `Konseling - ${data.duration} Menit`,
    },
    {
      id: 2,
      title: 'Kategori Permasalahan',
      content: data.problemCategory.map((cat) => cat.problemCategory).join(', '),
    },
    {
      id: 3,
      title: 'Harapan Setelah Konseling',
      content: data.expectation,
    },
    {
      id: 4,
      title: 'Detail Keluhan',
      content: data.complaint,
    },
  ]

  return (
    <div
      className={`w-full bg-white border border-[#ebebeb] rounded-[15px] px-3 transition-all duration-500 ease-in-out ${
        accordionOpen ? '' : 'pb-3'
      }`}
    >
      <div
        onClick={() => setAccordionOpen(!accordionOpen)}
        className="bg-transparent cursor-pointer py-1 xl:py-0 relative z-10"
      >
        <div className="flex flex-col gap-2 w-full bg-transparent transition-all duration-500 ease-in-out py-3">
          <div className="flex justify-between items-center">
            <span className="font-bold text-[#222222] text-[16px]">Konseling dengan Psikolog</span>
            <SVGIcons
              className={`transition-all duration-500 ease-in-out ${
                accordionOpen ? 'rotate-0' : 'rotate-[180deg]'
              }`}
              name={IIcons.ArrowUp}
            />
          </div>
          <div className="flex flex-col gap-1">
            <InfoCardTransaksi icon="Calendar" title={formatDate(data.startTime)} />
            <InfoCardTransaksi
              icon="Time"
              title={`${formatTime(data.startTime)} - ${formatTime(data.endTime)}`}
            />
            <InfoCardTransaksi icon={data.method === 'VideoCall' ? 'Video' : 'Call'} title={data.method} />
          </div>
          <div className="flex flex-col gap-2 border-t border-[#ebebeb] pt-4 mt-4">
            <h3>Psikolog</h3>
            <ProfileCardTransaksi
              photo={data.psychologist.profilePhoto}
              nama={data.psychologist.fullName}
              job="Psi., Psikolog"
              flexDirection="flex-col md:flex-row"
            />
          </div>
        </div>

        <div
          className={`bg-transparent overflow-hidden transition-all duration-500 ease-in-out text-slate-600 ${
            accordionOpen ? 'max-h-[1000px] opacity-100 pb-3' : 'max-h-0 opacity-0'
          }`}
        >
          <div className="flex flex-col gap-4">
            {details.map((detail) => (
              <InfoDetail key={detail.id} title={detail.title} content={detail.content} />
            ))}

            {/* Ubah Catatan Konseling Button */}
            <button
              onClick={(e) => {
                e.stopPropagation()
                handleOpenUpdateModal()
              }}
              className="flex items-center gap-2 text-main-100 font-medium text-sm"
            >
              <SVGIcons name={IIcons.Edit} className="w-4 h-4" />
              Ubah Catatan Konseling
            </button>

            <span className="text-[14px] text-[#535353]">Jadwal dibuat: {formatDate(data.createdAt)}</span>
            <span className="text-[14px] text-[#535353]">ID Konseling: {data.id}</span>
          </div>
        </div>
      </div>

      {/* Notes Modal */}
      <NotesModal
        isOpen={isModalOpen}
        onClose={closeModal}
        notesData={{
          categories: noteData.problemCategory,
          expectations: noteData.expectation,
          feelings: noteData.complaint,
          description: '', // Not used in this implementation
        }}
        categoryOptions={categoryOptions}
        loadingCategories={loadingCategories}
        onInputChange={(field, value) => {
          if (field === 'expectations') {
            updateNoteData('expectation', value as string)
          } else if (field === 'feelings') {
            updateNoteData('complaint', value as string)
          }
        }}
        toggleCategory={(categoryId) => toggleCategory(categoryId)}
        onSave={updateCounselingNote}
        onClear={clearNoteData}
      />
    </div>
  )
}
