'use client'
import { useGetNotification } from '@/hooks/useGetNotification.hook'
import { NotificationItem } from '../navbar/NotificationItem'
import { useEffect } from 'react'
import { profileService } from '@/services/profile.service'

export default function NotifikasiBody() {
  const { data, refetch } = useGetNotification()

  useEffect(() => {
    async function readAllNotification() {
      try {
        await profileService.readAllNotifications()
        refetch()
      } catch (error) {
        console.log(error)
      }
    }
    readAllNotification()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  return (
    <>
      <div className="flex flex-col">
        {!data?.length ? (
          <div className="flex justify-center p-2 text-gray-200">Tidak ada notifikasi baru</div>
        ) : (
          data?.map((item: any, idx: number) => {
            return <NotificationItem isBodyItem key={idx} {...item} />
          })
        )}
      </div>
    </>
  )
}
