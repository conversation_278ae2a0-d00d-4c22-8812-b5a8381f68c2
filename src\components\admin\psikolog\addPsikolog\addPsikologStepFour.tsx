'use client'

import AppInput from '@/components/_common/input/Input'
import { UseFormGetValues, UseFormRegister, UseFormReturn, UseFormSetValue } from 'react-hook-form'

interface AddPsikologStepFourProps {
  pageNumberStep: number
  register: UseFormRegister<any>
  getValues: UseFormGetValues<any>
  setValue: UseFormSetValue<any>
  errors: UseFormReturn['formState']['errors']
  onSubmit: () => void
}
export default function AddPsikologStepFour({
  pageNumberStep,
  register,
  getValues,
  setValue,
  errors,
}: AddPsikologStepFourProps) {
  return (
    <>
      <div className="flex flex-col gap-[6px] mb-4">
        <h2 className="text-[26px] font-bold">Informasi Rekening</h2>
        <span className="text-[12px] text-[#737373]">Step {pageNumberStep} dari 4</span>
      </div>

      {/* nama bank */}
      <AppInput
        {...register('bankName')}
        className="pt-0"
        type="text"
        name="bankName"
        label="Nama Bank"
        placeholder="Contoh: BCA (Bank Central Asia)"
      />

      {/* No. Rekening */}
      <AppInput
        {...register('bankAccount')}
        className="pt-0"
        type="text"
        name="bankAccount"
        label="No. Rekening"
        placeholder="Masukkan Nomor Rekening"
      />

      {/* Atas Nama Rekening */}
      <AppInput
        {...register('bankAccountName')}
        className="pt-0"
        type="text"
        name="bankAccountName"
        label="Atas Nama Rekening"
        placeholder="Masukkan Nama Pemilik Rekening"
      />
    </>
  )
}
