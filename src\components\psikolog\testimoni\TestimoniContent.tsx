import { LabelValue } from '@/components/_common/CardInfo/LabelValue'
import { IIcons } from '@/components/_common/icon'
import { ListInformation } from '@/components/_common/ListInformation'
import { TestimonyItemProps, TestimonyListProps } from './TestimoniList'
import {
  formatStringToFulldateOutput,
  formatStringToFullDateTimeOutput,
  formatStringToStartEndTimeOutput,
  formatStringToTimeOutput,
} from '@/utils/displayDate'
import moment from 'moment'
import useGetPsychologistTimezone from '@/hooks/useGetPsychologistTimezone.hook'
import useGetTimezoneLabel from '@/hooks/useGetTimezone.hook'

export const TestimoniItemContent = ({ counseling, testimony, createdAt }: TestimonyItemProps) => {
  const psychologistTimezone = useGetPsychologistTimezone()
  const timeZoneLabel = useGetTimezoneLabel()
  const dateLabel = formatStringToFulldateOutput(counseling.startTime)
  const timeLabel = formatStringToStartEndTimeOutput({
    date: counseling?.startTime,
    duration: counseling?.duration,
    timezone: psychologistTimezone,
    timeLabel: timeZoneLabel,
    isUTC: true,
  })

  const createdDateTestimony = createdAt
    ? formatStringToFullDateTimeOutput({
        date: createdAt,
        timezone: psychologistTimezone,
        timeLabel: timeZoneLabel,
      })
    : '-'
  return (
    <div className="grid grid-cols-3 grid-rows-1 gap-y-4">
      <span className="col-span-3">
        <LabelValue
          displayRows
          labelClass="text-body-sm font-medium text-gray-300 mb-2"
          valueClass="font-medium text-gray-400"
          label="Konseling"
          value={
            <div className="grid ">
              <ListInformation
                className="border-b-0 py-0 gap-2"
                listItem={[
                  { label: dateLabel, icon: IIcons.Calendar },
                  { label: timeLabel, icon: IIcons.Time },
                  {
                    label: counseling.method === 'Call' ? 'Call' : 'Video Call',
                    icon: counseling.method === 'Call' ? IIcons.Call : IIcons.VideoCall,
                  },
                ]}
              />
            </div>
          }
        />
      </span>
      <span className="col-span-3">
        <LabelValue
          displayRows
          labelClass="text-body-sm font-medium text-gray-300"
          valueClass="font-medium text-gray-400"
          label="Testimoni"
          value={testimony}
        />
      </span>
      <LabelValue
        displayRows
        labelClass="text-body-sm font-medium text-gray-300"
        valueClass="font-medium text-gray-400"
        label={`Testimoni diberikan pada: ${createdDateTestimony}`}
        value=""
      />
    </div>
  )
}
