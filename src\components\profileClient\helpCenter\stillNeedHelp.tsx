import ButtonPrimary from '@/components/_common/ButtonPrimary'
import Link from 'next/link'

export default function StillNeedHelp() {
  return (
    <div className="flex fixed bottom-0 right-0 md:relative justify-center items-center w-full mt-[78px] md:mt-2">
      <div className="flex justify-center items-center w-full">
        <div className="w-full md:w-auto bg-white px-4 py-[18px] flex justify-between md:justify-normal gap-5 rounded-t-[15px] md:rounded-[15px] border border-[#EBEBEB] md:border-0 shadow-md drop-shadow-md shadow-[#ebebeb] overflow-hidden">
          {/* img */}
          <figure className="w-[64px] relative hidden md:flex">
            <img src="/ilustration/call-me.svg" alt="" className="w-full scale-x-[-1] absolute -bottom-5" />
          </figure>
          {/* text */}
          <div className="flex flex-col justify-center">
            <span className="font-bold md:font-medium">Masih perlu bantuan?</span>
            <span className="text-[#535353] text-[11px] md:text-[14px] md:text-inherit">
              Buat pesan untuk Admin Healing.
            </span>
          </div>
          {/* button */}
          <Link
            href={
              'https://api.whatsapp.com/send/?phone=6285173025865&text=Hi+Mental+Healing%2C+saya+perlu+bantuan&type=phone_number&app_absent=0'
            }
            className="underline text-white"
            target="_blank"
            passHref
          >
            <ButtonPrimary className="min-w-[143px] rounded-full" variant={'outlined'} size="xs">
              Buat Pesan
            </ButtonPrimary>
          </Link>
        </div>
      </div>
    </div>
  )
}
