import React, { useState, useEffect } from 'react'
import Link from 'next/link'
import { usePathname, useRouter } from 'next/navigation'
import { IIcons, SVGIcons } from '../_common/icon'
import { MenuItemProps } from '@/constans/SidebarMenu'
import { isActiveLink } from '@/utils/linkActiveChecker'
import { Accordion, AccordionItem, AccordionTrigger, AccordionContent } from '../ui/accordion'

type SidebarItemProps = {
  item: MenuItemProps
  pageName: string
  setPageName: (value: any) => void
  // onClickMenu: () => void
  // menuActive: string
}

const SidebarItem = ({ item, pageName, setPageName }: SidebarItemProps) => {
  const [menuActive, setMenuActive] = useState<string>('')
  const handleClick = (selectedMenu: MenuItemProps) => {
    setMenuActive((prev) => (prev === selectedMenu.id ? '' : selectedMenu.id ?? ''))
  }

  const pathname = usePathname()

  const isActive = (item: any) => {
    const active = isActiveLink(pathname, item.route)
    if (active) return true
    if (item.subMenu) {
      return item.subMenu.some((child: any) => isActive(child))
    }
    return false
  }

  useEffect(() => {
    const checkIsSubMenuActive = item.subMenu && item.subMenu?.some((child: any) => isActive(child))
    if (checkIsSubMenuActive) {
      setMenuActive(item.id ?? '')
    } else {
      setMenuActive('')
    }
  }, [pathname])

  return (
    <>
      <Link
        href={item.route}
        onClick={() => handleClick(item)}
        className={`${isActive(item) ? 'text-main-100 stroke-main-100 font-bold' : 'text-gray-200'} group flex flex-row items-center py-1.5 px-6 text-sm focus:outline-nones transition-colors duration-200 rounded-lg`}
      >
        <SVGIcons name={item.icon} className="w-6 group-hover:stroke-main-100" />
        <span className="group-hover:text-main-100 group-hover:font-bold ml-2">{item.label}</span>
        {item.subMenu?.length && (
          <div className="flex grow justify-end">
            <SVGIcons
              className={`transition-all duration-500 ease-in-out ${menuActive === item.id ? 'rotate-[180deg]' : 'rotate-0'}`}
              name={IIcons.ArrowDown}
            />
          </div>
        )}
      </Link>
      {item.subMenu?.length ? (
        <>
          <Accordion key={item.id} value={menuActive} type="single" collapsible className="py-0 !my-0">
            <AccordionItem
              key={item.id}
              value={item.id!}
              className={`transition-colors duration-200 border-b-0 w-full`}
            >
              <AccordionContent className="border-b-0 grid gap-y-4 pt-4 w-full">
                {item.subMenu.map((subMenuItem) => {
                  return (
                    <Link
                      key={subMenuItem.route}
                      href={subMenuItem.route}
                      className={`${isActive(subMenuItem) ? 'text-main-100 stroke-main-100 font-bold' : 'text-gray-200'} group/menu flex items-center text-sm focus:outline-nones transition-colors duration-200 rounded-lg text-left px-6`}
                    >
                      <span className="group-hover/menu:text-main-100 group-hover/menu:font-bold pl-8">
                        {subMenuItem.label}
                      </span>
                    </Link>
                  )
                })}
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </>
      ) : null}
    </>
  )
}

export default SidebarItem
