'use client'

import JadwalConselingAdmin from './cards/jadwalConselingAdmin'
import TotalCardAdmin from './cards/TotalCardAdmin'
import CardSemuaJadwalAdmin from './cards/cardSemuaJadwalAdmin'
import { useGetProfile } from '@/hooks/useGetProfile.hook'
import { useGetUserDashboard } from './hooks/useGetUserDashboard'
import { useGetTotalCounseling } from './hooks/useGetTotalCounseling'
import { useGetTestimonyCounter } from './hooks/useGetTestimonyCounter'
import { AuthRole } from '@/store/auth/auth.action'
import { useSelector } from '@/store'

const AdminPageComponent = () => {
  const { user } = useSelector((state) => state.Authentication)
  const { data: Profile } = useGetProfile()
  const { data: User } = useGetUserDashboard()
  const { data: Counseling } = useGetTotalCounseling()
  const { data: Testimony } = useGetTestimonyCounter()
  const isSuperAdmin = user?.role === AuthRole.SUPERADMIN

  return (
    <>
      <h1 className="text-[#222222] text-[20px] md:text-[32px] font-bold md:mb-0 p-4 md:p-6 2xl:p-8">
        Salam sehat mental, <span className="block md:inline"></span>Kak {Profile?.nickname}!
      </h1>

      <div className="flex flex-col md:flex-row gap-4 lg:gap-6 px-4 lg:px-6">
        {/* main bar */}
        <div className="w-full md:w-3/5 flex flex-col gap-6 order-2 md:order-1 pb-6">
          <JadwalConselingAdmin />
          <CardSemuaJadwalAdmin />
        </div>
        {/* side bar */}
        <div className="w-full md:w-2/5 flex flex-col gap-6 order-1 md:order-2">
          {/* mobile */}
          <div className="md:hidden flex items-start gap-4 overflow-x-scroll">
            {isSuperAdmin && (
              <div>
                <TotalCardAdmin
                  borderColor="border-[#039EE9]"
                  title="Total Pendapatan"
                  midTitle="Rp.1.000"
                  midTitleColor="text-[#039EE9]"
                  bottomSubStyle="hidden"
                />
              </div>
            )}
            <div>
              <TotalCardAdmin
                borderColor="border-[#EBEBEB]"
                title="Total User"
                midTitle="1000"
                midTitleColor="text-[#222222]"
                bottomSubStyle="inline"
              />
            </div>
          </div>
          {/* desktop */}
          <div className="hidden md:flex flex-col gap-6">
            {isSuperAdmin && (
              <TotalCardAdmin
                borderColor="border-[#039EE9]"
                title="Total Pendapatan"
                midTitle="Rp.1.000"
                midTitleColor="text-[#039EE9]"
                bottomSubStyle="hidden"
              />
            )}
            {isSuperAdmin && (
              <TotalCardAdmin
                borderColor="border-[#EBEBEB]"
                title="Total User"
                midTitle={User?.total ?? 0}
                note={
                  <>
                    {User?.totalLast7Days > 0 ? (
                      <span className="font-bold">+{User?.totalLast7Days ?? 0}</span>
                    ) : (
                      'Tidak ada data baru'
                    )}{' '}
                    dari 7 hari terakhir
                  </>
                }
                midTitleColor="text-[#222222]"
                bottomSubStyle="inline"
              />
            )}
            <TotalCardAdmin
              borderColor="border-[#EBEBEB]"
              title="Total Konseling"
              midTitle={Counseling?.total ?? 0}
              midTitleColor="text-[#222222]"
              bottomSubStyle="hidden"
            />
            <TotalCardAdmin
              borderColor="border-[#EBEBEB]"
              title="Total Testimoni"
              midTitle={Testimony?.total ?? 0}
              midTitleColor="text-[#222222]"
              bottomSubStyle="hidden"
            />
          </div>
        </div>
      </div>
    </>
  )
}

export default AdminPageComponent
