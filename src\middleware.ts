import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { Routes } from './constans/routes'
import { AuthRole } from './store/auth/auth.action'

export default function middleware(req: NextRequest) {
  const token = req.cookies.get('auth_token')?.value
  const role = req.cookies.get('auth_role')?.value
  const auth_token = Buffer.from(token ?? '', 'base64').toString('ascii')
  const auth_role = Buffer.from(role ?? '', 'base64').toString('ascii')
  const isAuthenticated = !!auth_token && !!auth_role
  const isSuperAdmin = auth_role === AuthRole.SUPERADMIN
  const isAdmin = auth_role === AuthRole.ADMIN
  const isPsikolog = auth_role === AuthRole.PSIKOLOG
  if (
    !isAuthenticated &&
    (req.nextUrl.pathname.startsWith('/admin') ||
      req.nextUrl.pathname.startsWith('/psychologist') ||
      req.nextUrl.pathname.startsWith('/user') ||
      req.nextUrl.pathname.startsWith('/profile') ||
      req.nextUrl.pathname.startsWith('/transaksi'))
  ) {
    const absoluteURL = new URL(Routes.Login, req.nextUrl.origin)
    return NextResponse.redirect(absoluteURL.toString())
  } else {
    if (!isAdmin && !isSuperAdmin && req.nextUrl.pathname.startsWith('/admin')) {
      const absoluteURL = new URL(Routes.Login, req.nextUrl.origin)
      return NextResponse.redirect(absoluteURL.toString())
    }
    if (!isPsikolog && req.nextUrl.pathname.startsWith('/psychologist')) {
      const absoluteURL = new URL(Routes.Login, req.nextUrl.origin)
      return NextResponse.redirect(absoluteURL.toString())
    }
  }
}
