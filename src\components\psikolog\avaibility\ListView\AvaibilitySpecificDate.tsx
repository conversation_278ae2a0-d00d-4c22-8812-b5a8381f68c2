import ButtonPrimary from '@/components/_common/ButtonPrimary'
import { formatDate } from 'date-fns'
import { SVGIcons, IIcons } from '@/components/_common/icon'
import { H2 } from '@/components/_common/ui'
import AvaibilityDateTimePicker from './AvaibilityDateTimePicker'
import { useState } from 'react'

import { displayLocalDate } from '@/utils/displayDate'
import { SpecificDateProps } from '@/store/psikolog/avaibility.reducer'
import { avaibilityService, PayloadSepcificAvaibility } from '@/services/avaibility.service'
import { useToast } from '@/components/ui/use-toast'
import { INPUT_DATE_FORMAT } from '@/constans/date'

export const AvaibilitySpecificDate = ({
  specificDateAvaibility,
  refetch,
}: {
  specificDateAvaibility: SpecificDateProps[]
  refetch?: (options?: any) => void
}) => {
  const { toast } = useToast()
  const [toggle, setToggle] = useState<boolean>(false)
  const [itemSelected, setItemSelected] = useState<SpecificDateProps | null>(null)

  const handleToggle = () => setToggle((prev) => !prev)

  const handleSubmit = async (payload: PayloadSepcificAvaibility[]) => {
    try {
      if (itemSelected) {
        await avaibilityService.updateBatchAvaibility(
          payload,
          formatDate(itemSelected.date, INPUT_DATE_FORMAT)
        )
        toast({
          title: 'Berhasil',
          description: 'Jadwal spesifik berhasil ditambahkan',
          variant: 'success',
        })
        setItemSelected(null)
      } else {
        await avaibilityService.createBatchAvaibility(payload)
        toast({
          title: 'Berhasil',
          description: 'Jadwal spesifik berhasil ditambahkan',
          variant: 'success',
        })
        setItemSelected(null)
      }
      refetch && refetch()
    } catch (error: any) {
      const errorMessage =
        error?.response?.data?.message ||
        error?.message ||
        'Terjadi kesalahan saat menghapus jadwal spesifik. Silahkan coba lagi'
      toast({
        title: 'Gagal',
        description: errorMessage,
        variant: 'danger',
      })
    }
  }

  const handleEditItem = (date: SpecificDateProps) => {
    if (date) {
      setItemSelected(date)
      setToggle(true)
    }
  }
  const handleRemoveItem = async (val: string) => {
    try {
      const dateUTCFormat = val ? val + 'T00:00:00.000Z' : ''
      await avaibilityService.removeAvaibilityByDate(dateUTCFormat)
      refetch && refetch()
    } catch (error: any) {
      const errorMessage =
        error?.response?.data?.message ||
        error?.message ||
        'Terjadi kesalahan saat menghapus jadwal spesifik. Silahkan coba lagi'
      toast({
        title: 'Gagal',
        description: errorMessage,
        variant: 'danger',
      })
    }
  }

  return (
    <>
      <div className="grid">
        <div className="grid">
          <div className="flex flex-col md:flex-row items-start justify-between gap-2">
            <div className="flex flex-col flex-start gap-y-2">
              <H2>Jadwal Spesifik</H2>
              <span className="text-body-sm font-medium text-gray-200">
                Jadwal ini maka akan menggantikan jadwal mingguan Anda. Waktu Indonesia Bagian Barat (WIB)
              </span>
            </div>
            <ButtonPrimary
              className="w-full md:w-auto"
              variant="outlined"
              size="xs"
              onClick={() => {
                setItemSelected(null)
                handleToggle()
              }}
            >
              Tambah
            </ButtonPrimary>
          </div>
        </div>
        <div className="grid">
          {specificDateAvaibility &&
            specificDateAvaibility.map((sepcificDate) => {
              return (
                <div
                  key={sepcificDate.date}
                  className="flex flex-wrap flex-row items-start py-6 min-h-[22px] w-full"
                >
                  <div className="flex flex-1 justify-between space-x-2 w-full sm:w-3/12">
                    <div className="flex flex-1 items-center space-x-2 h-[22px] w-full sm:w-3/12">
                      <span className="font-medium text-[12px] xs:text-[14px] md:text-[16px]">
                        {sepcificDate.date ? displayLocalDate(new Date(sepcificDate.date)) : '-'}
                      </span>
                    </div>
                    <div className="grid items-center flex-1">
                      {sepcificDate?.availability?.length &&
                        sepcificDate?.availability.map((time) => {
                          return (
                            <div key={time.id} className="flex flex-row gap-2 items-center justify-start">
                              <span className="font-medium text-[12px] xs:text-[14px] md:text-[16px]">
                                {time.startTime}
                              </span>
                              <SVGIcons name={IIcons.Dash} />
                              <span className="font-medium text-[12px] xs:text-[14px] md:text-[16px]">
                                {time.endTime}
                              </span>
                            </div>
                          )
                        })}
                    </div>
                  </div>
                  <div className="flex">
                    <span className="cursor-pointer" onClick={() => handleEditItem(sepcificDate)}>
                      <SVGIcons name={IIcons.Edit} />
                    </span>
                    <span className="cursor-pointer" onClick={() => handleRemoveItem(sepcificDate.date)}>
                      <SVGIcons name={IIcons.Close} />
                    </span>
                  </div>
                </div>
              )
            })}
        </div>
      </div>
      <AvaibilityDateTimePicker
        id={itemSelected?.date} //udpate later id or date
        date={!!itemSelected?.date ? new Date(itemSelected.date) : undefined}
        timeList={itemSelected?.availability || []}
        open={toggle}
        onOpen={handleToggle}
        onSubmit={(payload: PayloadSepcificAvaibility[]) => handleSubmit(payload)}
      />
    </>
  )
}
