import { DataGrid } from '@/components/datagrid/DataTable'
import { InfoDataDisplay } from '@/components/datagrid/InfoDataDisplay'
import useGetPsychologistTimezone from '@/hooks/useGetPsychologistTimezone.hook'
import useGetTimezoneLabel from '@/hooks/useGetTimezone.hook'
import { formatStringToFulldateOutput, formatStringToStartEndTimeOutput } from '@/utils/displayDate'
import { ColumnDef } from '@tanstack/react-table'
import ReportIcon from '@/assets/icons/report-blue.svg'
import { AppSheet } from '@/components/_common/Sheet/AppSheet'
import ClientReportDetailsHeader from '@/components/admin/ClientReport/details/ClientReportDetailsHeader'
import { useState } from 'react'
import ClientReportDetails from './ClientReportDetails'

export const ListingCounselling = ({ id }: { id: string }) => {
  const psychologistTimezone = useGetPsychologistTimezone()
  const timeZoneLabel: string = useGetTimezoneLabel()
  const [toggle, setToggle] = useState<boolean>(false)
  const [selected, setSelected] = useState<any>(null)
  const toggleSheet = () => {
    setToggle((prev) => !prev)
  }
  const handleClickRow = (item: any) => {
    console.log(item)
    setToggle(true)
    setSelected(item)
  }

  const columns: ColumnDef<any>[] = [
    {
      accessorKey: 'id',
      header: 'Konseling ID',
    },
    {
      accessorKey: 'startTime',
      header: 'Jadwal',
      cell: ({ cell, row }) => {
        const dateLabel = formatStringToFulldateOutput(row?.original?.startTime)
        const timeLabel = formatStringToStartEndTimeOutput({
          date: row.original?.startTime,
          duration: row.original?.duration,
          timezone: psychologistTimezone,
          timeLabel: timeZoneLabel,
          isUTC: true,
        })
        return <InfoDataDisplay title={dateLabel} subTitle={timeLabel} />
      },
    },
    {
      accessorKey: 'clientReport',
      header: 'Klien Report',
      cell: ({ cell, row }) => {
        if (row.original?.clientReport === null) {
          return <div className="flex items-center gap-2 font-bold text-gray-100">Report belum tersedia</div>
        }
        return (
          <div
            className="flex items-center gap-2 cursor-pointer font-bold text-main-100"
            onClick={() => handleClickRow(row.original)}
          >
            <ReportIcon />
            Baca Klien Report
          </div>
        )
      },
    },
  ]

  return (
    <div className="flex flex-col">
      <DataGrid
        fetchPath={`api/psychologists/counseling`}
        bulkFilter={`where={"psychologistId":{"not":"null"},"status":{"notIn":["EXPIRED_PAYMENT"]},"clientId":"${id}"}`}
        columns={columns as unknown as ColumnDef<any, any>[]}
        hideAction={true}
      />
      <AppSheet
        open={toggle}
        side="right"
        onClose={toggleSheet}
        title="Klien Report Detail"
        className="!max-w-screen md:!max-w-[50vw] px-4 pt-[22px] pb-6"
      >
        <div className="mt-6 flex flex-col gap-4 p-6 rounded-[15px] border border-[#EBEBEB]">
          <ClientReportDetailsHeader
            client={selected?.client?.nickname ?? ''}
            psikolog={selected?.psychologist?.nickname ?? ''}
            date={selected?.startTime ? formatStringToFulldateOutput(selected?.startTime) : ''}
            time={
              selected?.startTime
                ? formatStringToStartEndTimeOutput({
                    date: selected?.startTime,
                    duration: selected?.duration,
                    timezone: psychologistTimezone,
                    timeLabel: timeZoneLabel,
                    isUTC: true,
                  })
                : ''
            }
            via={selected?.method === 'Call' ? 'Call' : 'Video Call'}
          />
          <ClientReportDetails report={selected?.clientReport} />
        </div>
      </AppSheet>
    </div>
  )
}
