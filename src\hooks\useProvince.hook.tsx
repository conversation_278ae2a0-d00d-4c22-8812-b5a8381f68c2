import { useToast } from '@/components/ui/use-toast'
import { regionService } from '@/services/region.service'
import { capitalizeEachWord } from '@/utils/stringUtils'
import { useQuery } from '@tanstack/react-query'

export const useProvince = () => {
  const { toast } = useToast()
  return useQuery({
    queryKey: ['Province'],
    queryFn: () => {
      return regionService
        .getProvince()
        .then((response) => {
          const payload = response.map((val: any) => ({
            ...val,
            label: capitalizeEachWord(val.name),
            value: val.id,
          }))
          return payload
        })
        .catch((error) => {
          toast({
            title: 'Gagal',
            description: '<PERSON><PERSON><PERSON>di masalah dengan server, Silahkan hubungi Admin',
            variant: 'danger',
          })
        })
    },
  })
}
