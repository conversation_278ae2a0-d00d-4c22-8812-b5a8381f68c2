'use client'

import { DataGrid } from '@/components/datagrid/DataTable'
import { getDummy } from '@/utils/getDummyData'
import { ColumnDef } from '@tanstack/react-table'
import { useRouter } from 'next/navigation'
import { DateRange } from 'react-day-picker'
import { FilterHeader } from '../FilterHeader'
import { useMemo, useState } from 'react'
import { AppSheet } from '@/components/_common/Sheet/AppSheet'
import ClientReportDetailsHeader from './details/ClientReportDetailsHeader'
import ClientReportDetails from './details/ClientReportDetails'
import { useDispatch } from '@/store'
import { setMetaClientReport } from '@/store/admin/meta.reducer'
import { ConfirmModal } from '@/components/_common/ConfirmModal/ConfirmModal'
import { clientReportService } from '@/services/clientReport.service'
import { useToast } from '@/components/ui/use-toast'
import { Routes } from '@/constans/routes'
import { formatStringToFulldateOutput, formatStringToStartEndTimeOutput } from '@/utils/displayDate'

type ListingClientReporttype = {
  columns: ColumnDef<any, any>
  rangeDate: DateRange | undefined
  actions: string[]
  showSecondFilter?: boolean
  fetchPath: string
  refRefetch: any
  pageFilter?: any[]
  setMeta?: (val: any) => void
}

export const ListingClientReport = ({
  columns,
  rangeDate,
  actions = [],
  showSecondFilter,
  fetchPath,
  refRefetch,
  pageFilter,
  setMeta,
}: ListingClientReporttype) => {
  const { toast } = useToast()
  const router = useRouter()
  const dispatch = useDispatch()
  const [deleteId, setDeleteId] = useState<string | null>(null)
  const [openModal, setOpenModal] = useState<boolean>(false)
  const [toggle, setToggle] = useState<boolean>(false)
  const [selected, setSelected] = useState<any>(null)

  const Update = useMemo(
    () => ({
      name: 'update',
      icon: null,
      label: 'Atur',
      onClick: (item: any) => {
        handleUpdate(item?.original?.client?.fullName, item?.original?.id)
      },
    }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  )
  const Delete = useMemo(
    () => ({
      name: 'delete',
      icon: null,
      label: 'Hapus',
      onClick: (item: any) => {
        setDeleteId(item?.original?.id)
      },
    }),
    []
  )
  const Reminder = useMemo(
    () => ({
      name: 'reminder',
      icon: null,
      label: 'Ingatkan Psikolog',
      onClick: (item: any) => {
        handleReminder(item?.original?.id)
      },
    }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  )

  const actionsMenu = (row: any) => {
    let actionsMenus: any[] = []
    if (actions.includes('update')) {
      actionsMenus = [...actionsMenus, Update]
    }
    if (actions.includes('delete')) {
      actionsMenus = [...actionsMenus, Delete]
    }
    if (actions.includes('reminder')) {
      actionsMenus = [...actionsMenus, Reminder]
    }
    return actionsMenus
  }

  const toggleSheet = () => {
    setToggle((prev) => !prev)
  }

  const handleClickRow = (item: any) => {
    setToggle(true)
    setSelected(item)
  }

  const handleDelete = async () => {
    if (!deleteId) {
      toast({
        variant: 'danger',
        title: 'Id tidak ditemukan',
      })
      return
    }
    try {
      await clientReportService.adminDeleteClientReport(deleteId ?? '')
      toast({
        variant: 'success',
        title: 'Klien report berhasil dihapus',
      })
      refRefetch.current()
      setTimeout(() => {
        setDeleteId(null)
        setToggle(false)
      }, 500)
    } catch (error) {
      toast({
        variant: 'danger',
        title: 'Klien report gagal dihapus',
      })
    }
  }
  const handleReminder = async (id: string) => {
    if (!id) {
      toast({
        variant: 'danger',
        title: 'Id tidak ditemukan',
      })
      return
    }
    try {
      await clientReportService.adminRemindPsychologistClientReport(id)
      toast({
        variant: 'success',
        title: 'Email remider untuk isi klien report berhasil terkirim',
      })
      refRefetch.current()
    } catch (error) {
      toast({
        variant: 'danger',
        title: 'Email remider untuk isi klien report gagal terkirim',
      })
    }
  }

  const handleUpdate = (name: string, reportId: string) => {
    router.push(`${Routes.AdminCreateClientReport}?name=${name}&reportId=${reportId}`)
  }

  return (
    <>
      <div className="flex flex-col">
        <div className="flex gap-x-5 mb-4 justify-between md:justify-start">
          <FilterHeader onChangeSearch={(val) => console.log(val)} />
        </div>
        <DataGrid
          refetchRef={refRefetch}
          fetchPath={fetchPath}
          actionMenuList={actionsMenu}
          columns={columns as unknown as ColumnDef<any, any>[]}
          onClickRow={(item) => handleClickRow(item)}
          pageFilter={pageFilter}
          setMeta={(meta) => dispatch(setMetaClientReport(meta ? meta : null))}
        />
      </div>
      {!!deleteId && (
        <ConfirmModal
          isOpenModal
          title="Hapus klien report?"
          content="Data akan terhapus secara permanen."
          onCancel={() => setDeleteId(null)}
          onYes={() => {
            handleDelete()
          }}
        />
      )}
      <AppSheet
        open={toggle}
        side="right"
        onClose={toggleSheet}
        title="Klien Report Detail"
        className="!max-w-screen md:!max-w-[50vw] px-4 pt-[22px] pb-6"
      >
        <div className="mt-6 flex flex-col gap-4 p-6 rounded-[15px] border border-[#EBEBEB]">
          <ClientReportDetailsHeader
            client={selected?.client?.nickname}
            psikolog={selected?.psychologist?.nickname}
            date={
              selected?.counseling?.startTime
                ? formatStringToFulldateOutput(selected?.counseling?.startTime)
                : ''
            }
            time={
              selected?.counseling?.startTime
                ? formatStringToStartEndTimeOutput({
                    date: selected?.counseling?.startTime,
                    duration: selected?.counseling?.duration,
                    timezone: 'Asia/Jakarta',
                    isUTC: true,
                  })
                : ''
            }
            via={selected?.counseling?.method === 'Call' ? 'Call' : 'Video Call'}
          />
          <ClientReportDetails
            report={selected}
            onDelete={() => {
              setDeleteId(selected?.id)
            }}
            onUpdate={() => handleUpdate(selected?.client?.fullName, selected?.id)}
          />
        </div>
      </AppSheet>
    </>
  )
}
