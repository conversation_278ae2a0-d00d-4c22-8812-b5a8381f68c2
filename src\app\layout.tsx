import { GoogleAnalytics } from '@next/third-parties/google'
import type { Metadata } from 'next'
import { Toaster } from '@/components/ui/toaster'
import { quicksand } from '@/utils/fonts'
import './globals.css'
import StoreProvider from './StoreProvider'
import Providers from '@/components/Providers'
import { BookingProvider } from '@/context/useBookingCounseling'

export const metadata: Metadata = {
  title: 'Mental Healing',
  description: 'Generated by mental healing',
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <StoreProvider>
      <html lang="en" className="scroll-smooth">
        <body className={`overflow-y-scroll overflow-x-auto ${quicksand.className}`}>
          <Providers>
            <BookingProvider>{children}</BookingProvider>
          </Providers>
          <Toaster />
        </body>
        <GoogleAnalytics gaId={process.env.NEXT_MEASUREMENT_ID ?? ''} />
      </html>
    </StoreProvider>
  )
}
