import { IIcons, SVGIcons } from '@/components/_common/icon'
import AppInput from '@/components/_common/input/Input'

export default function FilterRiwayatSaldo() {
  return (
    <div className="flex flex-col gap-4">
      {/* Filter Header */}
      <div className="flex flex-col md:flex-row gap-2 md:gap-0 justify-between">
        <div className="flex items-center gap-3">
          {/* search */}
          <AppInput
            type="text"
            placeholder="Cari disini.."
            value=""
            onChange={() => {}}
            errorMsg={''}
            prefixIcon={IIcons.Search}
          />
          {/* dropdown */}
          <div className="relative">
            <input
              type="text"
              placeholder="Semua Jenis Transaksi"
              name=""
              id=""
              className="w-[210px] h-[48px] py-3 px-4 rounded-[15px] border border-[#EBEBEB] focus:border-transparent focus:outline-none focus:ring focus:ring-main-100 focus:ring-opacity-40 text-[#737373] placeholder-[#737373] text-[14px]"
            />
            <SVGIcons name={IIcons.ArrowDown} className="w-6 h-6 absolute right-4 bottom-3" />
          </div>
        </div>
        <div className="h-[48px] w-fit py3 px-4 rounded-[15px] border border-[#EBEBEB] flex items-center gap-2">
          <SVGIcons name={IIcons.Calendar} className="w-6 h-6" />
          <span className="text-[#222222] text-[14px]">10 Jul - 17 Jul 2024</span>
        </div>
      </div>
    </div>
  )
}
