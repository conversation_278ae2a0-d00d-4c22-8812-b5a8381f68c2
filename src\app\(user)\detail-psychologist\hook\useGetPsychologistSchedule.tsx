// import { psychologistService } from '@/services/psychologist.service'
// import { useState, useEffect } from 'react'

// export const useGetPsychologistSchedule = (psychologistId: string) => {
//   const [schedule, setSchedule] = useState(null)
//   const [loadingSchedule, setLoadingSchedule] = useState(true)
//   const [errorSchedule, setErrorSchedule] = useState(null)

//   const fetchPsychologistSchedule = async () => {
//     try {
//       setLoadingSchedule(true)
//       const response = await psychologistService.getPsychologistAvailabilityDateById(psychologistId) //schedule not working
//       setSchedule(response)
//       setErrorSchedule(null)
//     } catch (err) {
//       setErrorSchedule(err as null)
//       setSchedule(null)
//     } finally {
//       setLoadingSchedule(false)
//     }
//   }

//   useEffect(() => {
//     if (psychologistId) {
//       fetchPsychologistSchedule()
//     }
//   }, [psychologistId])

//   return {
//     schedule,
//     loadingSchedule,
//     errorSchedule,
//     refetch: fetchPsychologistSchedule,
//   }
// }
