import { addDays, compareAsc, eachDayOfInterval, format, isPast, setDefaultOptions, subDays } from 'date-fns'
import { useCallback, useEffect, useState } from 'react'
import { id, is } from 'date-fns/locale'
import { useSelector } from '@/store'
import moment from 'moment'
import { useDispatch } from 'react-redux'
import { setWeekView } from '@/store/psikolog/schedule.reducer'

type DateType = {
  isoDate: Date
  day: string
  month: string
  date: string
  year: string
}
export default function useWeeklyCalendar(date: Date | undefined, customDate: boolean = false) {
  const [currentDays, setCurrentDays] = useState<Array<DateType>>([])
  const { weekView } = useSelector((state) => state.psikologSchedule)
  const dispatch = useDispatch()

  const isEqualDate = (dateA: Date, dateB: Date) => {
    return (
      dateA.getDate() === dateB.getDate() &&
      dateA.getMonth() === dateB.getMonth() &&
      dateA.getFullYear() === dateB.getFullYear()
    )
  }

  const getCurrentWeek = useCallback(
    (customDate: boolean) => {
      setDefaultOptions({ locale: id })
      // moment.tz.setDefault()
      const selectedDate = date ? new Date(date) : new Date()

      const isEqual = isEqualDate(selectedDate, new Date())
      const isPastDate = isPast(subDays(new Date(selectedDate), 6))
      console.log(date, isEqual, isPastDate)

      const existingStartWeek = weekView.start ? new Date(weekView.start) : new Date()
      const existingEndWeek = weekView.end ? new Date(weekView.end) : new Date()
      const isRangeOfWeek = moment(selectedDate).isBetween(existingStartWeek, existingEndWeek)
      let currentStartWeek = existingStartWeek
      let currentEndWeek = existingEndWeek
      if (customDate) {
        currentStartWeek = subDays(new Date(selectedDate), 3)
        currentEndWeek = addDays(new Date(selectedDate), 3)
        dispatch(
          setWeekView({
            start: moment(currentStartWeek).format('YYYY-MM-DD'),
            end: moment(currentEndWeek).format('YYYY-MM-DD'),
          })
        )
      } else {
        if (isRangeOfWeek) {
          currentStartWeek = existingStartWeek
          currentEndWeek = existingEndWeek
        }
      }

      const daysOfWeek = eachDayOfInterval({ start: currentStartWeek, end: currentEndWeek })
      const weekdays = daysOfWeek.map((day, idx) => {
        return {
          isoDate: day,
          day: isEqualDate(day, new Date())
            ? 'Hari ini'
            : isEqualDate(day, addDays(new Date(), 1))
              ? 'Besok'
              : format(day, 'EEEE', { locale: id }),
          month: format(day, 'MMM', { locale: id }),
          date: format(day, 'dd', { locale: id }),
          year: format(day, 'yyyy', { locale: id }),
        }
      })

      setCurrentDays(weekdays)
    },
    [date, dispatch, weekView.end, weekView.start]
  )

  useEffect(() => {
    getCurrentWeek(customDate)
  }, [getCurrentWeek, customDate])

  return {
    currentDays,
  }
}
