import { TableState } from '@tanstack/react-table'
import AppInput from '../_common/input/Input'
import { IIcons, SVGIcons } from '../_common/icon'
import { Updater } from '@tanstack/react-query'
import { getPaginationItems } from '@/utils/getPaginationItems'
import { AppSelect } from '../_common/Select/AppSelect'

type FooterProps = {
  fromRow: string | number
  toRow: string | number
  totalRow: string | number
  totalPage: string | number
  getState: () => TableState
  setPageSize: (updater: Updater<number, any>) => void
  previousPage: () => void
  getCanPreviousPage: () => boolean
  setPageIndex: (updater: Updater<number, any>) => void
  nextPage: () => void
  getCanNextPage: () => boolean
}

export const FooterTable = ({
  fromRow,
  toRow,
  totalRow,
  totalPage,
  getState,
  setPageSize,
  previousPage,
  getCanPreviousPage,
  setPageIndex,
  nextPage,
  getCanNextPage,
}: FooterProps) => {
  const allowPrevious = getCanPreviousPage()
  const allowNext = getCanNextPage()

  const pageItems = getPaginationItems(getState().pagination.pageIndex + 1, Number(totalPage), 7)

  return (
    <nav
      className="flex items-center xs:flex-col sm:flex-row flex-column flex-wrap xs:justify-around lg:justify-between xs:pt-1.5 pt-4 xs:px-0 xs:py-3 sm:px-2 md:px-4 gap-2 xs:gap-3 sm:gap-4"
      aria-label="Table navigation"
    >
      <span className="flex flex-none text-sm font-medium text-gray-400 md:mb-0 md:inline sm:w-auto">
        Menampilkan {fromRow?.toLocaleString() ?? 0} {` - `}
        {toRow?.toLocaleString() ?? 0} dari {String(totalRow)}
      </span>

      <div className="flex flex-none items-center flex-row gap-x-4 xs:w-auto">
        <span className="flex align-start">Tampilkan</span>
        <AppSelect
          value={String(getState().pagination.pageSize)}
          options={[
            { label: '10 Data', value: '10' },
            { label: '20 Data', value: '20' },
            { label: '30 Data', value: '30' },
            { label: '40 Data', value: '40' },
            { label: '50 Data', value: '50' },
          ]}
          placeholder={'row'}
          onChange={(val) => setPageSize(Number(val))}
        ></AppSelect>
      </div>

      <ul className="flex lg:flex-1 lg:justify-center 2lg:flex-initial 2lg:justify-normal -space-x-px rtl:space-x-reverse text-sm h-8 gap-x-2">
        <li>
          <button
            onClick={() => previousPage()}
            disabled={!allowPrevious}
            className={[
              `w-6 sm:w-8 h-[34px] xs:w-7 xs:p-0.5 sm:p-1 flex items-center border text-body-sm justify-center capitalize bg-white rounded-[5px]`,
              `border-line-200 text-gray-400 ${allowPrevious ? 'hover:border-gray-400' : 'cursor-not-allowed'}`,
            ].join(' ')}
          >
            <SVGIcons name={IIcons.ArrorLeft} />
          </button>
        </li>
        {pageItems.map((pageNum, idx) => {
          return (
            <button
              key={idx}
              disabled={isNaN(pageNum)}
              onClick={() => {
                const page = pageNum - 1
                setPageIndex(page)
              }}
              className={`w-6 sm:w-8 h-[34px] xs:w-7 xs:p-0.5 sm:px-3 sm:py-2 flex items-center border text-body-sm justify-center capitalize bg-white rounded-[5px]  
              ${getState().pagination.pageIndex === pageNum - 1 ? 'border-gray-400 text-gray-400' : 'border-line-200 text-gray-200'}
              ${!isNaN(pageNum) ? 'hover:border-gray-400 hover:text-gray-400' : 'border-none'}
              `}
            >
              {!isNaN(pageNum) ? (
                pageNum
              ) : (
                <div className="flex flex-start items-center gap-x-1">
                  <span className="h-[4px] w-[4px] bg-gray-100 rounded-full"></span>
                  <span className="h-[4px] w-[4px] bg-gray-100 rounded-full"></span>
                </div>
              )}
            </button>
          )
        })}

        <li>
          <button
            onClick={() => nextPage()}
            disabled={!allowNext}
            className={[
              `w-6 sm:w-8 h-[34px] xs:w-7 xs:p-0.5 sm:p-1 flex items-center border text-body-sm justify-center capitalize bg-white rounded-[5px]`,
              `border-line-200 text-gray-400 ${allowNext ? 'hover:border-gray-400' : 'cursor-not-allowed'}`,
            ].join(' ')}
          >
            <SVGIcons name={IIcons.ArrowRight} />
          </button>
        </li>
      </ul>
    </nav>
  )
}
