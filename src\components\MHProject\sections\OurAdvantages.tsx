type OurAdvantageProps = {
  title: string
  content: any[]
}

export const OurAdvantages = ({ title, content }: OurAdvantageProps) => {
  return (
    <div className="flex flex-col gap-y-15 items-center px-4 md:px-0">
      <span className="text-subheading-md md:text-[38px] md:leading-[42px] font-bold text-gray-400 text-center">
        {title}
      </span>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {content.length &&
          content.map((item, id) => {
            return (
              <div
                key={item.id}
                className={`flex flex-col justify-center items-start gap-y-4 p-6 bg-[#F8FAFC] rounded-lg ${(id === 0 || id === content.length - 1) && 'md:col-span-2'}`}
              >
                <span className="text-subheading-md md:text-heading-sm font-bold text-gray-400">
                  {item.label}
                </span>
                <span className="text-body-md md:text-body-lg font-medium text-gray-300">{item.content}</span>
              </div>
            )
          })}
      </div>
    </div>
  )
}
