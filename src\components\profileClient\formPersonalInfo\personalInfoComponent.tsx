'use client'
import { useState, useEffect, useRef } from 'react'
import { HeaderContent } from '@/components/admin/HeaderContent'
import Breadcrumb from '@/components/breadcrumbs/Breadcrumbs'
import { format } from 'date-fns'
import { useProfileInformation } from '../hook/useClientInformationProfile'
import { AppSelect } from '@/components/_common/Select/AppSelect'
import { RadioInput } from '@/components/_common/RadioInput/RadioInput'
import { DatePicker } from '@/components/ui/DatePicker'
import AppInput from '@/components/_common/input/Input'
import ButtonPrimary from '@/components/_common/ButtonPrimary'
import { InputImage } from '@/components/_common/InputImage'
import {
  EducationOptionsList,
  EthnicityoptionsList,
  GenderOptions,
  MaritalStatusOptions,
  OccupationOptionsList,
  ReligionOptionsList,
} from '@/constans/onboardOptions'
import { FormInputProfile } from '@/components/psikolog/setting/FormInputProfile'
import { DomiciliesOptions } from '@/components/auth/Onboard/DomiciliesOptions'
import { useProvince } from '@/hooks/useProvince.hook'
import { useRegencies } from '@/hooks/useRegencies.hook'
import { useDistricts } from '@/hooks/useDistricts.hook'
import { useSubDistricts } from '@/hooks/useSubdistricts.hook'
import { useForm } from 'react-hook-form'

interface UpdateData {
  [key: string]: string | Date | File | null
}

interface FormDataField {
  fullName: string
  nickname: string
  birthDate: Date | null
  gender: string
  phoneNumber: string
  birthOrder: string
  religion: string
  ethnicity: string
  domicile: string
  maritalStatus: string
  education: string
  occupation: string
  workplace: string
  profilePhoto: File | null
}

export default function PersonalInfoComponent() {
  const { profileData, loading, error, updateProfileInformation, updateLoading, updateSuccess, updateError } =
    useProfileInformation()

  const [formData, setFormData] = useState({
    fullName: '',
    nickname: '',
    birthDate: null as Date | null,
    gender: '',
    phoneNumber: '',
    birthOrder: '',
    religion: '',
    ethnicity: '',
    domicile: '',
    maritalStatus: '',
    education: '',
    occupation: '',
    workplace: '',
    profilePhoto: null,
  })

  // Initialize React Hook Form to fix TypeScript errors with DomiciliesOptions
  const { register, getValues, setValue } = useForm<any>({
    defaultValues: {
      province: '',
      regencies: '',
      districts: '',
      subDistricts: '',
    },
  })

  const [successMessage, setSuccessMessage] = useState('')
  const [profilePhotoPreview, setProfilePhotoPreview] = useState('')
  const ref = useRef<any>(null)
  // Update form data when profile data is loaded
  useEffect(() => {
    if (profileData) {
      setFormData({
        fullName: profileData.fullName || '',
        nickname: profileData.nickname || '',
        birthDate: profileData.birthDate ? new Date(profileData.birthDate) : null,
        gender: profileData.gender || '',
        phoneNumber: profileData.phoneNumber || '',
        birthOrder: profileData.birthOrder || '',
        religion: profileData.religion || '',
        ethnicity: profileData.ethnicity || '',
        domicile: profileData.domicile || '',
        maritalStatus: profileData.maritalStatus || '',
        education: profileData.education || '',
        occupation: profileData.occupation || '',
        workplace: profileData.workplace || '',
        profilePhoto: null,
      })

      if (profileData.profilePhoto) {
        setProfilePhotoPreview(profileData.profilePhoto)
      }
    }
  }, [profileData])

  // Handle update success message
  useEffect(() => {
    if (updateSuccess) {
      setSuccessMessage('Profile updated successfully!')
      const timer = setTimeout(() => {
        setSuccessMessage('')
      }, 3000)
      return () => clearTimeout(timer)
    }
  }, [updateSuccess])

  // Format birth date for display
  const formatBirthDate = (dateString: Date) => {
    if (!dateString) return 'Not specified'
    try {
      const date = new Date(dateString)
      return format(date, 'dd MMMM yyyy')
    } catch (error) {
      console.error('Error formatting date:', error)
      return 'Invalid date'
    }
  }

  // Format birth order for display
  const formatBirthOrder = (birthOrder: string) => {
    if (!birthOrder) return 'Not specified'

    const parts = birthOrder.split('/')
    if (parts.length !== 2) return birthOrder

    return `Anak ke-${parts[0]} dari ${parts[1]} bersaudara`
  }

  // Handle form field changes
  const handleInputChange = (field: keyof typeof formData, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }))
  }

  // Handle profile photo change
  const handleFileChange = (file: any) => {
    if (file) {
      setFormData((prev) => ({
        ...prev,
        profilePhoto: file,
      }))
      setProfilePhotoPreview(URL.createObjectURL(file))
    }
  }

  // Handle form submission for each field

  const handleSubmit = (field: keyof FormDataField): void => {
    const updateData: UpdateData = {}

    if (field === 'birthOrder') {
      // Handle special case for birth order if needed
      updateData[field] = formData[field]
    } else if (field === 'birthDate') {
      // Format date for API
      updateData[field] = formData[field]
    } else if (field === 'profilePhoto') {
      // Handle profile photo
      if (formData.profilePhoto) {
        updateData[field] = formData.profilePhoto
      }
    } else {
      // Handle regular fields
      updateData[field] = formData[field]
    }

    updateProfileInformation(updateData)
  }

  // Add these states for location codes
  const [locationCodes, setLocationCodes] = useState({
    province: '',
    regencies: '',
    districts: '',
    subDistricts: '',
  })

  // Add these queries to get location data
  const { data: provinces } = useProvince()
  const { data: regencies } = useRegencies(locationCodes.province)
  const { data: districts } = useDistricts(locationCodes.regencies)
  const { data: subDistricts } = useSubDistricts(locationCodes.districts)

  // Add function to parse domicile string and get location names
  const parseDomicileString = (domicileString: string) => {
    if (!domicileString) return ''
    const codes = domicileString.split(', ')
    if (codes.length !== 4) return domicileString

    const provinceName = provinces?.find((p: string | any) => p.id === codes[0])?.name || codes[0]
    const regencyName = regencies?.find((r: string | any) => r.id === codes[1])?.name || codes[1]
    const districtName = districts?.find((d: string | any) => d.id === codes[2])?.name || codes[2]
    const subDistrictName = subDistricts?.find((s: string | any) => s.id === codes[3])?.name || codes[3]

    return `${provinceName}, ${regencyName}, ${districtName}, ${subDistrictName}`
  }

  // Update useEffect to set location codes and React Hook Form values
  useEffect(() => {
    if (profileData?.domicile) {
      const codes = profileData.domicile.split(', ')
      if (codes.length === 4) {
        const newLocationCodes = {
          province: codes[0],
          regencies: codes[1],
          districts: codes[2],
          subDistricts: codes[3],
        }

        setLocationCodes(newLocationCodes)

        // Also update the form values for react-hook-form
        setValue('province', codes[0])
        setValue('regencies', codes[1])
        setValue('districts', codes[2])
        setValue('subDistricts', codes[3])
      }
    }
  }, [profileData, setValue])

  // Update the hook-form values when locationCodes change
  useEffect(() => {
    setValue('province', locationCodes.province)
    setValue('regencies', locationCodes.regencies)
    setValue('districts', locationCodes.districts)
    setValue('subDistricts', locationCodes.subDistricts)
  }, [locationCodes, setValue])

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <p>Loading personal information...</p>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <p className="text-red-500">Error loading personal information: {error.message}</p>
      </div>
    )
  }

  return (
    <div className="flex items-center justify-center lg:mb-[198px] lg:mt-[64px] w-full lg:w-[930px] xl:w-[1120px] max-w-[1120px] py-4 lg:py-0 px-4 lg:px-0">
      <div className="flex flex-col w-full md:w-[548px] mb-[150px] md:mb-0">
        <Breadcrumb containerClasses="pb-6 hidden md:flex" pageName="Personal Info" />
        {/* Header */}
        <HeaderContent className="mb-[18px] gap-y-0" title="Personal Info" />

        {/* Success Message */}
        {successMessage && (
          <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
            <p>{successMessage}</p>
          </div>
        )}

        {/* Error Message */}
        {updateError && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            <p>Error updating profile: {updateError.message}</p>
          </div>
        )}

        {/* Profile Photo */}
        <FormInputProfile
          label="Foto Profil"
          type="file"
          value={formData.profilePhoto}
          renderViewMode={
            profilePhotoPreview ? (
              <div className="mt-2">
                <img
                  src={profilePhotoPreview}
                  alt="Profile"
                  className="w-16 h-16 rounded-full object-cover"
                />
              </div>
            ) : (
              <div className="mt-2 text-gray-500">No profile photo</div>
            )
          }
          onSubmit={() => handleSubmit('profilePhoto')}
        >
          <InputImage
            name="profilePhoto"
            onChange={handleFileChange}
            preview={profilePhotoPreview}
            maxFileSizeMb={2}
            accept="image/jpeg,image/png"
            inputRef={ref}
            label="Upload foto profil"
          />
        </FormInputProfile>

        {/* Nickname */}
        <FormInputProfile
          label="Nama Panggilan"
          type="text"
          value={formData.nickname}
          renderViewMode={<div className="mt-2">{formData.nickname || '-'}</div>}
          onSubmit={() => handleSubmit('nickname')}
        >
          <AppInput
            type="text"
            name="nickname"
            value={formData.nickname}
            onChange={(e) => handleInputChange('nickname', e.target.value)}
          />
        </FormInputProfile>

        {/* Full Name */}
        <FormInputProfile
          label="Nama Lengkap"
          type="text"
          value={formData.fullName}
          renderViewMode={<div className="mt-2">{formData.fullName || '-'}</div>}
          onSubmit={() => handleSubmit('fullName')}
        >
          <AppInput
            type="text"
            name="fullName"
            value={formData.fullName}
            onChange={(e) => handleInputChange('fullName', e.target.value)}
          />
        </FormInputProfile>

        {/* Birth Date */}
        <FormInputProfile
          label="Tanggal Lahir"
          type="date"
          value={formData.birthDate}
          renderViewMode={
            <div className="mt-2">{formData.birthDate ? formatBirthDate(formData.birthDate) : '-'}</div>
          }
          onSubmit={() => handleSubmit('birthDate')}
        >
          <DatePicker
            placeholder="Pilih Tanggal Lahir"
            className="py-3 h-[50px] w-full"
            date={formData.birthDate ?? undefined}
            onSelect={(date) => handleInputChange('birthDate', date)}
            captionLayout="dropdown"
            fromYear={new Date().getFullYear() - 100}
            toYear={new Date().getFullYear()}
          />
        </FormInputProfile>

        {/* Gender */}
        {/* <FormInputProfile
          label="Jenis Kelamin"
          type="select"
          option={GenderOptions}
          value={formData.gender}
          renderViewMode={<div className="mt-2">{formData.gender || '-'}</div>}
          onSubmit={() => handleSubmit('gender')}
        >
          <AppSelect
            options={GenderOptions}
            value={formData.gender}
            onChange={(val) => handleInputChange('gender', val)}
            className="h-[50px]"
            placeholder="Pilih Jenis Kelamin"
          />
        </FormInputProfile> */}

        {/* Phone Number */}
        <FormInputProfile
          label="Nomor Telepon"
          type="text"
          value={formData.phoneNumber}
          renderViewMode={<div className="mt-2">{formData.phoneNumber || '-'}</div>}
          onSubmit={() => handleSubmit('phoneNumber')}
        >
          <AppInput
            type="text"
            name="phoneNumber"
            value={formData.phoneNumber}
            onChange={(e) => handleInputChange('phoneNumber', e.target.value)}
          />
        </FormInputProfile>

        {/* Domicile */}
        <FormInputProfile
          label="Domisili"
          type="text"
          value={formData.domicile}
          renderViewMode={<div className="mt-2">{parseDomicileString(formData.domicile) || '-'}</div>}
          onSubmit={() => handleSubmit('domicile')}
        >
          <DomiciliesOptions
            register={register}
            getValues={getValues}
            setValue={(field, value) => {
              setValue(field, value)
              setLocationCodes((prev) => ({
                ...prev,
                [field]: value,
              }))
            }}
            errors={{}}
            onFinalSetValue={(val) => {
              handleInputChange('domicile', val)
            }}
            provinceProps={locationCodes.province}
            regencieProps={locationCodes.regencies}
            districtProps={locationCodes.districts}
            subDistrictProps={locationCodes.subDistricts}
            onSubmit={() => console.log('domicile submitted')}
          />
        </FormInputProfile>

        {/* Birth Order */}
        <FormInputProfile
          label="Urutan Bersaudara"
          type="text"
          value={formData.birthOrder}
          renderViewMode={<div className="mt-2">{formatBirthOrder(formData.birthOrder) || '-'}</div>}
          onSubmit={() => handleSubmit('birthOrder')}
        >
          <div className="flex gap-4 items-center">
            <AppInput
              className="pt-0 max-w-[105px]"
              type="number"
              name="childTo"
              placeholder="Anak ke"
              value={formData.birthOrder?.split('/')[0] || ''}
              onChange={(e) => {
                const childTo = e.target.value
                const totalSibling = formData.birthOrder?.split('/')[1] || '0'
                handleInputChange('birthOrder', `${childTo}/${totalSibling}`)
              }}
            />
            <span>Dari</span>
            <AppInput
              className="pt-0 grow"
              type="number"
              name="totalSibling"
              placeholder="Jumlah Saudara"
              value={formData.birthOrder?.split('/')[1] || ''}
              onChange={(e) => {
                const childTo = formData.birthOrder?.split('/')[0] || '0'
                const totalSibling = e.target.value
                handleInputChange('birthOrder', `${childTo}/${totalSibling}`)
              }}
            />
          </div>
        </FormInputProfile>

        {/* Ethnicity */}
        <FormInputProfile
          label="Suku Bangsa"
          type="select"
          option={EthnicityoptionsList}
          value={formData.ethnicity}
          renderViewMode={<div className="mt-2">{formData.ethnicity || '-'}</div>}
          onSubmit={() => handleSubmit('ethnicity')}
        >
          <AppSelect
            options={EthnicityoptionsList}
            value={formData.ethnicity}
            onChange={(val) => handleInputChange('ethnicity', val)}
            className="h-[50px]"
            placeholder="Pilih Suku Bangsa"
          />
          {formData.ethnicity === 'Lainnya (Input sendiri)' && (
            <AppInput
              className="pt-2 mt-2"
              type="text"
              name="ethnicityOther"
              placeholder="Silahkan masukan suku bangsa lainnya"
              onChange={(e) => handleInputChange('ethnicity', e.target.value)}
            />
          )}
        </FormInputProfile>

        {/* Religion */}
        <FormInputProfile
          label="Agama"
          type="select"
          option={ReligionOptionsList}
          value={formData.religion}
          renderViewMode={<div className="mt-2">{formData.religion || '-'}</div>}
          onSubmit={() => handleSubmit('religion')}
        >
          <AppSelect
            options={ReligionOptionsList}
            value={formData.religion}
            onChange={(val) => handleInputChange('religion', val)}
            className="h-[50px]"
            placeholder="Pilih Agama"
          />
          {formData.religion === 'Lainnya (Input sendiri)' && (
            <AppInput
              className="pt-2 mt-2"
              type="text"
              name="religionOther"
              placeholder="Silahkan masukan agama lainnya"
              onChange={(e) => handleInputChange('religion', e.target.value)}
            />
          )}
        </FormInputProfile>

        {/* Occupation */}
        <FormInputProfile
          label="Pekerjaan Saat Ini"
          type="select"
          option={OccupationOptionsList}
          value={formData.occupation}
          renderViewMode={<div className="mt-2">{formData.occupation || '-'}</div>}
          onSubmit={() => handleSubmit('occupation')}
        >
          <AppSelect
            options={OccupationOptionsList}
            value={formData.occupation}
            onChange={(val) => handleInputChange('occupation', val)}
            className="h-[50px]"
            placeholder="Pilih Pekerjaan"
          />
          {formData.occupation === 'Lainnya (Input sendiri)' && (
            <AppInput
              className="pt-2 mt-2"
              type="text"
              name="occupationOther"
              placeholder="Silahkan masukan pekerjaan lainnya"
              onChange={(e) => handleInputChange('occupation', e.target.value)}
            />
          )}
        </FormInputProfile>

        {/* Workplace */}
        <FormInputProfile
          label="Perusahaan Tempat Bekerja"
          type="text"
          value={formData.workplace}
          renderViewMode={<div className="mt-2">{formData.workplace || '-'}</div>}
          onSubmit={() => handleSubmit('workplace')}
        >
          <AppInput
            type="text"
            name="workplace"
            value={formData.workplace || ''}
            onChange={(e) => handleInputChange('workplace', e.target.value)}
          />
        </FormInputProfile>

        {/* Marital Status */}
        <FormInputProfile
          label="Status Pernikahan"
          type="select"
          option={MaritalStatusOptions}
          value={formData.maritalStatus}
          renderViewMode={<div className="mt-2">{formData.maritalStatus || '-'}</div>}
          onSubmit={() => handleSubmit('maritalStatus')}
        >
          <RadioInput
            options={MaritalStatusOptions}
            name={'maritalStatus'}
            value={formData.maritalStatus}
            onChange={(val) => handleInputChange('maritalStatus', val)}
          />
        </FormInputProfile>

        {/* Education */}
        <FormInputProfile
          label="Pendidikan"
          type="select"
          option={EducationOptionsList}
          value={formData.education}
          renderViewMode={<div className="mt-2">{formData.education || '-'}</div>}
          onSubmit={() => handleSubmit('education')}
        >
          <AppSelect
            options={EducationOptionsList}
            value={formData.education}
            onChange={(val) => handleInputChange('education', val)}
            className="h-[50px]"
            placeholder="Pilih Pendidikan"
          />
        </FormInputProfile>
      </div>
    </div>
  )
}
