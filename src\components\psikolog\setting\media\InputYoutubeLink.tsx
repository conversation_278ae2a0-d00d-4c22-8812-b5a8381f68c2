import ButtonPrimary from '@/components/_common/ButtonPrimary'
import AppInput from '@/components/_common/input/Input'
import { AppBigCaption, AppMediumText } from '@/components/_common/ui'
import YoutubeEmbed from './YoutubeEmbedVideo'
import { useEffect, useState } from 'react'
import { LoadingSkeletonVideo } from './LoadingVideo'
import { Skeleton } from '@/components/ui/skeleton'

type InputYoutubelinkProps = {
  isEdit?: boolean
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void
  onSubmit: (link: string | undefined) => void
  onEdit: () => void
  onRemove: () => void
  btnShow?: boolean
  value: string | undefined
  isLoading?: boolean
}

const API_KEY = 'AIzaSyAdTcjjVniUazHxgEQy5Wn99k--Noz-1XE'

export const InputYoutubeLink = ({
  isEdit = true,
  value,
  onEdit,
  onRemove,
  onSubmit,
  btnShow,
  isLoading: isLoadingApp,
}: InputYoutubelinkProps) => {
  const [isLoading, setIsLoading] = useState(false)
  const [link, setLink] = useState<string>('')
  const [title, setTitle] = useState<string>('')
  const [inputLink, setInputLink] = useState<string | undefined>(value)

  const handleClickSave = () => {
    setIsLoading(true)
    setTimeout(() => {
      setIsLoading(false)
    }, 1500)
    onSubmit(inputLink)
  }

  const handleEdit = () => {
    onEdit()
  }
  const handleRemove = () => {
    onRemove()
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    e.preventDefault()
    setInputLink(e.target.value)
  }

  useEffect(() => {
    async function fetchYTApi(embedId: string) {
      const response = await fetch(
        `https://youtube.googleapis.com/youtube/v3/videos?part=snippet%2CcontentDetails%2Cstatistics&id=${embedId}&key=${API_KEY}`
      )
        .then((res) => res.json())
        .then((result) => {
          const videoItem = result.items[0]
          setLink(`https://www.youtube.com/embed/${embedId}`)
          setTitle(videoItem?.snippet?.title)
        })
        .catch((error) => {
          console.log(error)
        })
      return response
    }
    try {
      const linkSrc = inputLink ?? ''
      const getEmbedId = linkSrc?.split('v=')[1]
      const embedId = getEmbedId?.split('&')[0]
      fetchYTApi(embedId)
    } catch (error) {
      console.log(error)
    }
  }, [inputLink])

  const renderBtn = () => {
    if (btnShow) {
      return (
        <div className="mt-4">
          <ButtonPrimary
            isLoading={isLoading || isLoadingApp}
            variant="contained"
            size="xs"
            onClick={() => handleClickSave()}
          >
            Simpan
          </ButtonPrimary>
        </div>
      )
    } else {
      return null
    }
  }

  if (isEdit) {
    return (
      <div className="grid col-span-2 items-center gap-y-2">
        <AppMediumText bold>Youtube Link</AppMediumText>
        <AppInput type={'text'} value={inputLink ?? ''} onChange={handleChange} />
        <AppBigCaption>Pastikan yang dilampirkan merupakan link Youtube.</AppBigCaption>
        {renderBtn()}
      </div>
    )
  }

  return (
    <div className="flex flex-col xs:flex-col sm:flex-row flex-start xs:flex-start md:items-center gap-4">
      <label className={`cursor-pointer flex items-center bg-opacity-50`}>
        {isLoading && <Skeleton className="h-[135px] w-[240px] rounded-xl" />}
        <YoutubeEmbed className={isLoading ? 'hidden' : ''} src={link ?? ''} />
      </label>

      <div className="flex flex-col items-start gap-2">
        {isLoading ? (
          <Skeleton className="h-4 w-[150px]" />
        ) : (
          title && <span className="w-full text-caption-md font-bold text-gray-400">{title}</span>
        )}

        <div className="flex gap-2">
          {isLoading ? (
            <Skeleton className="h-8 w-[150px]" />
          ) : (
            <>
              <ButtonPrimary variant="outlined" size="xs" onClick={handleEdit}>
                Ubah Video
              </ButtonPrimary>
              <ButtonPrimary color="gray" variant="outlined" size="xs" onClick={handleRemove}>
                Hapus
              </ButtonPrimary>
            </>
          )}
        </div>
      </div>
    </div>
  )
}
