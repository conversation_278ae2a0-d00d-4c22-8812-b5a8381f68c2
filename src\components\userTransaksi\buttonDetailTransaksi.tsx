import ButtonPrimary from '../_common/ButtonPrimary'

export default function ButtonDetailTransaksi({
  title,
  title2,
  showJadwal,
  showSecondButton,
  buttonVariant,
  color,
}: {
  title: string
  title2: string
  showJadwal: boolean
  showSecondButton: any
  buttonVariant: any
  color: any
}) {
  const showButton = () => {
    if (showSecondButton === true) {
      return (
        <>
          <ButtonPrimary
            className="min-w-[143px] w-full rounded-[15px] py-[14px]"
            variant={'contained'}
            size="base"
          >
            {title}
          </ButtonPrimary>
          <ButtonPrimary
            className="min-w-[143px] w-full rounded-[15px] py-[14px]"
            color="gray"
            variant={'outlined'}
            size="base"
          >
            {title2}
          </ButtonPrimary>
        </>
      )
    } else if (showSecondButton === false) {
      return (
        <>
          <ButtonPrimary
            className="min-w-[143px] w-full rounded-[15px] py-[14px]"
            color={color}
            variant={buttonVariant}
            size="base"
          >
            {title2}
          </ButtonPrimary>
        </>
      )
    } else {
      return null
    }
  }
  return (
    <>
      <div className={`${showSecondButton === null ? 'hidden' : 'flex'} items-center justify-between gap-4`}>
        <div className={`${showJadwal === true ? 'flex' : 'hidden'} flex-col gap-1 w-full`}>
          <span className="text-[16px] text-[#222222]">Jadwal Berikutnya: </span>
          <span className="text-[16px] text-[#222222] font-bold">Senin, 8 Agu 2024, 13.00 WIB</span>
        </div>
        {showButton()}
      </div>
    </>
  )
}
