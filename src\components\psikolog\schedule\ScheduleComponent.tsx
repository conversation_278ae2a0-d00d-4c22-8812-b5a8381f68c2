'use client'
import TabItem from '@/components/_common/tabs/TabItem'
import TabList from '@/components/_common/tabs/TabList'
import { HeaderContent } from '@/components/admin/HeaderContent'
import ScheduleList from './ScheduleList'
import React, { useEffect, useMemo, useRef, useState } from 'react'
import { AppModal } from '@/components/_common/Modal/AppModal'
import ScheduleForm from './reschedule/ScheduleForm'
import { IModalActionState } from '@/interfaces/components/psikolog/schedule'
import { useDispatch, useSelector } from '@/store'
import {
  ScheduleListProps,
  setModalState,
  setRejectStep,
  setRescheduleStep,
} from '@/store/psikolog/schedule.reducer'
import RejectForm from './RejectSchedule/RejectForm'
import { counsellingService } from '@/services/counselling.service'
import {
  CancelledStatus,
  CompletedStatus,
  PaymentStatus,
  UpcomingStatus,
  WaitConfirmationsStatus,
} from '@/constans/StaticOptions'
import { useCounselingList } from './useCounselingList.hook'
import { useRouter, useSearchParams } from 'next/navigation'
import { Routes } from '@/constans/routes'
import StartCounselingDialog from './StartCounseling/StartCounselingDialog'
import { useToast } from '@/components/ui/use-toast'
import { ListInformation } from '@/components/_common/ListInformation'
import {
  formatStringToFulldateOutput,
  formatStringToStartEndTimeOutput,
  formatStringToTimeOutput,
} from '@/utils/displayDate'
import { IIcons } from '@/components/_common/icon'
import ApproveCounselingDialog from './ApproveCounseling/ApproveCounselingDialog'
import CompleteConfirmationDialog from './CompleteConfirmation/CompleteConfirmation'
import CancelSessionDialog from './CancelSession/CancelSession'
import useGetPsychologistTimezone from '@/hooks/useGetPsychologistTimezone.hook'
import useGetTimezoneLabel from '@/hooks/useGetTimezone.hook'

export const ScheduleComponent = () => {
  const { toast } = useToast()
  const router = useRouter()
  const modalRef = useRef<any>(null)
  const dispatch = useDispatch()
  const { modalState, rescheduleStep } = useSelector((state) => state.psikologSchedule)
  const [activeTab, setActiveTab] = useState<number>(0)

  const handleCLickStartCounselling = (item?: any) => {
    dispatch(
      setModalState({
        modal:
          modalState.modal === IModalActionState.START_COUNSELING ? null : IModalActionState.START_COUNSELING,
        item,
      })
    )
  }

  const handleCLickReschedule = (item?: any) => {
    console.log(modalState)
    dispatch(
      setModalState({
        modal: modalState.modal === IModalActionState.RESCHEDULE ? null : IModalActionState.RESCHEDULE,
        item,
      })
    )
    dispatch(setRescheduleStep(1))
  }

  const handleCLickReject = (item?: any) => {
    dispatch(
      setModalState({
        modal: modalState.modal === IModalActionState.REJECT ? null : IModalActionState.REJECT,
        item,
      })
    )
    dispatch(setRejectStep(1))
  }

  const approveCounseling = async (item: ScheduleListProps) => {
    try {
      await counsellingService.approveCounselling(item.id)
      toast({
        title: 'Berhasil',
        description: (
          <span>
            Konseling dengan <span className="font-bold">{item.client?.fullName}</span> berhasil anda
            diterima.
          </span>
        ),
        variant: 'success',
      })
      handleOnclickTab(0)
      refetch()
    } catch (err) {
      toast({
        title: 'Gagal',
        description: 'Konseling gagal diterima. Silahkan coba lagi.',
        variant: 'danger',
      })
    }
  }

  const handleClickApproveCounseling = async (item: ScheduleListProps) => {
    const approveConfirmation = localStorage.getItem('approveConfirmation')
    if (approveConfirmation === 'true') {
      approveCounseling(item)
    } else {
      dispatch(
        setModalState({
          modal: modalState.modal === IModalActionState.APPROVE ? null : IModalActionState.APPROVE,
          item,
        })
      )
    }
  }

  const handleCancelSession = async (item: ScheduleListProps) => {
    dispatch(
      setModalState({
        modal:
          modalState.modal === IModalActionState.CANCEL_SESSION ? null : IModalActionState.CANCEL_SESSION,
        item,
      })
    )
  }

  const handleConfirmationSession = async (item: ScheduleListProps) => {
    try {
      await counsellingService.completeCounselling(item.id)
      dispatch(
        setModalState({
          modal:
            modalState.modal === IModalActionState.CONFIRMATION_COMPLETELY
              ? null
              : IModalActionState.CONFIRMATION_COMPLETELY,
          item,
        })
      )
    } catch (error: any) {
      const showErrorFromBE = error?.response?.data?.frontEndDisplayMessage
      toast({
        title: showErrorFromBE ? 'Gagal' : 'Terjadi Masalah Jaringan',
        description: showErrorFromBE
          ? error?.response?.data?.message
          : 'Terjadi masalah pada server, silahkan hubungi admin untuk bantuan.',
        variant: 'danger',
      })
    }
  }

  const [category, setCategory] = useState<PaymentStatus[]>(UpcomingStatus)
  const searchParam = useSearchParams()
  const categoryParam = searchParam.get('category')
  useEffect(() => {
    if (categoryParam === 'upcoming') {
      setCategory(UpcomingStatus)
      setActiveTab(0)
    } else if (categoryParam === 'wait-confirmation') {
      setCategory(WaitConfirmationsStatus)
      setActiveTab(1)
    } else if (categoryParam === 'completed') {
      setCategory(CompletedStatus)
      setActiveTab(2)
    } else if (categoryParam === 'cancelled') {
      setCategory(CancelledStatus)
      setActiveTab(3)
    } else {
      setCategory(UpcomingStatus)
      setActiveTab(0)
    }
  }, [categoryParam])

  const { data, isError, isLoading, isPending, isFetched, refetch } = useCounselingList(category)
  const { counselingList, meta: counselingMeta } = data || {}
  const { upcomingCounselling, waitConfirmationCounselling, completeCounselling, cancelledCounselling } =
    counselingList || {}
  const [meta, setMeta] = useState(counselingMeta)

  const handleClose = (isRefetch?: boolean) => {
    dispatch(
      setModalState({
        modal: null,
        item: null,
      })
    )
    isRefetch && refetch()
  }

  const isLoadingApp = isLoading || isPending

  useEffect(() => {
    if (isFetched) setMeta(counselingMeta)
  }, [counselingMeta, isFetched])

  const ScheduleListTab = useMemo(
    () => [
      {
        label: `Akan Datang (${meta?.totalUpcoming ?? 0})`,
        content: (
          <ScheduleList
            isLoadingApp={isLoadingApp}
            itemList={upcomingCounselling ?? []}
            onStartCounselling={(item) => handleCLickStartCounselling(item)}
            onReschedule={(item) => handleCLickReschedule(item)}
            onCancelSession={(item) => handleCancelSession(item)}
            onConfirmationSession={(item) => handleConfirmationSession(item)}
          />
        ),
      },
      {
        label: `Menunggu Konfirmasi (${meta?.totalWaitConfirmation ?? 0})`,
        content: (
          <ScheduleList
            isLoadingApp={isLoadingApp}
            itemList={waitConfirmationCounselling ?? []}
            onApprove={(itemCounselling: ScheduleListProps) => handleClickApproveCounseling(itemCounselling)}
            onReject={(item) => handleCLickReject(item)}
            onReschedule={(item) => handleCLickReschedule(item)}
          />
        ),
      },
      {
        label: `Selesai (${meta?.totalCompleted ?? 0})`,
        content: (
          <ScheduleList
            isLoadingApp={isLoadingApp}
            itemList={completeCounselling ?? []}
            onCreateClientReport={(item: ScheduleListProps) =>
              router.push(
                `${Routes.PsychologistCreateClientReport}?name=${item.client?.fullName}&counselingId=${item.id}`
              )
            }
            onChangeCLientReport={(item: ScheduleListProps) =>
              router.push(
                `${Routes.PsychologistCreateClientReport}?name=${item.client?.fullName}&counselingId=${item.id}`
              )
            }
          />
        ),
      },
      {
        label: `Batal (${meta?.totalCancelled ?? 0})`,
        content: <ScheduleList isLoadingApp={isLoadingApp} itemList={cancelledCounselling ?? []} />,
      },
    ],
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [
      cancelledCounselling,
      completeCounselling,
      isLoadingApp,
      meta?.totalCancelled,
      meta?.totalCompleted,
      meta?.totalUpcoming,
      meta?.totalWaitConfirmation,
      router,
      upcomingCounselling,
      waitConfirmationCounselling,
    ]
  )

  let titleModal: string | React.ReactNode = ''

  if (modalState.modal === IModalActionState.RESCHEDULE) {
    titleModal = rescheduleStep === 3 ? 'Jadwal Baru Berhasil Dikirim' : 'Ubah Jadwal'
  } else if (modalState.modal === IModalActionState.REJECT) {
    titleModal = 'Tolak Jadwal'
  } else if (modalState.modal === IModalActionState.START_COUNSELING) {
    titleModal = (
      <TitleWithBadge
        startTime={modalState.item?.startTime ?? ''}
        endTime={modalState.item?.endTime ?? ''}
        duration={modalState.item?.duration ?? 60}
        fullName={modalState.item?.client?.fullName ?? ''}
        method={modalState.item?.method === 'Call' ? 'Call' : 'Video Call'}
      />
    )
  } else if (modalState.modal === IModalActionState.APPROVE) {
    titleModal = 'Terima Konseling?'
  } else if (modalState.modal === IModalActionState.CONFIRMATION_COMPLETELY) {
    titleModal = 'Selamat, Konseling sudah selesai!'
  } else if (modalState.modal === IModalActionState.CANCEL_SESSION) {
    titleModal = (
      <TitleWithBadge
        title={`Batalkan Sesi dengan ${modalState.item?.client?.fullName ?? 'Klien'}`}
        startTime={modalState.item?.startTime ?? ''}
        endTime={modalState.item?.endTime ?? ''}
        fullName={modalState.item?.client?.fullName ?? ''}
        method={modalState.item?.method === 'Call' ? 'Call' : 'Video Call'}
        duration={modalState.item?.duration ?? 60}
      />
    )
  }

  const modalOpen =
    modalState.modal === IModalActionState.RESCHEDULE ||
    modalState.modal === IModalActionState.REJECT ||
    modalState.modal === IModalActionState.START_COUNSELING ||
    modalState.modal === IModalActionState.APPROVE ||
    modalState.modal === IModalActionState.CONFIRMATION_COMPLETELY ||
    modalState.modal === IModalActionState.CANCEL_SESSION

  const handleOnclickTab = (index: number) => {
    setActiveTab(index)
    if (index === 0) {
      router.push(`${Routes.PsychologistCounseling}?category=upcoming`)
    }
    if (index === 1) {
      router.push(`${Routes.PsychologistCounseling}?category=wait-confirmation`)
    }
    if (index === 2) {
      router.push(`${Routes.PsychologistCounseling}?category=completed`)
    }
    if (index === 3) {
      router.push(`${Routes.PsychologistCounseling}?category=cancelled`)
    }
  }

  const isRefetchOncloseModalX =
    modalState.modal === IModalActionState.REJECT ||
    modalState.modal === IModalActionState.RESCHEDULE ||
    modalState.modal === IModalActionState.APPROVE

  return (
    <>
      <HeaderContent title="Jadwal" />
      <TabList
        onClickTabs={(index) => handleOnclickTab(index)}
        className="sticky top-navbar z-10 bg-white"
        activeTabIndex={activeTab}
      >
        {ScheduleListTab.map((counselling, index) => {
          return (
            <TabItem key={index} label={counselling.label}>
              {counselling.content}
            </TabItem>
          )
        })}
      </TabList>
      <AppModal
        ref={modalRef}
        className="md:w-full max-w-[668px]"
        open={modalOpen}
        onClose={() => {
          handleClose(isRefetchOncloseModalX)
        }}
        title={titleModal}
        showOverlay={true}
      >
        {modalState.modal === IModalActionState.RESCHEDULE ? (
          <ScheduleForm
            item={modalState.item}
            onClose={() => handleClose(true)}
            callbackToggleCalendar={(arg) => {
              modalRef.current.className = arg
                ? modalRef.current.className.replace('bg-black/80', 'bg-black/10 ')
                : modalRef.current.className + 'bg-black/80 '
            }}
          />
        ) : modalState.modal === IModalActionState.REJECT ? (
          <RejectForm item={modalState.item} onClose={() => handleClose(true)} />
        ) : modalState.modal === IModalActionState.START_COUNSELING ? (
          <StartCounselingDialog item={modalState.item} onClose={() => handleClose()} />
        ) : modalState.modal === IModalActionState.APPROVE ? (
          <ApproveCounselingDialog
            item={modalState.item}
            onClose={() => {
              handleClose(true)
              handleOnclickTab(0)
            }}
          />
        ) : modalState.modal === IModalActionState.CONFIRMATION_COMPLETELY ? (
          <CompleteConfirmationDialog item={modalState.item} onClose={() => handleClose()} />
        ) : modalState.modal === IModalActionState.CANCEL_SESSION ? (
          <CancelSessionDialog item={modalState.item} onClose={() => handleClose(true)} />
        ) : null}
      </AppModal>
    </>
  )
}

export const TitleWithBadge = ({
  title,
  startTime,
  endTime,
  fullName,
  method,
  duration,
}: {
  title?: string
  startTime: string
  endTime: string
  fullName: string
  method: string
  duration: number
}) => {
  const psychologistTimezone = useGetPsychologistTimezone()
  const timeZoneLabel = useGetTimezoneLabel()
  const dateLabel = formatStringToFulldateOutput(startTime)
  const timeLabel = formatStringToStartEndTimeOutput({
    date: startTime,
    duration: duration,
    timezone: psychologistTimezone,
    timeLabel: timeZoneLabel,
    isUTC: true,
  })
  return (
    <div className="grid gap-2">
      <span>{title ? title : `Mulai Konseling dengan ${fullName.length ? fullName : 'Klien'}`}</span>
      <ListInformation
        className="py-0 border-b-0 text-body-lg font-medium"
        listItem={[
          { label: dateLabel, icon: IIcons.Calendar },
          { label: timeLabel, icon: IIcons.Time },
          { label: method, icon: method === 'Call' ? IIcons.Call : IIcons.VideoCall },
        ]}
      />
    </div>
  )
}
