'use client'
import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Card } from '@/components/_common/ui'
import { SVGIcons, IIcons } from '@/components/_common/icon'
import Image from 'next/image'
import ButtonPrimary from '@/components/_common/ButtonPrimary'
import { toast } from '@/components/ui/use-toast'
import { counsellingService } from '@/services/counselling.service'

function BookingStatusPage() {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(true)
  const [paymentStatus, setPaymentStatus] = useState('failed')
  const [counselingData, setCounselingData] = useState<any>(null)
  const [transactionId, setTransactionId] = useState('')

  // Fetch booking data from localStorage or query params
  useEffect(() => {
    // // Try to get counseling ID from localStorage
    // const counselingId = localStorage.getItem('currentCounselingId')

    // // Try to get transaction ID from URL query params
    // const urlParams = new URLSearchParams(window.location.search)
    // const txnId = urlParams.get('transaction_id')
    // if (txnId) {
    //   setTransactionId(txnId)
    // }

    // Get previous booking data
    const savedBooking = localStorage.getItem('konselingBooking')
    if (savedBooking) {
      try {
        const bookingData = JSON.parse(savedBooking)
        setCounselingData(bookingData)
      } catch (error) {
        console.error('Error parsing saved booking', error)
      }
    }

    // // If we have a counseling ID, fetch the latest status
    // if (counselingId) {
    //   fetchCounselingStatus(counselingId)
    // } else {
    //   // Otherwise just stop loading state
    setIsLoading(false)
    // }
  }, [])

  // // Fetch the latest counseling status from API
  // const fetchCounselingStatus = async (counselingId) => {
  //   try {
  //     setIsLoading(true)

  //     const response = await counsellingService.getCounselingDetails(counselingId)

  //     if (response && response.status) {
  //       // Set payment status based on the response
  //       setPaymentStatus(response.status === 'PAID' ? 'success' : 'failed')

  //       // Update counseling data if we have new information
  //       if (response.counselingDetails) {
  //         setCounselingData({
  //           ...counselingData,
  //           ...response.counselingDetails,
  //         })
  //       }
  //     }
  //   } catch (error) {
  //     console.error('Failed to fetch counseling status:', error)
  //     // If there's an error, we'll default to the status from URL params
  //     const urlParams = new URLSearchParams(window.location.search)
  //     const status = urlParams.get('status')
  //     if (status === 'failed' || status === 'cancel') {
  //       setPaymentStatus('failed')
  //     }
  //   } finally {
  //     setIsLoading(false)
  //   }
  // }

  // Render success status page based on the design in the image
  const renderSuccessStatus = () => {
    return (
      <div className="flex flex-col md:flex-row relative">
        <div className="md:w-1/2 px-6 py-12 md:px-12 md:py-24 flex flex-col justify-center text-white relative z-10">
          <h1 className="text-3xl md:text-4xl font-bold mb-4">Terima kasih atas kepercayaannya</h1>
          <p className="text-xl">Semoga kita dapat menjadi versi terbaik dari diri kita</p>
        </div>

        <div className="md:w-1/2 px-6 py-8 md:px-12 md:py-24 flex items-center justify-center">
          <div className="w-full max-w-md">
            <Card className="rounded-xl overflow-hidden bg-white">
              <div className="bg-green-100 text-green-600 font-medium p-3 px-6 rounded-t-xl">
                Sudah Dibayar
              </div>

              {/* Session details */}
              <div className="p-6 border-b">
                <p className="text-gray-500">Jadwal Konselingmu</p>
                <p className="font-semibold text-xl mb-1">{counselingData?.formattedDate || '-'}</p>
                <p className="text-gray-500">
                  {counselingData?.method === 'Call' ? 'Voice Call' : 'Video Call'} - Google Meet
                </p>
              </div>

              {/* Psychologist details */}
              <div className="p-6">
                <div className="flex items-center gap-4">
                  <div className="w-16 h-16 rounded-full overflow-hidden bg-gray-100">
                    {counselingData?.psychologistImage ? (
                      <Image
                        width={64}
                        height={64}
                        src={counselingData.psychologistImage}
                        alt={counselingData?.psychologistName || 'Psychologist'}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="flex items-center justify-center h-full w-full">
                        <Image
                          width={64}
                          height={64}
                          src="/placeholder-avatar.jpg"
                          alt="Mahira Syafana"
                          className="w-full h-full object-cover"
                        />
                      </div>
                    )}
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">
                      {counselingData?.psychologistName || 'Mahira Syafana M.Psi. Psikolog'}
                    </h3>
                    <p className="text-gray-500 text-sm">
                      {counselingData?.specializations ||
                        'Percintaan, Keluarga, Kecemasan, Organisasi, +4 lainnya'}
                    </p>
                  </div>
                </div>
              </div>
            </Card>

            <div className="flex justify-center mt-6">
              <ButtonPrimary variant="contained" className="px-8 py-3" onClick={() => router.push('')}>
                Lihat Detail Jadwal
              </ButtonPrimary>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Render failed status display
  const renderFailedStatus = () => {
    return (
      <div className="flex flex-col items-center justify-center px-4 py-12">
        <Card className="p-6 border rounded-lg max-w-md w-full shadow-lg bg-white">
          <div className="flex flex-col items-center justify-center py-8">
            <div className="w-16 h-16 rounded-full bg-red-100 flex items-center justify-center mb-6">
              <SVGIcons name={IIcons.Close} className="text-red-500 h-8 w-8" />
            </div>
            <h2 className="text-xl font-bold mb-2">Pembayaran Gagal</h2>
            <p className="text-gray-500 mb-8">Maaf, pembayaran Anda tidak dapat diproses</p>

            {transactionId && (
              <div className="mb-6 w-full">
                <p className="text-sm text-gray-500 text-center">ID Transaksi</p>
                <p className="text-center font-medium">{transactionId}</p>
              </div>
            )}
          </div>
        </Card>
      </div>
    )
  }

  const renderStatusContent = () => {
    if (isLoading) {
      return (
        <div className="flex flex-col items-center justify-center h-full py-8 mt-28">
          <div className="w-16 h-16 rounded-full bg-white bg-opacity-50 flex items-center justify-center mb-6">
            <SVGIcons name={IIcons.Repeat} className="animate-spin" />
          </div>
          <h2 className="text-xl text-white font-bold mb-2">Memuat status pembayaran...</h2>
          <p className="text-white">Mohon tunggu sebentar</p>
        </div>
      )
    }

    if (paymentStatus === 'success') {
      return renderSuccessStatus()
    } else {
      return renderFailedStatus()
    }
  }

  return (
    <div className="min-h-screen flex flex-col">
      <div className="absolute inset-0 z-0">
        <div className="h-2/3 bg-main-100 rounded-b-2xl"></div>
      </div>

      {/* Content container */}
      <div className="relative z-10 flex-grow flex flex-col">{renderStatusContent()}</div>
    </div>
  )
}

export default BookingStatusPage
