import { AppBigCaption, AppMediumText, Flex } from '@/components/_common/ui'
import Image from 'next/image'
import { twMerge } from 'tailwind-merge'
import { AvatarWithInfo } from '../_common/CardInfo/AvatarWithInfo'
import { useEffect, useState } from 'react'
type UserPhotoProps = {
  photo: string
  title: string
  subTitle?: string
  className?: string
}

export const UserPhoto = ({ photo, title, subTitle, className }: UserPhotoProps) => {
  const [image, setImage] = useState(photo)
  useEffect(() => {
    setImage(photo)
  }, [photo])
  return (
    <AvatarWithInfo
      wrapClassName="items-center flex-start gap-x-2 gap-y-0"
      className="w-8 h-8"
      image={image}
      alt={title}
    >
      <div className="flex flex-col gap-y-1">
        <AppMediumText className="text-gray-400 capitalize">{title}</AppMediumText>
        <AppBigCaption className="text-gray-200">{subTitle}</AppBigCaption>
      </div>
    </AvatarWithInfo>
  )
}
