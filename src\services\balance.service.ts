import { config } from '@/constans/config'
import { httpRequest } from '@/utils/network'

export class BalanceService {
  async getTransactionListing() {
    return await httpRequest({
      method: 'get',
      url: `${config?.apiBaseUrl}api/psychologists/balance/transactions?page=1&perPage=1000`,
    })
  }
  async getPsychologistBalance() {
    return await httpRequest({
      method: 'get',
      url: `${config?.apiBaseUrl}api/psychologists/balance`,
    })
  }

  async getPsychologistBalanceHistoryById({
    id,
    types,
    page = 1,
    perPage = 10,
  }: {
    id: string
    types?: string[]
    page?: number
    perPage?: number
  }) {
    try {
      const whereConditions: Record<string, any> = {}

      if (types) {
        if (types.length === 0) {
          return null
        } else {
          whereConditions.type = { in: types }
        }
      }

      const url = `${config?.apiBaseUrl}api/psychologists/${id}/transactions?where=${JSON.stringify(whereConditions)}&page=${page}&perPage=${perPage}`

      console.log('URL:', url)
      console.log('Where Conditions:', whereConditions)

      return await httpRequest({
        method: 'get',
        url,
      })
    } catch (error) {
      console.error('Error in getPsychologistBalanceHistoryById:', error)
      throw error
    }
  }
}

export const balanceService = new BalanceService()
