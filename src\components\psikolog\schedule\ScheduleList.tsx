import { ActionItemProps, ScheduleItem } from './ScheduleItem'
import { ScheduleListProps } from '@/store/psikolog/schedule.reducer'

const ScheduleList = ({
  onStartCounselling,
  onReschedule,
  onApprove,
  onReject,
  onCreateClientReport,
  onChangeCLientReport,
  onCancelSession,
  onConfirmationSession,
  itemList,
  isLoadingApp,
}: ActionItemProps) => {
  return (
    <div className="grid grid-cols-6 grid-rows-1">
      <div className="grid col-span-6 lg:col-span-5 xl:col-span-4">
        <ScheduleItem
          itemList={itemList as ScheduleListProps[]}
          {...{
            isLoadingApp,
            onStartCounselling,
            onReschedule,
            onApprove,
            onReject,
            onCreateClientReport,
            onChangeCLientReport,
            onCancelSession,
            onConfirmationSession,
          }}
        />
      </div>
    </div>
  )
}

export default ScheduleList
