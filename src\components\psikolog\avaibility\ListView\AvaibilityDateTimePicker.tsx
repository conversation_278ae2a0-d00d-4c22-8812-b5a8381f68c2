import { AppModal } from '@/components/_common/Modal/AppModal'
import { Calendar } from '@/components/ui/calendar'
import { Separator } from '@/components/ui/separator'
import { useState, useEffect } from 'react'
import { addHours, format, isBefore } from 'date-fns'
import { id } from 'date-fns/locale'
import { TimeListAvaibility } from './TimeListAvaibility'
import { useToast } from '@/components/ui/use-toast'
import ButtonPrimary from '@/components/_common/ButtonPrimary'
import { INPUT_DATE_FORMAT } from '@/constans/date'
import { AvaibilityItemProps } from '../useAvaibilityList.hook'
import { getEndTime, getStartTime } from '../../utils/getAvaibilityTime'
import { PayloadSepcificAvaibility } from '@/services/avaibility.service'
import { useSelector } from '@/store'

type AvaibilityDateTimePickerProps = {
  id?: string
  open: boolean
  date: Date | undefined
  timeList: AvaibilityItemProps[]
  onSubmit: (val: any) => void
  onOpen: (val: boolean) => void
}

const AvaibilityDateTimePicker = ({
  id: targetId,
  open,
  date,
  timeList,
  onOpen,
  onSubmit,
}: AvaibilityDateTimePickerProps) => {
  const { toast } = useToast()
  const [avaibilityDate, setAvaibilityDate] = useState<Date | undefined>(date)
  const [timeListAvaibility, setTimeListAvaibility] = useState<AvaibilityItemProps[]>(timeList)
  const [dateAvaibility, setDateAvaibility] = useState<string>(
    date ? format(date, INPUT_DATE_FORMAT, { locale: id }) : ''
  )
  const { userIdentity } = useSelector((state) => state.PsychologistProfile)
  const timezone = userIdentity?.userConfig?.TIMEZONE ?? 'Asia/Jakarta'

  useEffect(() => {
    setDateAvaibility(date ? format(date, INPUT_DATE_FORMAT, { locale: id }) : '')
    setTimeListAvaibility(timeList)
    setAvaibilityDate(date)
  }, [date, timeList])

  const onSelect = (e: any) => {
    setAvaibilityDate(e)
    setDateAvaibility(format(e, INPUT_DATE_FORMAT, { locale: id }))
  }

  const initialTime: AvaibilityItemProps = {
    id: null,
    startTime: getStartTime(timeListAvaibility),
    endTime: getEndTime(getStartTime(timeListAvaibility)),
    psychologistId: '',
    day: 0,
    date: dateAvaibility ? dateAvaibility + 'T00:00:00.000Z' : '',
    timezone: timezone,
    createdAt: '',
    modifiedAt: '',
  }

  const handleAddItem = (val: string) => {
    if (timeList.length >= 12) {
      toast({
        variant: 'danger',
        title: 'Anda sudah mencapai jadwal maksimal, jangan lupa beristirahat :D',
      })
      return
    } else {
      setTimeListAvaibility((prev) => [...prev, { ...initialTime, id: null }])
    }
  }

  const handleRemoveItem = (val: AvaibilityItemProps) => {
    setTimeListAvaibility((prev) => prev.filter((v) => v.id !== val.id))
  }

  const handleSetStartTime = (val: AvaibilityItemProps) => {
    setTimeListAvaibility((prev) =>
      prev.map((time) =>
        time.id === val.id
          ? {
              ...time,
              endTime: isBefore(
                new Date(`01/01/1000 ${time.endTime}`),
                new Date(`01/01/1000 ${val.startTime}`)
              )
                ? format(addHours(new Date(`01/01/1000 ${val.startTime}`), 1), 'kk:mm')
                : time.endTime,
              startTime: val.startTime,
            }
          : time
      )
    )
  }
  const handleSetEndTime = (val: AvaibilityItemProps) => {
    setTimeListAvaibility((prev) =>
      prev.map((time) =>
        time.id === val.id
          ? {
              ...time,
              endTime: val.endTime,
            }
          : time
      )
    )
  }

  const handleReset = () => {
    setDateAvaibility('')
    setAvaibilityDate(undefined)
    setTimeListAvaibility([])
  }

  const handleSubmit = () => {
    let payload: PayloadSepcificAvaibility[] = []
    if (targetId) {
      timeListAvaibility.map((time) => {
        const dateUTCFormat = time.date ? time.date : ''
        payload.push({
          id: time.id,
          timezone: timezone,
          date: dateUTCFormat,
          startTime: time.startTime,
          endTime: time.endTime,
        })
      })
    } else {
      timeListAvaibility.map((time) => {
        const dateUTCFormat = time.date ? time.date : ''
        payload.push({
          timezone: timezone,
          date: dateUTCFormat,
          startTime: time.startTime,
          endTime: time.endTime,
        })
      })
    }
    onSubmit(payload)
    handleReset()
    onOpen(false)
  }

  return (
    <AppModal title="Pilih spesifik tanggal dan jam" open={open} onClose={onOpen}>
      <div className="flex w-full justify-center">
        <Calendar
          locale={id}
          size="lg"
          className=""
          mode="single"
          selected={avaibilityDate}
          onSelect={onSelect}
          fromDate={new Date()}
          initialFocus
        />
      </div>
      <Separator orientation="horizontal" />
      {dateAvaibility && (
        <div className="flex xs:px-3 sm:px-0">
          <TimeListAvaibility
            id={10}
            addItem={(val) => handleAddItem(val)}
            onRemoveTime={(val) => handleRemoveItem(val)}
            onSetStartTime={(val) => handleSetStartTime(val)}
            onSetEndTime={(val) => handleSetEndTime(val)}
            day={dateAvaibility}
            timeList={timeListAvaibility}
            isActive={true}
            hideCopyButton={true}
          />
        </div>
      )}
      <div className="flex gap-4 justify-end pt-2">
        <ButtonPrimary
          className="px-8"
          variant="outlined"
          size="xs"
          color="gray"
          onClick={() => onOpen(false)}
        >
          Batal
        </ButtonPrimary>
        <ButtonPrimary
          className="px-8"
          variant="contained"
          size="xs"
          color="primary"
          onClick={() => handleSubmit()}
        >
          Simpan
        </ButtonPrimary>
      </div>
    </AppModal>
  )
}

export default AvaibilityDateTimePicker
