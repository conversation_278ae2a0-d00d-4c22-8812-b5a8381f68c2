import { IIcons, SVGIcons } from '@/components/_common/icon'

export default function ClientReportDetailsHeader({
  client,
  psikolog,
  date,
  time,
  via,
}: {
  client: string
  psikolog: string
  date: string
  time: string
  via: string
}) {
  return (
    <div className="flex flex-col gap-4">
      <h2 className="text-[16px] font-bold text-[#222222]">
        Klien Report <span className="text-[#039EE9]">{client}</span> dari
        <span className="text-[#039EE9]"> Psikolog {psikolog}</span>
      </h2>
      {/* jadwal */}
      <div className="flex items-center gap-3 md:gap-6 flex-wrap">
        <div className="flex items-center gap-1">
          <SVGIcons className="mr-2" name={IIcons.Calendar} />
          <span>{date}</span>
        </div>
        <div className="flex items-center gap-1">
          <SVGIcons className="mr-2" name={IIcons.Clock} />
          <span>{time}</span>
        </div>
        <div className="flex items-center gap-1">
          {via === 'Call' ? (
            <SVGIcons className="mr-2" name={IIcons.Call} />
          ) : (
            <SVGIcons className="mr-2" name={IIcons.VideoCall} />
          )}
          <span>{via}</span>
        </div>
      </div>
    </div>
  )
}
