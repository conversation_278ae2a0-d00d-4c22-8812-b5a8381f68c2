export const LoadingTableSkeleton = () => {
  return (
    <div className="animate-pulse space-y-2">
      {/* Table Header Placeholder */}
      <div className="flex flex-row gap-4">
        <div className="h-8 bg-slate-200 rounded w-1/4"></div>
        <div className="h-8 bg-slate-200 rounded w-1/4"></div>
        <div className="h-8 bg-slate-200 rounded w-1/4"></div>
        <div className="h-8 bg-slate-200 rounded w-1/4"></div>
      </div>

      {/* Table Rows Placeholders */}
      {[...Array(5)].map((_, index) => (
        <div key={index} className="flex flex-row gap-4">
          <div className="h-6 bg-slate-200 rounded w-1/4"></div>
          <div className="h-6 bg-slate-200 rounded w-1/4"></div>
          <div className="h-6 bg-slate-200 rounded w-1/4"></div>
          <div className="h-6 bg-slate-200 rounded w-1/4"></div>
        </div>
      ))}
    </div>
  )
}
