'use client'
import ButtonPrimary from '@/components/_common/ButtonPrimary'
import AppInput from '@/components/_common/input/Input'
import { UseFormGetValues, UseFormRegister, UseFormReturn, UseFormSetValue } from 'react-hook-form'
import { AdminValuesType } from './AdminMain'
import { AppSelect } from '@/components/_common/Select/AppSelect'
import { AdminRoleOptions } from '@/constans/StaticOptions'

export type AdminFormProps = {
  register: UseFormRegister<AdminValuesType>
  getValues: UseFormGetValues<AdminValuesType>
  setValue: UseFormSetValue<AdminValuesType>
  errors: UseFormReturn['formState']['errors']
  onSubmit: () => void
  onCancel: () => void
  isLoading: boolean
  isDisabled: boolean
}

export const AdminForm = ({ register, getValues, setValue, errors, onSubmit, onCancel, isLoading, isDisabled }: AdminFormProps) => {
  return (
    <form
      onSubmit={(e) => {
        e.preventDefault()
        e.stopPropagation()
        onSubmit()
      }}
      className="flex flex-col gap-6 w-full md:w-[70%]"
    >
      <AppInput
        {...register('fullName')}
        label="Nama Lengkap*"
        type="text"
        name="fullName"
        errorMsg={!!errors.fullName ? String(errors.fullName.message) : undefined}
      />
      <AppInput
        {...register('nickname')}
        label="Nama Panggilan*"
        type="text"
        name="nickname"
        errorMsg={!!errors.nickname ? String(errors.nickname.message) : undefined}
      />
      <AppInput
        {...register('email')}
        label="Email*"
        type="email"
        name="email"
        errorMsg={!!errors.email ? String(errors.email.message) : undefined}
      />
      <AppInput
        {...register('phoneNumber')}
        label="No. Handphone*"
        type="text"
        name="phoneNumber"
        errorMsg={!!errors.phoneNumber ? String(errors.phoneNumber.message) : undefined}
      />
      <AppInput
        {...register('password')}
        label="Password*"
        type="password"
        name="password"
        errorMsg={!!errors.password ? String(errors.password.message) : undefined}
      />
      <div className="flex gap-4 items-center justify-end mt-6">
        <ButtonPrimary
          onClick={() => onCancel()}
          className="min-w-[140px]"
          size="sm"
          variant="outlined"
          color="gray"
          type="button"
        >
          Batal
        </ButtonPrimary>
        <ButtonPrimary
          className="min-w-[140px]"
          onClick={() => {
            window.scrollTo({ top: 0, behavior: 'smooth' })
          }}
          size="sm"
          variant="contained"
          isLoading={isLoading}
          disabled={isDisabled}
          type="submit"
        >
          Simpan
        </ButtonPrimary>
      </div>
    </form>
  )
}
