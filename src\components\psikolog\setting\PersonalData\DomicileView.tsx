import { useDistricts } from '@/hooks/useDistricts.hook'
import { useProvince } from '@/hooks/useProvince.hook'
import { useRegencies } from '@/hooks/useRegencies.hook'
import { useSubDistricts } from '@/hooks/useSubdistricts.hook'
import { capitalizeEachWord } from '@/utils/stringUtils'

export const DomicileView = ({ domicile }: { domicile: string[] }) => {
  const idProvince = domicile[0]?.replace(/\s/g, '') ?? ''
  const idRegencies = domicile[1]?.replace(/\s/g, '') ?? ''
  const idDistricts = domicile[2]?.replace(/\s/g, '') ?? ''
  const idSubDistricts = domicile[3]?.replace(/\s/g, '') ?? ''

  const { data: Province } = useProvince()
  const { data: Regencies } = useRegencies(idProvince)
  const { data: Districts } = useDistricts(idRegencies)
  const { data: SubDistricts } = useSubDistricts(idDistricts)

  const provinceName = Province?.find((val: any) => val.id === idProvince)?.name ?? ''
  const regenciesName = Regencies?.find((val: any) => val.id === idRegencies)?.name ?? ''
  const districtsName = Districts?.find((val: any) => val.id === idDistricts)?.name ?? ''
  const subDistrictsName = SubDistricts?.find((val: any) => val.id === idSubDistricts)?.name ?? ''

  const location = [provinceName, regenciesName, districtsName, subDistrictsName].map((val) => {
    if (val) {
      return capitalizeEachWord(val)
    }
  })

  return <div className="capitalize">{location.join(', ')}</div>
}
