import ButtonPrimary from '@/components/_common/ButtonPrimary'
import { Card } from '@/components/_common/ui'
import Image from 'next/image'

type FeatureProps = {
  image: string
  service: string[]
  title: string
  subTitle: string
  label: string
  isTypeCounselling?: boolean
  handleClick: () => void
}

export const FeatureCard = ({
  image,
  service,
  title,
  subTitle,
  label,
  isTypeCounselling,
  handleClick = () => {},
}: FeatureProps) => {
  return (
    <Card className="p-3 xs:p-3 sm:p-3 md:p-3 bg-white flex flex-col justify-start gap-y-4 min-h-full">
      <div className="relative h-[200px] min-h-[200px] w-full">
        <Image
          src={image}
          alt="mh-image"
          fill
          style={{
            objectFit: isTypeCounselling ? 'contain' : 'cover',
            objectPosition: isTypeCounselling ? 'bottom' : 'center',
            borderRadius: '15px',
          }}
        />
      </div>
      <div className="grid gap-y-1">
        <span className="text-subheading-md font-bold text-gray-400">{title}</span>
        <span className="text-caption-md font-medium text-gray-300">{subTitle}</span>
      </div>
      <ServiceList list={service} isTypeCounselling={isTypeCounselling} />
      <div className="flex w-full h-full grow items-end">
        <ButtonPrimary
          variant="outlined"
          size="xs"
          className={
            isTypeCounselling ? 'hidden md:flex w-full rounded-full h-fit' : 'w-fit rounded-full h-fit'
          }
          onClick={() => handleClick && handleClick()}
        >
          {label}
        </ButtonPrimary>
      </div>
    </Card>
  )
}

const ServiceList = ({ list, isTypeCounselling }: { list: string[]; isTypeCounselling?: boolean }) => {
  return (
    <div className="grid gap-y-2">
      <span className="text-gray-400 text-caption-md">
        {isTypeCounselling ? 'Topik dapat meliputi:' : 'Layanan:'}
      </span>
      <div className="flex flex-wrap gap-2">
        {list.map((item) => {
          return (
            <span key={item} className="bg-main-50 text-gray-400 text-caption-md rounded-full py-[6px] px-3">
              {item}
            </span>
          )
        })}
      </div>
    </div>
  )
}
