import { useState, useEffect } from 'react'
import { httpRequest } from '@/utils/network'
import { config } from '@/constans/config'

interface ProblemCategory {
  id: string
  problemCategory: string
  iconUrl: string | null
  order: number
  createdAt: string
  createdBy: string
  modifiedAt: string
  modifiedBy: string
}

export const useProblemCategories = () => {
  const [categories, setCategories] = useState<ProblemCategory[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchCategories = async () => {
    setLoading(true)
    setError(null)

    try {
      const response = await httpRequest({
        method: 'get',
        url: `${config?.apiBaseUrl}api/problem-category`,
      })

      setCategories(response)
      return response
    } catch (err) {
      setError('Failed to fetch problem categories')
      console.error('Error fetching problem categories:', err)
      return null
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchCategories()
  }, [])

  return {
    categories,
    loading,
    error,
    fetchCategories,
  }
}
