import Image from 'next/image'
import { StepItem } from './StepItem'
import Link from 'next/link'

const ListSteps = [
  {
    id: 1,
    label: 'Cari Psikolog yang Cocok',
    content: 'Temukan Psikolog yang sesuai dengan permasalahanmu.',
  },
  { id: 2, label: '<PERSON><PERSON>', content: '<PERSON><PERSON> gambaran permasalahan yang sedang kamu hadapi.' },
  {
    id: 3,
    label: 'Lakukan Pembayaran',
    content: 'Kamu bisa langsung bayar dengan berbagai pilihan metode yang tersedia.',
  },
  {
    id: 4,
    label: 'Konseling Berjalan',
    content: 'Waktunya konseling! Ceritakan keluhanmu dengan Psikolog secara nyaman.',
  },
  {
    id: 5,
    label: '<PERSON><PERSON><PERSON>, kamu hebat!',
    content:
      'Terima kasih sudah berjuang sejauh ini. Kami percaya bahwa kamu bisa menjadi versi terbaik dirimu.',
  },
]
export const HowToComponent = () => {
  return (
    <div className="grid gap-y-10">
      <span className="text-subheading-md md:text-[38px] font-bold text-gray-400 text-center">
        J<PERSON> gimana kalau aku mau konseling?
      </span>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 w-full">
        {ListSteps.map((step) => (
          <StepItem key={step.id} {...step} />
        ))}
        <div className="p-6 w-full bg-[#34B1ED] flex justify-start items-center rounded-lg text-white font-bold relative">
          <span className="text-body-lg font-bold">
            Perlu bantuan? <p />
            <Link
              href={
                'https://api.whatsapp.com/send/?phone=6285173025865&text=Hi+Mental+Healing%2C+%28isi+pesan+kamu+disini%29&type=phone_number&app_absent=0'
              }
              target="_blank"
            >
              <span className="text-body-sm font-bold underline">Hubungi Kami</span>
            </Link>
          </span>
          <Image
            src={'/ilustration/call-me.svg'}
            alt="call-me-img"
            width={106}
            height={106}
            className="hidden md:block"
            style={{
              width: 106,
              position: 'absolute',
              right: 30,
              bottom: 0,
            }}
          />
          <Image
            src={'/ilustration/call-me.svg'}
            alt="call-me-img"
            width={80}
            height={80}
            className="md:hidden"
            style={{
              width: 80,
              position: 'absolute',
              right: 30,
              bottom: 0,
            }}
          />
        </div>
      </div>
    </div>
  )
}
