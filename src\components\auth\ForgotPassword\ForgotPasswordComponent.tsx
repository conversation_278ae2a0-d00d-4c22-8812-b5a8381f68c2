'use client'
import AppInput from '@/components/_common/input/Input'
import Title from '../Title'
import ButtonPrimary from '@/components/_common/ButtonPrimary'

import { yupResolver } from '@hookform/resolvers/yup'
import { useForm } from 'react-hook-form'
import * as yup from 'yup'
import { useRouter } from 'next/navigation'
import Translation from '@/constans/Translation'
import { authService } from '@/services/auth.service'
import { useState } from 'react'
import { useToast } from '@/components/ui/use-toast'
import { Routes } from '@/constans/routes'

const validationSchema = yup.object().shape({
  email: yup.string().required(Translation.RequiredEmail).email(Translation.ValidEmail),
})

export const ForgotPasswordComponent = () => {
  const { toast } = useToast()
  const router = useRouter()
  const [isLoading, setIsLoading] = useState<boolean>(false)

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(validationSchema),
  })

  async function onSubmit(data: { email: string }) {
    setIsLoading(true)
    try {
      await authService
        .forgotPassword(data.email)
        .then((res) => {
          setTimeout(() => {
            toast({
              variant: 'success',
              title:
                res?.message || 'Berhasil mengirim tautan atur ulang kata sandi. Silahkan cek email anda.',
            })
            setIsLoading(false)
            router.replace(Routes.UserForgotPasswordConfirmation + '?email=' + data.email)
          }, 1000)
        })
        .catch((err) => {
          console.log(err)
          setTimeout(() => {
            toast({
              variant: 'danger',
              title: 'Gagal mengirim tautan atur ulang kata sandi. Silahkan hubungi admin.',
            })
            setIsLoading(false)
          }, 1000)
        })
    } catch (error) {
      setTimeout(() => {
        toast({
          variant: 'danger',
          title: 'Terjadi masalah teknis. Silahkan hubungi admin.',
        })
        setIsLoading(false)
      }, 1000)
    } finally {
      setTimeout(() => {
        setIsLoading(false)
      }, 1000)
    }
  }
  return (
    <div className="grid gap-y-4">
      <Title
        title="Lupa Password"
        subTitle="Masukan email Anda, kami akan kirimkan link untuk reset password Anda."
      />
      <AppInput
        {...register('email')}
        errorMsg={!!errors.email ? errors.email.message : undefined}
        className="pt-0"
        label="Email"
        type="email"
        name="email"
      />
      <ButtonPrimary
        variant="contained"
        size="xs"
        className="w-full p-3 min-h-[48px] space-x-2"
        onClick={() => handleSubmit(onSubmit)()}
        isLoading={isLoading}
      >
        {'Kirim'}
      </ButtonPrimary>
    </div>
  )
}

export default ForgotPasswordComponent
