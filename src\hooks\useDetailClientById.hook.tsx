import { clientsService } from '@/services/client.service'
import { useQuery } from '@tanstack/react-query'

export const useDetailClientsById = (clientId: string) => {
  return useQuery({
    queryKey: ['ClientDetails', { clientId }],
    queryFn: () =>
      clientsService
        .getClientsDetails(clientId)
        .then((response) => {
          return response
        })
        .catch((error) => {
          return null
        }),
  })
}
