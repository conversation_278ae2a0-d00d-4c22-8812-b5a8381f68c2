'use client'

import { CardUSerInfo } from '@/components/_common/CardInfo/CardUserInfo'
import { ListInfo } from '@/components/_common/CardInfo/ListInfo'
import TabList from '@/components/_common/tabs/TabList'
import { BigTitleTypography } from '@/components/_common/ui'
import TabItem from '@/components/_common/tabs/TabItem'
import { LoadingCardWithAvatar } from '@/components/loading/LoadingCard'
import { LoadingDetailPsychologist } from '@/components/loading/LoadingDetailPsychologist'
import { ProfileClient } from './ProfileClient'
import { useDetailClientsById } from '@/hooks/useDetailClientById.hook'
import { ListingCounselling } from './ListingCounselling'

const DetailClientComponent = ({ idClient }: { idClient: string }) => {
  const { data, isLoading, isPending } = useDetailClientsById(idClient)
  const isLoadingApp = isLoading || isPending
  const clientName = data?.fullName ?? ''
  const clientPhoto = data?.profilePhoto ?? ''
  const phoneNumber = data?.phoneNumber ?? ''
  const listInfo = [idClient, phoneNumber]
  console.log(data)
  const detailPsychologist = data ?? {}
  const CounsellingData = [
    {
      label: 'Konseling',
      content: <ListingCounselling id={idClient} />,
    },
    {
      label: 'Profil Klien',
      content: <ProfileClient {...detailPsychologist} />,
    },
  ]
  if (isLoadingApp) return <LoadingDetailPsychologist />
  return (
    <>
      <div className="flex sm:flex sm:items-center pb-6">
        <BigTitleTypography>Detail Klien</BigTitleTypography>
      </div>
      <div className="grid grid-flow-row-dense grid-cols-6 grid-rows-1 gap-4">
        {isLoadingApp ? (
          <div className="col-span-6 xs:col-span-6 sm:col-span-6 xl:col-span-4 flex items-center gap-x-6 p-2 xs:p-3 sm:p-4 xl:p-6">
            <LoadingCardWithAvatar />
          </div>
        ) : (
          <CardUSerInfo
            className="col-span-6 xs:col-span-6 sm:col-span-6 xl:col-span-4 flex items-center gap-x-6 p-2 xs:p-3 sm:p-4 xl:p-6"
            image={clientPhoto}
            heading={clientName}
            alt={clientName}
            subHeading={<ListInfo list={listInfo} />}
          />
        )}
      </div>
      <div className="gap-4 mt-4">
        <TabList className="sticky top-navbar z-30 bg-white" activeTabIndex={0}>
          {CounsellingData.map((counselling, index) => {
            return (
              <TabItem className="bg-main-100" key={index} label={counselling.label}>
                {counselling.content}
              </TabItem>
            )
          })}
        </TabList>
      </div>
    </>
  )
}

export default DetailClientComponent
