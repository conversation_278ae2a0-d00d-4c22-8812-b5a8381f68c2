import AppInput from '@/components/_common/input/Input'
import { RadioInput } from '@/components/_common/RadioInput/RadioInput'
import { OnboardStepOneProps } from './OnboardStepOne'
import { InputImage } from '@/components/_common/InputImage'
import { useEffect, useRef, useState } from 'react'
import { getDownloadURL, ref as storageRef, uploadBytesResumable } from 'firebase/storage'
import { useToast } from '@/components/ui/use-toast'
import { firebaseAuth, storage } from '@/lib/firebase/config'
import { useDispatch } from '@/store'
import { setPhotoURL } from '@/store/auth/auth.reducer'
import { updateProfile } from 'firebase/auth'
import {
  EducationOptionsList,
  EthnicityoptionsList,
  MaritalStatusOptions,
  OccupationOptionsList,
  Otheroptions,
  ReligionOptionsList,
} from '@/constans/onboardOptions'
import { DomiciliesOptions } from './DomiciliesOptions'
import { AppSelect } from '@/components/_common/Select/AppSelect'

type OnboardStepTwoProps = OnboardStepOneProps

export const OnboardStepTwo = ({ register, getValues, setValue, errors, onSubmit }: OnboardStepTwoProps) => {
  const dispatch = useDispatch()
  const { toast } = useToast()
  const ref = useRef<any>(null)

  const uploadImage = async (imageUpload: any) => {
    const firebaseUser = firebaseAuth.currentUser
    if (imageUpload) {
      const imageRef = storageRef(
        storage,
        `profile/${firebaseUser?.uid ? firebaseUser?.uid + imageUpload.type : imageUpload.name}`
      )
      const uploadTask = uploadBytesResumable(imageRef, imageUpload)

      uploadTask.on(
        'state_changed',
        (snapshot) => {
          const percent = Math.round((snapshot.bytesTransferred / snapshot.totalBytes) * 100)
          // update progress will enable if needed
          // setProgress(percent)
        },
        (err) => {
          toast({
            variant: 'danger',
            title: 'Proses perbaharui photo profil mengalami masalah',
          })
        },
        () => {
          getDownloadURL(uploadTask.snapshot.ref)
            .then(async (url) => {
              await updateProfile(firebaseUser!, {
                photoURL: url,
              })
              toast({
                variant: 'success',
                title: 'Perbaharui photo profil berhasil',
              })
              setValue('profilePhoto', url, { shouldValidate: true })
              dispatch(setPhotoURL(url))
            })
            .catch((err) => {
              toast({
                variant: 'danger',
                title: 'Perbaharui photo profil gagal',
              })
            })
        }
      )
    }
  }

  return (
    <>
      <InputImage
        {...register('profilePhoto')}
        name="profilePhoto"
        label="Foto"
        accept={'image/jpeg,image/png'}
        onChange={(file) => {
          if (file) {
            uploadImage(file)
          }
        }}
        preview={getValues('profilePhoto') || ''}
        objectPosition="center"
        inputRef={ref}
      />
      <RadioInput
        options={MaritalStatusOptions}
        name={'maritalStatus'}
        value={getValues('maritalStatus')!}
        label="Status Pernikahan"
        errorMsg={!!errors.maritalStatus ? String(errors.maritalStatus.message) : undefined}
        onChange={(val) => {
          setValue('maritalStatus', val, { shouldValidate: true })
        }}
      />
      <div className="grid gap-2">
        <label className="text-body-md font-bold text-gray-400">Urutan Bersaudara*</label>
        <div className="flex gap-4 items-center">
          <AppInput
            {...register('childTo')}
            className="pt-0 max-w-[105px]"
            type="number"
            name="childTo"
            errorMsg={!!errors.childTo ? String(errors.childTo.message) : undefined}
            placeholder="Anak ke"
          />
          <span>Dari</span>
          <AppInput
            {...register('totalSibling')}
            className="pt-0 grow"
            type="number"
            name="totalSibling"
            errorMsg={!!errors.totalSibling ? String(errors.totalSibling.message) : undefined}
            placeholder="Jumlah Saudara"
          />
        </div>
      </div>
      <AppSelect
        {...register('education')}
        options={EducationOptionsList || []}
        onChange={(val) => setValue('education', val, { shouldValidate: true })}
        value={getValues('education') ? String(getValues('education')) : ''}
        className="h-[50px]"
        label="Pendidikan"
        name="education"
        placeholder="Pilih Pendidikan"
        errorMsg={!!errors.education ? String(errors.education.message) : undefined}
      />
      <AppSelect
        {...register('occupation')}
        options={OccupationOptionsList || []}
        onChange={(val) => setValue('occupation', val, { shouldValidate: true })}
        value={getValues('occupation') ? String(getValues('occupation')) : ''}
        className="h-[50px]"
        label="Pekerjaan"
        name="occupation"
        placeholder="Pilih Pekerjaan"
        errorMsg={!!errors.occupation ? String(errors.occupation.message) : undefined}
      />
      {getValues('occupation') === Otheroptions && (
        <AppInput
          {...register('occupationOther')}
          className="pt-0"
          type="text"
          name="occupationOther"
          placeholder="Silahkan masukan pekerjaan lainnya"
          errorMsg={!!errors.occupationOther ? String(errors.occupationOther.message) : undefined}
        />
      )}

      <DomiciliesOptions
        register={register}
        getValues={getValues}
        setValue={setValue}
        errors={errors}
        onSubmit={onSubmit}
        onFinalSetValue={(val: string) => setValue('domicile', val, { shouldValidate: true })}
      />
      <AppSelect
        {...register('ethnicity')}
        options={EthnicityoptionsList || []}
        onChange={(val) => setValue('ethnicity', val, { shouldValidate: true })}
        value={getValues('ethnicity') ? String(getValues('ethnicity')) : ''}
        className="h-[50px]"
        label="Suku Bangsa"
        name="ethnicity"
        placeholder="Pilih Suku Bangsa"
        errorMsg={!!errors.ethnicity ? String(errors.ethnicity.message) : undefined}
      />
      {getValues('ethnicity') === Otheroptions && (
        <AppInput
          {...register('ethnicityOther')}
          className="pt-0"
          type="text"
          name="ethnicityOther"
          placeholder="Silahkan masukan suku bangsa lainnya"
          errorMsg={!!errors.ethnicityOther ? String(errors.ethnicityOther.message) : undefined}
        />
      )}
      <AppSelect
        {...register('religion')}
        options={ReligionOptionsList || []}
        onChange={(val) => setValue('religion', val, { shouldValidate: true })}
        value={getValues('religion') ? String(getValues('religion')) : ''}
        className="h-[50px]"
        label="Agama"
        name="religion"
        placeholder="Pilih Agama"
        errorMsg={!!errors.religion ? String(errors.religion.message) : undefined}
      />
      {getValues('religion') === Otheroptions && (
        <AppInput
          {...register('religionOther')}
          className="pt-0"
          type="text"
          name="religionOther"
          placeholder="Silahkan masukan agama lainnya"
          errorMsg={!!errors.religionOther ? String(errors.religionOther.message) : undefined}
        />
      )}
    </>
  )
}
