import { AppMediumText } from '../ui'

type StatusType = {
  variant: 'warn' | 'primary' | 'success' | 'danger' | 'disable'
  label: string
}

export const Status = ({ variant, label }: StatusType) => {
  const varianColor =
    variant === 'warn'
      ? 'bg-warning-100'
      : variant === 'primary'
        ? 'bg-[#4781F3]'
        : variant === 'success'
          ? 'bg-success-100'
          : variant === 'danger'
            ? 'bg-danger-100'
            : 'bg-gray-100'
  return (
    <span className="flex flex-start items-center gap-x-2">
      <span className={`h-2.5 w-2.5 rounded-full ${varianColor}`}></span>
      {label && <AppMediumText>{label}</AppMediumText>}
    </span>
  )
}
