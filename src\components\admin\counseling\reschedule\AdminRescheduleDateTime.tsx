import ButtonPrimary from '@/components/_common/ButtonPrimary'
import { AvatarWithInfo } from '@/components/_common/CardInfo/AvatarWithInfo'
import { NoDataFound } from '@/components/_common/NoData/NoDataFound'
import { AppBigCaption, AppBigText } from '@/components/_common/ui'
import { WeeklyCalendarPicker } from '@/components/_common/WeeklyDatePicker/WeeklyCalendar'
import { LoadingTableSkeleton } from '@/components/loading/LoadingTable'
import { LoadingTimeList } from '@/components/loading/LoadingTimeList'
import { useToast } from '@/components/ui/use-toast'
import { MOMENT_INPUT_DATE_FORMAT } from '@/constans/date'
import useGetTimezoneLabel from '@/hooks/useGetTimezone.hook'
import { cn } from '@/lib/utils'
import { dispatch, useSelector } from '@/store'
import { setRescheduleDetail, setRescheduleStep } from '@/store/admin/counseling.reducer'
import moment from 'moment'

export const AdminRescheduleDateTime = ({
  item,
  timeList = [],
  dateSelected,
  timeSelected,
  callbackToggleCalendar,
  onSelectedDate,
  onSelectedTime,
  onSubmit,
  isLoading,
}: {
  item: any
  timeList: any[]
  timeSelected: string
  dateSelected: Date | undefined
  callbackToggleCalendar?: (arg: boolean) => void
  onSelectedDate: (date: Date | undefined) => void
  onSelectedTime: (time: any) => void
  onSubmit?: ({ date, time }: { date: Date | undefined; time: string }) => void
  isLoading: boolean
}) => {
  const { toast } = useToast()
  const { rescheduleDetail, rescheduleStep } = useSelector((state) => state.AdminCounseling)

  const timeAvalaibleList = timeList?.[0]?.times?.length ? timeList?.[0]?.times : []
  const timezone = timeList[0] ? timeList[0].timezone : ''
  console.log(timeAvalaibleList, timeList[0], timeList)

  const handleConfirm = () => {
    if (!dateSelected || !timeSelected) {
      toast({
        variant: 'danger',
        title: 'Silahkan pilih tanggal dan waktu',
      })
      return
    }
    onSubmit && onSubmit({ date: dateSelected, time: timeSelected })
    dispatch(
      setRescheduleDetail({
        ...rescheduleDetail,
        date: moment(dateSelected).format(MOMENT_INPUT_DATE_FORMAT),
        time: timeSelected,
      })
    )
    dispatch(setRescheduleStep(rescheduleStep + 1))
  }

  const handleSelectedDate = (val: Date | undefined) => {
    onSelectedDate && onSelectedDate(val)
  }

  const timeZone = useGetTimezoneLabel(timezone)
  return (
    <>
      <div className="md:grid grid-cols-4 grid-rows-1 flex flex-col items-start gap-4 border-b border-line-200 pb-4 md:items-center">
        <span className="col-span-1 font-bold">Klien</span>
        <div className="col-span-3">
          <AvatarWithInfo
            className="w-[60px] h-[60px]"
            image={item?.client?.profilePhoto}
            name={item?.client?.fullName}
            heading={<AppBigText bold>{item?.client?.fullName}</AppBigText>}
            subHeading={<AppBigCaption className="text-gray-200">{item?.client?.occupation}</AppBigCaption>}
          />
        </div>
      </div>
      <div className="grid grid-cols-4 grid-rows-1 gap-2 items-center ">
        <span className="col-span-1 font-bold">Tanggal</span>
        <div className="col-span-4">
          <WeeklyCalendarPicker
            callbackToggle={(arg) => callbackToggleCalendar && callbackToggleCalendar(arg)}
            date={dateSelected}
            onSelect={(val) => handleSelectedDate(val)}
          />
        </div>
      </div>
      <div className="grid grid-cols-4 grid-rows-1 gap-2 items-center ">
        <span className="col-span-1 font-bold">Waktu</span>
        <div className="col-span-4 flex flex-wrap flex-row gap-2">
          {isLoading ? (
            <LoadingTimeList />
          ) : timeAvalaibleList?.length ? (
            timeAvalaibleList.map((itemTime: any) => {
              const isPastDate = moment(dateSelected).isBefore(moment(), 'day')
              const disableDate = isPastDate
              return (
                <ButtonPrimary
                  className={cn('rounded-[8px]', timeSelected === itemTime.time && 'bg-main-100/50')}
                  textSize="text-body-md"
                  textColor="text-gray-400"
                  key={itemTime.time}
                  variant="outlined"
                  size="xs"
                  disabled={disableDate}
                  onClick={() => !disableDate && onSelectedTime(itemTime.time)}
                >
                  {`${itemTime.time} ${timeZone}`}
                </ButtonPrimary>
              )
            })
          ) : (
            <NoDataFound note="Tidak ada jadwal tersedia pada tanggal yang dipilih." />
          )}
        </div>
      </div>
      <div className="flex justify-end items-center gap-2">
        <ButtonPrimary onClick={() => handleConfirm()} className="rounded-sm" variant="contained" size="xs">
          Selanjutnya
        </ButtonPrimary>
      </div>
    </>
  )
}
