'use client'

import React from 'react'
import { useRouter } from 'next/navigation'
import useLocalStorage from '@/hooks/useLocalStorage'
import SidebarItem from './SidebarItem'
import { MenuItemProps } from '@/constans/SidebarMenu'
import useCalculateWidth from '@/hooks/useCalculateWidth'
import { Routes } from '@/constans/routes'
import SettingIcon from '@/assets/icons/sidebar/setting.svg'
import TokenService from '@/services/token.sevice'

type SidebarProps = {
  menuList: MenuItemProps[]
  sidebarOpen: boolean
  setSidebarOpen: (arg: boolean) => void
}

const Sidebar = ({ menuList, sidebarOpen, setSidebarOpen }: SidebarProps) => {
  const router = useRouter()
  const width = useCalculateWidth()
  const [pageName, setPageName] = useLocalStorage('selectedMenu', 'dashboard')

  const handleSignOut = () => {
    TokenService.removeLocalTokenAndRole()
    router.refresh()
  }

  return (
    <aside
      className={`fixed left-0 top-navbar w-sidebar h-[calc(100vh-76px)] py-6 bg-white border-r border-line-200 duration-300 ease-linear lg:translate-x-0 overflow-y-auto ${
        sidebarOpen ? 'translate-x-0' : '-translate-x-full'
      }`}
    >
      <nav className="flex flex-col flex-1 space-y-6">
        {menuList.map((menuItem, menuIndex) => {
          if (!menuItem.showInDesktop) return null
          return <SidebarItem key={menuIndex} item={menuItem} pageName={pageName} setPageName={setPageName} />
        })}
        {
          <>
            <div
              onClick={() => {
                setPageName('Pengaturan Akun')
                router.push(Routes.PsychologistSetting)
              }}
              className={`lg:hidden text-gray-200 group flex flex-row items-center py-1.5 px-6 text-sm focus:outline-nones transition-colors duration-200 rounded-lg`}
            >
              <div className="w-6">
                <SettingIcon className="w-6 group-hover:stroke-main-100" />
              </div>
              <span className="group-hover:text-main-100 group-hover:font-bold ml-2">
                {'Pengaturan Akun'}
              </span>
            </div>
            <div
              onClick={() => handleSignOut()}
              className={`lg:hidden text-gray-200 group flex flex-row items-center py-1.5 px-6 text-sm focus:outline-nones transition-colors duration-200 rounded-lg`}
            >
              <svg
                className="w-6"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M2 12L1.60957 11.6877L1.35969 12L1.60957 12.3123L2 12ZM11 12.5C11.2761 12.5 11.5 12.2761 11.5 12C11.5 11.7239 11.2761 11.5 11 11.5V12.5ZM5.60957 6.68765L1.60957 11.6877L2.39043 12.3123L6.39043 7.31235L5.60957 6.68765ZM1.60957 12.3123L5.60957 17.3123L6.39043 16.6877L2.39043 11.6877L1.60957 12.3123ZM2 12.5H11V11.5H2V12.5Z"
                  className="group-hover:fill-main-100 fill-gray-200"
                />
                <path
                  d="M10 8.13193V7.38851C10 5.77017 10 4.961 10.474 4.4015C10.9479 3.84201 11.7461 3.70899 13.3424 3.44293L15.0136 3.1644C18.2567 2.62388 19.8782 2.35363 20.9391 3.25232C22 4.15102 22 5.79493 22 9.08276V14.9172C22 18.2051 22 19.849 20.9391 20.7477C19.8782 21.6464 18.2567 21.3761 15.0136 20.8356L13.3424 20.5571C11.7461 20.291 10.9479 20.158 10.474 19.5985C10 19.039 10 18.2298 10 16.6115V16.066"
                  className="group-hover:stroke-main-100 stroke-gray-200"
                />
              </svg>
              <span className="group-hover:text-main-100 group-hover:font-bold ml-2">{'Keluar'}</span>
            </div>
          </>
        }
      </nav>
    </aside>
  )
}

export default Sidebar
