'use client'
import Title from '../Title'

import { useRouter, useSearchParams } from 'next/navigation'
import { Routes } from '@/constans/routes'
import { Separator } from '@/components/ui/separator'
import Link from 'next/link'
import EmailIlustration from '@/assets/ilustration/email.svg'
import CountdownTimer from '../SignIn/CountdownTimer'
import { useState } from 'react'
import { authService } from '@/services/auth.service'

export const ForgotPasswordConfirmation = () => {
  const router = useRouter()
  const searchParams = useSearchParams()
  const emailFromUrl = searchParams.get('email') ?? ''
  const [isAvailableResend, setIsAvailableResend] = useState<boolean>(false)
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [time, setTime] = useState<number>(1)

  async function handleResend() {
    setIsLoading(true)
    try {
      authService
        .forgotPassword(emailFromUrl)
        .then((res) => {
          setIsLoading(false)
          setIsAvailableResend(false)
          setTime(1)
        })
        .catch((err) => {
          setIsLoading(false)
          console.log('Error Submit')
        })
    } catch (error) {
      setIsLoading(false)
      console.log('Server Error')
    }
  }

  const handleOnExpiredTimer = () => {
    setIsAvailableResend(true)
    console.log('Expired Timer')
  }

  return (
    <div className="grid gap-y-4">
      <div className="relative max-h-[100px] max-w-[100px] mx-auto text-center">
        <EmailIlustration />
      </div>
      <Title
        center
        title="Email Reset Password Terkirim!"
        subTitle="Silakan untuk cek email Anda dan ikuti step dalam email tersebut."
      />
      <Separator />
      <div className="flex flex-col gap-y-[8px] text-center">
        <span className="text-body-sm font-medium text-gray-400">
          Email reset password telah terkirim ke:
        </span>
        <span className="text-subheading-md font-bold text-gray-400">{emailFromUrl}</span>
      </div>
      <span className="text-body-sm font-medium text-gray-400 text-center">
        Salah masukan email?{' '}
        <Link className="text-body-sm font-bold text-main-100" href={Routes.UserForgotPassword}>
          Ganti Email
        </Link>
      </span>
      <Separator />
      <span className="text-body-sm font-medium text-gray-400 text-center">
        Tidak dapat email reset?{' '}
        <span
          className={`text-body-sm font-bold ${isAvailableResend ? 'text-[#039EE9] cursor-pointer' : 'text-gray-100 cursor-not-allowed'}`}
          onClick={() => isAvailableResend && !isLoading && handleResend()}
        >
          Kirim Ulang
        </span>{' '}
        {!isAvailableResend ? (
          <span className="`text-body-sm font-bold text-gray-300">
            {'('}
            <CountdownTimer
              onComplete={() => handleOnExpiredTimer()}
              className={`text-body-sm font-bold text-[#039EE9]`}
              time={time}
            />
            {')'}
          </span>
        ) : null}
      </span>
    </div>
  )
}

export default ForgotPasswordConfirmation
