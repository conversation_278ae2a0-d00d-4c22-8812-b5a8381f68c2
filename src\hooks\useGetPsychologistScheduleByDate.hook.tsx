import { counsellingService } from '@/services/counselling.service'
import { useQuery } from '@tanstack/react-query'

export const useGetPsychologistScheduleByDate = (psychologistId: string, date: string, duration?: number) => {
  return useQuery({
    queryKey: ['PsychologistSchedule', { psychologistId, date, duration }],
    queryFn: () =>
      counsellingService
        .getPsychologistScheduleBydate(psychologistId, date)
        .then((response) => {
          if (duration) {
            const timebyDuration = response?.find((val: any) => val?.durationInMinute === duration)
            return timebyDuration
          }
          return response
        })
        .catch((error) => {
          return null
        }),
  })
}
