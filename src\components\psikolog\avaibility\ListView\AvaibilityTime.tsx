import { useState } from 'react'
import { SVGIcons, IIcons } from '@/components/_common/icon'
import { AppSelect } from '@/components/_common/Select/AppSelect'
import { useDispatch } from '@/store'
import { AvaibilityItemProps, AvaibilityWithActionProps } from '../useAvaibilityList.hook'
import { getEndTime, getStartTime } from '../../utils/getAvaibilityTime'

export type PayloadUpdateTimeProps = {
  day: string
  value: string
  targetKey: string
  targetId: number
}

type AvaibilityTimeProps = {
  onSetTime?: (val: PayloadUpdateTimeProps) => void
  onRemoveTime?: (val: AvaibilityItemProps) => void
  onSetStartTime?: (val: AvaibilityItemProps) => void
  onSetEndTime?: (val: AvaibilityItemProps) => void
}

const TIME_OPTIONS = [
  { id: 1, label: '09:00', value: '09:00' },
  { id: 2, label: '10:00', value: '10:00' },
  { id: 3, label: '11:00', value: '11:00' },
  { id: 4, label: '12:00', value: '12:00' },
  { id: 5, label: '13:00', value: '13:00' },
  { id: 6, label: '14:00', value: '14:00' },
  { id: 7, label: '15:00', value: '15:00' },
  { id: 8, label: '16:00', value: '16:00' },
  { id: 9, label: '17:00', value: '17:00' },
  { id: 10, label: '18:00', value: '18:00' },
  { id: 10, label: '19:00', value: '19:00' },
  { id: 11, label: '20:00', value: '20:00' },
  { id: 12, label: '21:00', value: '21:00' },
]
const startTimeList = [...TIME_OPTIONS].slice(0, TIME_OPTIONS.length - 1)
const endTimeList = [...TIME_OPTIONS].slice(1, TIME_OPTIONS.length)

const AvaibilityTime = ({
  day,
  isActive,
  timeList = [],
  onSetTime,
  onSetStartTime,
  onSetEndTime,
  onRemoveTime,
}: AvaibilityWithActionProps & AvaibilityTimeProps) => {
  const dispatch = useDispatch()
  const [error, setError] = useState<{ [key: string]: string | React.ReactNode | null } | null>(null)

  const handleOnSetStartTime = (val: AvaibilityItemProps) => {
    if (onSetStartTime) {
      onSetStartTime(val)
    } else {
      console.log('handleOnSetStartTime on AvaibilityTime.tsx')
      // do something here
    }
  }
  const handleOnSetEndTime = (val: AvaibilityItemProps) => {
    if (onSetEndTime) {
      onSetEndTime(val)
    } else {
      console.log('handleOnSetEndTime on AvaibilityTime.tsx')
      // do something here
    }
  }

  const handleRemoveTime = (val: AvaibilityItemProps) => {
    try {
      if (onRemoveTime) {
        onRemoveTime(val)
      } else {
        console.log('handleRemoveTime on AvaibilityTime.tsx')
        // dispatch(setRemoveTime({ day: day, targetId: val }))
      }
    } catch (error) {}
  }

  return (
    <div className="flex flex-1 items-center">
      {isActive && timeList.length > 0 ? (
        <div className="grid gap-y-4 w-full md:w-auto">
          {timeList.map((time, id) => {
            return (
              <div key={time.id} className="grid items-center w-full">
                <div className="flex flex-row gap-2 items-center justify-start">
                  <AppSelect
                    value={time.startTime}
                    className="w-full md:w-[115px] h-[46px]"
                    wrapClass="flex items-center"
                    placeholder="Select time"
                    options={startTimeList}
                    onChange={(val) => {
                      const payload = {
                        ...time,
                        startTime: val,
                        endTime: getEndTime(val),
                      }
                      handleOnSetStartTime(payload)
                    }}
                  />
                  <SVGIcons name={IIcons.Dash} />
                  <AppSelect
                    value={time.endTime}
                    className="w-full md:w-[115px] h-[46px]"
                    wrapClass="flex items-center"
                    placeholder="Select time"
                    options={endTimeList}
                    onChange={(val) => {
                      const payload = {
                        ...time,
                        endTime: val,
                      }
                      handleOnSetEndTime(payload)
                    }}
                  />
                  <span className="cursor-pointer" onClick={() => handleRemoveTime(time)}>
                    <SVGIcons name={IIcons.Close} />
                  </span>
                </div>
                {error?.[time.id ? time.id : ''] ? (
                  <span className="text-caption-md text-danger-100 pt-1">
                    {error?.[time.id ? time.id : '']}
                  </span>
                ) : null}
              </div>
            )
          })}
        </div>
      ) : (
        <div className="text-body-lg text-gray-300 flex items-center h-[46px]">Tidak tersedia</div>
      )}
    </div>
  )
}

export default AvaibilityTime
