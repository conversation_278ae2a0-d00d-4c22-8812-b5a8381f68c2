import { IIcons, SVGIcons } from '@/components/_common/icon'

export default function CardHelp({
  header,
  subHeader,
  onClickItem,
}: {
  header: string
  subHeader: string
  onClickItem?: () => void
}) {
  return (
    <div
      onClick={() => onClickItem && onClickItem()}
      className="flex flex-col gap-1 py-4 border-b border-[#EBEBEB] cursor-pointer"
    >
      <div className="flex items-center justify-between">
        <span className="font-bold text-[14px] text-[#222222]">{header}</span>
        <SVGIcons name={IIcons.ArrowRight} />
      </div>
      <span className="text-[12px] text-[#535353]">{subHeader}</span>
    </div>
  )
}
