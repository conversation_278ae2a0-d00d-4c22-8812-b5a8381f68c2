'use client'
import AppInput from '@/components/_common/input/Input'
import { IIcons } from '@/components/_common/icon'
import { columns } from './columns'
import { DataGrid } from '@/components/datagrid/DataTable'
import { Card } from '@/components/_common/ui'
import { getDummy } from '@/utils/getDummyData'
import { HeaderContent } from '../HeaderContent'
import { useRouter } from 'next/navigation'
import { FilterHeader } from '../FilterHeader'
import { CLientStatus, CounsellingMethod } from '@/constans/StaticOptions'
import { useMemo } from 'react'
import { Routes } from '@/constans/routes'
import { ADMIN_CLIENT_API } from '@/constans/API_PATH'

const fetchData = async ({ pageIndex, pageSize }: { pageIndex: number; pageSize: number }) => {
  const limit = pageSize
  return new Promise((resolve, reject) => {
    const data = {
      data: getDummy(limit),
      page: pageIndex ? pageIndex + 1 : 1,
      limit: limit,
      totalPage: Math.ceil(500 / limit) ?? 1,
      total: 500,
    }
    resolve(data)
  })
}

export const ClientComponent = () => {
  const router = useRouter()
  const filterOptions = [
    { value: CLientStatus.ALL, label: 'Semua Status' },
    { value: CLientStatus.ACTIVE, label: 'Aktif' },
    { value: CLientStatus.BLOCKED, label: 'Terblokir' },
  ]

  const ContactClient = useMemo(
    () => ({
      name: 'contactClient',
      icon: null,
      label: 'Hubungi Klien',
      onClick: (item: any) => {
        console.log('Hubungi Klien = ', item)
      },
    }),
    []
  )
  const BlockClient = useMemo(
    () => ({
      name: 'blockClient',
      icon: null,
      label: 'Blokir',
      onClick: (item: any) => {
        console.log('Blokir = ', item)
      },
    }),
    []
  )

  const actionsMenu = (row: any) => {
    let actionsMenus: any[] = [ContactClient, BlockClient]
    return actionsMenus
  }

  return (
    <>
      <HeaderContent title="Klien" handleDatePicker={() => undefined} handleAdd={() => router.push('/admin/client/add')} />
      <Card className="border-0 p-0 xs:p-0 sm:border sm:p-6">
        <div className="flex flex-col">
          <div className="flex gap-x-5 mb-4">
            <FilterHeader
              firstOptions={filterOptions}
              onChangeSearch={(val) => console.log(val)}
              onChangeFilter={(val) => console.log(val)}
              labelFirstFilter="Status"
            />
          </div>
          <DataGrid
            actionMenuList={actionsMenu}
            columns={columns}
            fetchPath={ADMIN_CLIENT_API}
            // fetchData={fetchData}
            // handleViewItem={(item: any) => {
            //   const row = item.original
            //   router.push(`/admin/klien/${row.id}?name=${row.pengguna}&email=${row.email}`)
            // }}
            onClickRow={(row) => router.push(Routes.AdminClientDetail.replace('[id]', row.id))}
            handleEditItem={() => {}}
            handleRemoveItem={() => {}}
            deleteFunc={() => {}}
          />
        </div>
      </Card>
    </>
  )
}
