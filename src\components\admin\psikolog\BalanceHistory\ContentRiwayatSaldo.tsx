export default function ContentRiwayatSaldo({
  title,
  jadwal,
  jumlah,
  type,
}: {
  title: string
  jadwal: string
  jumlah: string
  type: string
}) {
  const jenis = () => {
    if (type == 'Pengeluaran') {
      return (
        <div className="flex flex-col gap-1 p-4">
          <span className="text-[#039EE9] text-[14px] font-bold">{jumlah}</span>
          <span className="text-[#737373] text-[12px] ml-2">{type}</span>
        </div>
      )
    } else {
      return (
        <div className="flex flex-col gap-1 p-4">
          <span className="text-[#222222] text-[14px] font-bold">{jumlah}</span>
          <span className="text-[#737373] text-[12px]">{type}</span>
        </div>
      )
    }
  }

  return (
    <div className="flex flex-col border-b border-[#EBEBEB]">
      {/* content */}
      <div className="flex justify-between">
        <div className="flex flex-col gap-1 py-4">
          <span className="text-[#222222] text-[14px]">{title}</span>
          <span className="text-[#737373] text-[12px]">{jadwal}</span>
        </div>
        {jenis()}
      </div>
    </div>
  )
}
