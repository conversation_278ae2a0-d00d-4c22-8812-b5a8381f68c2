import { useToast } from '@/components/ui/use-toast'
import { avaibilityService } from '@/services/avaibility.service'
import { SpecificDateProps } from '@/store/psikolog/avaibility.reducer'
import { useQuery } from '@tanstack/react-query'

type TimeListProps = {
  id: string
  startTime: string
  endTime: string
}

export type AvaibilityWeekProps = {
  id: number
  day: string
  isActive: boolean
  timeList: TimeListProps[]
  timezone: string
}

const initial: AvaibilityWeekProps[] = [
  {
    id: 1,
    day: 'Senin',
    isActive: false,
    timeList: [],
    timezone: '',
  },
  {
    id: 2,
    day: 'Selasa',
    isActive: false,
    timeList: [],
    timezone: '',
  },
  {
    id: 3,
    day: 'Rabu',
    isActive: false,
    timeList: [],
    timezone: '',
  },
  {
    id: 4,
    day: 'Kamis',
    isActive: false,
    timeList: [],
    timezone: '',
  },
  {
    id: 5,
    day: 'Jumat',
    isActive: false,
    timeList: [],
    timezone: '',
  },
  {
    id: 6,
    day: 'Sabtu',
    isActive: false,
    timeList: [],
    timezone: '',
  },
  {
    id: 0,
    day: 'Minggu',
    isActive: false,
    timeList: [],
    timezone: '',
  },
]

export type AvaibilityProps = {
  id: number
  day: string
  isActive: boolean
  timeList: AvaibilityItemProps[]
  timezone?: string
}

export type AvaibilityActionProps = {
  onCheckedChange?: (val: boolean) => void
  addItem?: (val: string) => void
  hideCopyButton?: boolean
  refetch?: (options?: any) => void
}

export type AvaibilityWithActionProps = AvaibilityProps & AvaibilityActionProps

export type AvaibilityItemProps = {
  id: string | null
  psychologistId: string
  day: number
  startTime: string
  endTime: string
  date: string | null
  timezone: string
  createdAt: string
  modifiedAt: string
}

export type WeeklyAvaibilityProps = {
  [key: string]: AvaibilityItemProps[]
}

const MapIndexToDay = ['Minggu', 'Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu']

const mappedAvaibility = (data: WeeklyAvaibilityProps) => {
  let avaibilityMapped = [...initial]
  const mappedAvaibilityResult = avaibilityMapped.map((item) => {
    return {
      ...item,
      isActive: data[item.id]?.length > 0 ? true : false,
      timeList: data[item.id] || [],
      timezone: data[item.id]?.[0]?.timezone || '',
    }
  })
  return mappedAvaibilityResult
}

export const useAvaibilityList = () => {
  const { toast } = useToast()
  // const { avaibility } = useSelector((state) => state.psikologAvaibility)

  return useQuery({
    queryKey: ['AvaibilityPsychologistList'],
    queryFn: () =>
      avaibilityService
        .getAvaibility()
        .then((response) => {
          const mappedWeeklyAvaibility = mappedAvaibility(response.weekly)
          return {
            weeklyAvaibility: mappedWeeklyAvaibility,
            specificAvaibility: response.specific as SpecificDateProps[],
          }
        })
        .catch((error) => {
          toast({
            title: 'Gagal',
            description: 'Terjadi masalah dengan server, Silahkan hubungi Admin',
            variant: 'danger',
          })
        }),
  })
}
