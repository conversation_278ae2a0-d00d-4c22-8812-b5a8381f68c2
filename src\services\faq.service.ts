import { config } from '@/constans/config'
import { httpRequest } from '@/utils/network'

export type CategoryProps = 'B2C' | 'MHBusiness' | 'MHEducation'
export class FaqService {
  async getFaqByCategory(category: CategoryProps) {
    return await httpRequest({
      method: 'get',
      url: `${config?.apiBaseUrl}api/faq/category/${category}`,
    })
  }
}

export const faqService = new FaqService()
