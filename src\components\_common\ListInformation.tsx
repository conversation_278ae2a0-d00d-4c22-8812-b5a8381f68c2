import { twMerge } from 'tailwind-merge'
import { IIcons, SVGIcons } from './icon'

type ListInformationType = {
  listItem: {
    label: string
    icon?: IIcons
  }[]
  className?: string
}
export const ListInformation = ({ listItem, className }: ListInformationType) => {
  return (
    <ul
      className={twMerge(
        `flex flex-wrap flex-col md:flex-row overflow-y-hidden overflow-x-auto border-b border-line-200 gap-x-4 py-4`,
        className
      )}
    >
      {listItem.map((item, index) => {
        return (
          <li className="flex items-center text-gray-200" key={index}>
            {item.icon && <SVGIcons className="mr-2" name={item.icon} />}{' '}
            <span className="text-gray-400">{item.label}</span>
          </li>
        )
      })}
    </ul>
  )
}
