'use client'
import { useRouter } from 'next/navigation'
import { FeatureCard } from './FeatureCard'
import { MHFeatureMobile } from './MHFeatureMobile'
import { STATIC_DATA } from '@/constans/STATIC_DATA'

export const MentalHealingFeature = () => {
  const router = useRouter()

  const FeatureList = [
    {
      id: 1,
      image: '/ilustration/home/<USER>',
      service: STATIC_DATA.Home.section1.content[0].services,
      title: STATIC_DATA.Home.section1.content[0].title,
      subTitle: STATIC_DATA.Home.section1.content[0].content,
      label: STATIC_DATA.Home.section1.content[0].label,
      handleClick: () => router.push('/search-psikolog'),
    },
    {
      id: 2,
      image: '/ilustration/home/<USER>',
      service: STATIC_DATA.Home.section1.content[1].services,
      title: STATIC_DATA.Home.section1.content[1].title,
      subTitle: STATIC_DATA.Home.section1.content[1].content,
      label: STATIC_DATA.Home.section1.content[1].label,
      handleClick: () => router.push('/mh-business'),
    },
    {
      id: 3,
      image: '/ilustration/home/<USER>',
      service: STATIC_DATA.Home.section1.content[2].services,
      title: STATIC_DATA.Home.section1.content[2].title,
      subTitle: STATIC_DATA.Home.section1.content[2].content,
      label: STATIC_DATA.Home.section1.content[2].label,
      handleClick: () => router.push('/mh-education'),
    },
  ]

  return (
    <>
      <div className="hidden md:grid grid-cols-3 gap-x-6 max-w-screen xl:max-w-[1120px] w-full -mt-[200px] z-1 md:px-4">
        {FeatureList.map((item) => (
          <FeatureCard key={item.id} {...item} />
        ))}
      </div>
      <MHFeatureMobile content={FeatureList} />
    </>
  )
}
