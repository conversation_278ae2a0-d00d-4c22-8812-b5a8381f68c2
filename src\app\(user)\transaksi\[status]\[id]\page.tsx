'use client'
import { Header<PERSON>ontent } from '@/components/admin/HeaderContent'
import Breadcrumb from '@/components/breadcrumbs/Breadcrumbs'
import BuktiTransaksi from '@/components/userTransaksi/buktiTransaksi'
import ButtonDetailTransaksi from '@/components/userTransaksi/buttonDetailTransaksi'
import CallUs from '@/components/userTransaksi/callUs'
import CardDetailTransaksi from '@/components/userTransaksi/cardDetailTransaksi'
import CardStatusBar, { Payment } from '@/components/userTransaksi/cardStatusBar'
import CatatanPsikolog from '@/components/userTransaksi/catatanPsikolog'
import PengalamanKonseling from '@/components/userTransaksi/pengalamanKonseling'
import ThankCard from '@/components/userTransaksi/thankCard'
import { useGetDetailTransaction } from '@/hooks/useGetDetailTransaction'

export default function DetailTransaksi({ params }: { params: { status: string; id: string } }) {
  const transactionId = params.id
  const transactionStatus = params.status

  const { data, isLoading, error } = useGetDetailTransaction(transactionId)
  console.log('Payment:', data?.payment)
  console.log('Transaction Status:', transactionStatus)
  console.log('Transaction Data:', data)

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div>Loading...</div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div>Error: {error}</div>
      </div>
    )
  }

  if (!data) {
    return null
  }

  const titleRename = () => {
    switch (transactionStatus) {
      case 'Konseling-dengan-psikolog':
        return 'Konseling dengan Psikolog'
      case 'Menunggu-konfirmasi-psikolog':
        return 'Menunggu Konfirmasi Psikolog'
      case 'Menunggu-konfirmasi-kamu':
        return 'Menunggu Konfirmasi Kamu'
      case 'Menunggu-pembayaran-konseling':
        return 'Menunggu Pembayaran Konseling'
      case 'Konseling-selesai':
        return 'Konseling Selesai'
      case 'Konseling-dibatalkan':
        return 'Konseling Dibatalkan'
      case 'Pembayaran-konseling-gagal':
        return 'Pembayaran Konseling Gagal'
      default:
        return ''
    }
  }

  const buttonDetail = () => {
    switch (transactionStatus) {
      case 'Konseling-dengan-psikolog':
        return (
          <ButtonDetailTransaksi
            title="Mulai Konseling"
            title2="Ubah Jadwal"
            showJadwal={false}
            showSecondButton={true}
            buttonVariant=""
            color=""
          />
        )
      case 'Menunggu-pembayaran-konseling':
      case 'Menunggu-konfirmasi-kamu':
        return (
          <ButtonDetailTransaksi
            title=""
            title2=""
            showJadwal={false}
            showSecondButton={null}
            buttonVariant=""
            color=""
          />
        )
      case 'Menunggu-konfirmasi-psikolog':
        return (
          <ButtonDetailTransaksi
            title=""
            title2="Ubah Jadwal"
            showJadwal={false}
            showSecondButton={false}
            buttonVariant="outlined"
            color="gray"
          />
        )
      case 'Konseling-dibatalkan':
      case 'Pembayaran-konseling-gagal':
        return (
          <ButtonDetailTransaksi
            title=""
            title2="Jadwalkan Lagi"
            showJadwal={false}
            showSecondButton={false}
            buttonVariant="contained"
            color="primary"
          />
        )
      case 'Konseling-selesai':
        return (
          <ButtonDetailTransaksi
            title=""
            title2="Jadwalkan Lagi"
            showJadwal={true}
            showSecondButton={false}
            buttonVariant="contained"
            color="primary"
          />
        )
      default:
        return null
    }
  }

  const showReceipt = () => {
    if (
      transactionStatus === 'Menunggu-pembayaran-konseling' ||
      transactionStatus === 'Konseling-dibatalkan' ||
      transactionStatus === 'Pembayaran-konseling-gagal'
    ) {
      return null
    } else {
      return <BuktiTransaksi />
    }
  }

  const notePsikolog = () => {
    if (transactionStatus === 'Konseling-selesai') {
      // return <CatatanPsikolog anyNotes={true} />
      return null
    } else {
      return null
    }
  }

  const conselingExperience = () => {
    if (transactionStatus === 'Konseling-selesai') {
      return <PengalamanKonseling />
    } else {
      return null
    }
  }

  const thankCard = () => {
    if (transactionStatus === 'Konseling-selesai') {
      return <ThankCard />
    } else {
      return null
    }
  }

  return (
    <>
      <div className="lg:mt-[64px] w-auto md:w-[738px] max-w-[1120px] flex items-center justify-center mb-20 lg:mb-72 px-4 md:px-0">
        <div className="flex flex-col gap-6 w-full">
          <Breadcrumb containerClasses="hidden md:flex" pageName={'Detail Konseling'} />
          <HeaderContent className="mb-0 block" title={'Konseling'} />
          <CardStatusBar title={titleRename()} payment={data?.payment as Payment} />
          <CardDetailTransaksi data={data} />
          <CatatanPsikolog
            // notes={data.clientReport?.notes}
            // tasks={data.clientReport?.tasks}
            // nextSchedule={data.clientReport?.nextSchedule}
            isEditable={titleRename() === 'Konseling Selesai'}
            onUpdateClick={() => {
              // Handle update click
              console.log('Update notes clicked')
            }}
          />
          {notePsikolog()}
          {showReceipt()}
          {conselingExperience()}
          {thankCard()}
          {buttonDetail()}
          <CallUs />
        </div>
      </div>
    </>
  )
}
