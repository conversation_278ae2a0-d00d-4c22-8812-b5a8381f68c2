import { useState } from 'react'
import PinPasswordButton from './pinPasswordButton'

export default function PinPassword() {
  const [pin, setPin] = useState<(number | null)[]>([null, null, null, null])

  const handleClick = (val: number | string) => {
    setPin((prevPin) => {
      // Jika tombol backspace ditekan
      if (val === 'backspace') {
        const lastFilledIndex = prevPin.findLastIndex((num) => num !== null)
        if (lastFilledIndex !== -1) {
          const newPin = [...prevPin]
          newPin[lastFilledIndex] = null
          return newPin
        }
      }
      // Jika tombol angka ditekan
      else if (typeof val === 'number') {
        const nextEmptyIndex = prevPin.indexOf(null)
        if (nextEmptyIndex !== -1) {
          const newPin = [...prevPin]
          newPin[nextEmptyIndex] = val
          return newPin
        }
      }
      return prevPin
    })
  }

  return (
    <div className="flex bg-white gap-4 w-[260px] xs:w-[300px] md:w-[400px]">
      <div className="flex flex-col items-center justify-center gap-8">
        <h4 className="text-center">Masukkan PIN untuk masuk ke akunmu terdiri dari 4 angka</h4>
        {/* fill pin */}
        <div className="flex items-center gap-1">
          <span
            className={`${pin[0] !== null ? 'bg-[#222222]' : 'bg-[#ebebeb]'} w-3 h-3 rounded-full`}
          ></span>
          <span
            className={`${pin[1] !== null ? 'bg-[#222222]' : 'bg-[#ebebeb]'} w-3 h-3 rounded-full`}
          ></span>
          <span
            className={`${pin[2] !== null ? 'bg-[#222222]' : 'bg-[#ebebeb]'} w-3 h-3 rounded-full`}
          ></span>
          <span
            className={`${pin[3] !== null ? 'bg-[#222222]' : 'bg-[#ebebeb]'} w-3 h-3 rounded-full`}
          ></span>
        </div>
        <div className="flex flex-wrap items-center justify-center gap-4 px-8">
          <PinPasswordButton onHandleChange={() => handleClick(1)} number="1" backSpace={false} />
          <PinPasswordButton onHandleChange={() => handleClick(2)} number="2" backSpace={false} />
          <PinPasswordButton onHandleChange={() => handleClick(3)} number="3" backSpace={false} />
          <PinPasswordButton onHandleChange={() => handleClick(4)} number="4" backSpace={false} />
          <PinPasswordButton onHandleChange={() => handleClick(5)} number="5" backSpace={false} />
          <PinPasswordButton onHandleChange={() => handleClick(6)} number="6" backSpace={false} />
          <PinPasswordButton onHandleChange={() => handleClick(7)} number="7" backSpace={false} />
          <PinPasswordButton onHandleChange={() => handleClick(8)} number="8" backSpace={false} />
          <PinPasswordButton onHandleChange={() => handleClick(9)} number="9" backSpace={false} />
          <div className="flex items-end gap-4 pr-[6px] w-full justify-end">
            <PinPasswordButton onHandleChange={() => handleClick(0)} number="0" backSpace={false} />
            <PinPasswordButton
              onHandleChange={() => handleClick('backspace')}
              number="backspace"
              backSpace={true}
            />
          </div>
        </div>
      </div>
    </div>
  )
}
