import Breadcrumb from '@/components/breadcrumbs/Breadcrumbs'
import { Container } from '@/components/_common/ui'
import { DetailCounselling } from '@/components/admin/counseling/DetailCounselling'
import { Suspense } from 'react'

const DetailCounsellingPage = async ({ params }: { params: Promise<{ id: string }> }) => {
  const idCounseling = (await params).id
  return (
    <Suspense>
      <Breadcrumb containerClasses="pb-2" pageName="Detail Konseling" />
      <Container>
        <DetailCounselling idCounseling={idCounseling} />
      </Container>
    </Suspense>
  )
}

export default DetailCounsellingPage
