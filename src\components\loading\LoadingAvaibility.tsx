export default function LoadingAvaibility() {
  return (
    <>
      <div className="flex flex-col gap-6">
        {/* header */}
        <div className="animate-pulse h-5 bg-slate-200 rounded w-1/2"></div>
        {/* card */}
        <div className="md:flex items-center gap-6 py-4 border-b border-[#EBEBEB]">
          {/* mobile */}
          <div className="flex flex-col gap-4 md:hidden">
            {/* togle mobile */}
            <div className="md:hidden flex items-center justify-between">
              {/* togle */}
              <div className="flex items-center gap-2">
                <div className="animate-pulse h-5 bg-slate-200 rounded w-[50px]"></div>
                <div className="animate-pulse h-5 bg-slate-200 rounded w-[40px]"></div>
              </div>
              {/* action button */}
              <div className="flex items-center gap-2">
                <div className="animate-pulse h-1 bg-slate-200 rounded w-4"></div>
                <div className="animate-pulse h-1 bg-slate-200 rounded w-4"></div>
              </div>
            </div>
            {/* input time  mobile*/}
            <div className="md:hidden flex items-center gap-3">
              <div className="animate-pulse h-5 bg-slate-200 rounded w-full"></div>
              <div className="animate-pulse h-1 bg-slate-200 rounded w-4"></div>
              <div className="animate-pulse h-5 bg-slate-200 rounded w-full"></div>
              <div className="animate-pulse h-1 bg-slate-200 rounded w-4"></div>
            </div>
          </div>
          {/* togle */}
          <div className="hidden md:flex items-center gap-2">
            <div className="animate-pulse h-5 bg-slate-200 rounded w-[50px]"></div>
            <div className="animate-pulse h-5 bg-slate-200 rounded w-[40px]"></div>
          </div>
          {/* input time */}
          <div className="hidden md:flex items-center gap-3 w-full">
            <div className="animate-pulse h-5 bg-slate-200 rounded w-full"></div>
            <div className="animate-pulse h-1 bg-slate-200 rounded w-4"></div>
            <div className="animate-pulse h-5 bg-slate-200 rounded w-full"></div>
          </div>
          {/* action button */}
          <div className="hidden md:flex items-center gap-1">
            <div className="animate-pulse h-1 bg-slate-200 rounded w-4"></div>
            <div className="animate-pulse h-1 bg-slate-200 rounded w-4"></div>
            <div className="animate-pulse h-1 bg-slate-200 rounded w-4"></div>
          </div>
        </div>
        {/* card */}
        <div className="md:flex gap-6 py-4">
          {/* mobile */}
          <div className="flex flex-col gap-4 md:hidden">
            {/* togle mobile */}
            <div className="md:hidden flex items-center justify-between">
              {/* togle */}
              <div className="flex items-center gap-2">
                <div className="animate-pulse h-5 bg-slate-200 rounded w-[50px]"></div>
                <div className="animate-pulse h-5 bg-slate-200 rounded w-[40px]"></div>
              </div>
              {/* action button */}
              <div className="flex items-center gap-2">
                <div className="animate-pulse h-1 bg-slate-200 rounded w-4"></div>
                <div className="animate-pulse h-1 bg-slate-200 rounded w-4"></div>
              </div>
            </div>
            {/* input time  mobile*/}
            <div className="flex flex-col gap-2">
              <div className="md:hidden flex items-center gap-3">
                <div className="animate-pulse h-5 bg-slate-200 rounded w-full"></div>
                <div className="animate-pulse h-1 bg-slate-200 rounded w-4"></div>
                <div className="animate-pulse h-5 bg-slate-200 rounded w-full"></div>
                <div className="animate-pulse h-1 bg-slate-200 rounded w-4"></div>
              </div>
              <div className="md:hidden flex items-center gap-3">
                <div className="animate-pulse h-5 bg-slate-200 rounded w-full"></div>
                <div className="animate-pulse h-1 bg-slate-200 rounded w-4"></div>
                <div className="animate-pulse h-5 bg-slate-200 rounded w-full"></div>
                <div className="animate-pulse h-1 bg-slate-200 rounded w-4"></div>
              </div>
            </div>
          </div>
          {/* togle */}
          <div className="hidden md:flex gap-2">
            <div className="animate-pulse h-5 bg-slate-200 rounded w-[50px]"></div>
            <div className="animate-pulse h-5 bg-slate-200 rounded w-[40px]"></div>
          </div>
          {/* input time */}
          <div className="flex flex-col gap-4 w-full">
            <div className="hidden md:flex items-center gap-3 w-full">
              <div className="animate-pulse h-5 bg-slate-200 rounded w-full"></div>
              <div className="animate-pulse h-1 bg-slate-200 rounded w-4"></div>
              <div className="animate-pulse h-5 bg-slate-200 rounded w-full"></div>
            </div>
            <div className="hidden md:flex items-center gap-3 w-full">
              <div className="animate-pulse h-5 bg-slate-200 rounded w-full"></div>
              <div className="animate-pulse h-1 bg-slate-200 rounded w-4"></div>
              <div className="animate-pulse h-5 bg-slate-200 rounded w-full"></div>
            </div>
          </div>
          {/* action button */}
          <div className="hidden md:flex gap-1">
            <div className="animate-pulse h-1 bg-slate-200 rounded w-4"></div>
            <div className="animate-pulse h-1 bg-slate-200 rounded w-4"></div>
            <div className="animate-pulse h-1 bg-slate-200 rounded w-4"></div>
          </div>
        </div>
      </div>
    </>
  )
}
