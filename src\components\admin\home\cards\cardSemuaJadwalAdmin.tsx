import { IIcons, SVGIcons } from '@/components/_common/icon'
import React, { useEffect, useState } from 'react'
import JadwalConselingPsikolog from '../jadwalConselingAdmin'
import JadwalConselingPsikologExistingUser from '../jadwalConselingExisting'
import BtnLeftNavigationMobile from '../../../../../public/images/jadwalNavLeftMobile.svg'
import BtnRightNavigationMobile from '../../../../../public/images/jadwalNavRightMobile.svg'
import BlueCalender from '../../../../../public/images/blueCalender.svg'
import { formatDate } from 'date-fns'
import { INPUT_DATE_FORMAT } from '@/constans/date'
import { useGetDashboardSchedule } from '@/components/psikolog/home/<USER>/useGetDashboardSchedule.hook'
import ListingCounselingSchedule from '@/components/psikolog/home/<USER>'
import Link from 'next/link'
import { id } from 'date-fns/locale'
import { Routes } from '@/constans/routes'
import moment from 'moment'

// Fungsi untuk mendapatkan nama bulan
function getMonthName(monthIndex: number): string {
  const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'Mei', 'Jun', 'Jul', 'Aug', 'Sep', 'Okt', 'Nov', 'Des']
  return monthNames[monthIndex]
}

export default function CardSemuaJadwal() {
  const [currentDate, setCurrentDate] = useState(new Date())

  useEffect(() => {
    const intervalId = setInterval(() => {
      setCurrentDate(new Date())
    }, 60000) // Update setiap 60 detik

    return () => clearInterval(intervalId)
  }, [])

  // List hari
  const days = ['Min', 'Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab']

  const handleNavigationClick = (direction: 'left' | 'right') => {
    const newDate = new Date(currentDate)
    newDate.setDate(currentDate.getDate() + (direction === 'left' ? -7 : 7))
    setCurrentDate(newDate)
  }

  // hitung tanggal awal dan akhir coy
  const startDate = new Date(currentDate)
  const endDate = new Date(currentDate)
  endDate.setDate(startDate.getDate() + 6)

  // Mendapatkan format tanggal untuk ditampilkan di id="dateIndicator" brok
  const dateIndicatorText = `${startDate.getDate()} ${getMonthName(startDate.getMonth())} - ${endDate.getDate()} ${getMonthName(endDate.getMonth())}`
  const yearIndicatorText = `${startDate.getFullYear()}`

  const { data, refetch, isLoading, isPending } = useGetDashboardSchedule(
    moment(startDate).toISOString(),
    moment(endDate).toISOString()
  )

  return (
    <>
      <div className="flex flex-col gap-[16px] bg-white rounded-[15px] border border-[#EBEBEB] px-6 pb-6 pt-0 max-h-[500px] overflow-y-scroll scrollbar-hide">
        <div className="flex flex-col gap-[16px]">
          <div className="grid sticky top-0 z-10 w-full gap-[16px] bg-white pt-6">
            <div className="flex items-center justify-between">
              <BtnLeftNavigationMobile
                className="inline-block md:hidden"
                onClick={() => handleNavigationClick('left')}
              />
              <div className="flex items-center gap-4 md:gap-[24px]">
                <p id="dateIndicator" className="font-bold text-[16px] text-[#222222]">
                  {dateIndicatorText} {yearIndicatorText}{' '}
                  {/* untuk Menampilkan tanggal dinamis yang sesuai dengan id="listJadwal" */}
                </p>
                {/* btn navigation */}
                <div className="flex items-center gap-4 md:gap-[24px]">
                  {/* dekstop */}
                  <div
                    id="navigationleft"
                    className="hidden w-[34px] h-[34px] md:flex items-center justify-center border border-[#EBEBEB] rounded-full cursor-pointer"
                    onClick={() => handleNavigationClick('left')}
                  >
                    <SVGIcons name={IIcons.ArrorLeft} />
                  </div>
                  <div
                    id="navigationright"
                    className="hidden w-[34px] h-[34px] md:flex items-center justify-center border border-[#EBEBEB] rounded-full cursor-pointer"
                    onClick={() => handleNavigationClick('right')}
                  >
                    <SVGIcons name={IIcons.ArrowRight} />
                  </div>
                </div>
              </div>
              <BtnRightNavigationMobile
                className="inline-block md:hidden"
                onClick={() => handleNavigationClick('right')}
              />
              <Link
                href={Routes.AdminCounselling}
                className="text-[#039EE9] text-[12px] md:text-[14px] font-bold text-end hidden md:inline-block"
              >
                Lihat Semua Jadwal
              </Link>
            </div>
            <div
              id="listJadwal"
              className="pb-[12px] border-b border-[#EBEBEB] flex items-center justify-between bg-white"
            >
              {data?.scheduleStatus?.length
                ? data?.scheduleStatus.map((item, index) => {
                    return (
                      <div className="flex flex-col items-center gap-[12px]" key={index}>
                        <p className="text-[#535353] text-[14px]">
                          {formatDate(new Date(item.date), 'EEEEEE', {
                            locale: id,
                          })}
                        </p>
                        <div className="flex flex-col items-center justify-center relative">
                          {item.hasSchedule ? (
                            <div
                              id="dotJadwal"
                              className="w-[6px] h-[6px] bg-[#039EE9] rounded-full absolute -top-1"
                            ></div>
                          ) : null}
                          <p className="text-[#535353] text-[14px]">
                            {formatDate(new Date(item.date), 'd', {
                              locale: id,
                            })}
                          </p>
                        </div>
                      </div>
                    )
                  })
                : Array.from({ length: 7 }).map((_, index) => {
                    const date = new Date(currentDate)
                    date.setDate(currentDate.getDate() + index)
                    return (
                      <div className="flex flex-col items-center gap-[12px]" key={index}>
                        <p className="text-[#535353] text-[14px]">{days[date.getDay()]}</p>
                        <div className="flex flex-col items-center justify-center relative">
                          <p className="text-[#535353] text-[14px]">{date.getDate()}</p>
                        </div>
                      </div>
                    )
                  })}
            </div>
          </div>
          {data?.isNoCounselingSchedule ? (
            <JadwalConselingPsikolog />
          ) : (
            <ListingCounselingSchedule scheduleList={data?.scheduleList || []} />
          )}
          <Link
            href={Routes.AdminCounselling}
            className="text-[#039EE9] text-[12px] md:text-[14px] font-bold text-center inline-block md:hidden"
          >
            Lihat Semua Jadwal
          </Link>
        </div>
      </div>
    </>
  )
}
