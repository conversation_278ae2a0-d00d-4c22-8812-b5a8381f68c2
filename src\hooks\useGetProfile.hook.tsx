import { useToast } from '@/components/ui/use-toast'
import { profileService } from '@/services/profile.service'
import { useDispatch, useSelector } from '@/store'
import { AuthRole } from '@/store/auth/auth.action'
import { setProfilePsychologist } from '@/store/psikolog/profile.reducer'
import { useQuery } from '@tanstack/react-query'

export const useGetProfile = () => {
  const dispatch = useDispatch()
  const { user } = useSelector((state) => state.Authentication)
  const { toast } = useToast()
  return useQuery({
    queryKey: ['Profile'],
    queryFn: () => {
      if (user?.role === AuthRole.PSIKOLOG) {
        return profileService
          .getPsychologistProfile()
          .then((response) => {
            dispatch(setProfilePsychologist(response))
            return response
          })
          .catch((error) => {
            toast({
              title: 'Gagal',
              description: '<PERSON>rjadi masalah dengan server, Silahkan hubungi Admin',
              variant: 'danger',
            })
          })
      } else if (user?.role === AuthRole.ADMIN || user?.role === AuthRole.SUPERADMIN) {
        return profileService
          .getAdminProfile()
          .then((response) => {
            return response
          })
          .catch((error) => {
            toast({
              title: 'Gagal',
              description: 'Terjadi masalah dengan server, Silahkan hubungi Admin',
              variant: 'danger',
            })
          })
      }
    },
  })
}
