'use client'

import { ColumnDef } from '@tanstack/react-table'
import { UserPhoto } from '../../UserPhoto/UserPhoto'
import { AppMediumText } from '@/components/_common/ui'
import { InfoDataDisplay } from '@/components/datagrid/InfoDataDisplay'
import { Status } from '@/components/_common/Status/Status'
import moment from 'moment-timezone'
import 'moment/locale/id'
import { ScheduleListProps } from '@/store/psikolog/schedule.reducer'
import { formatStringToStartEndTimeOutput } from '@/utils/displayDate'
import { CounsellingMethod, defaultTimezone } from '@/constans/StaticOptions'
import Link from 'next/link'
import { Routes } from '@/constans/routes'

export type CounsellingType = ScheduleListProps

export const columns: ColumnDef<any>[] = [
  {
    accessorKey: 'id',
    header: 'Konseling ID',
  },
  {
    accessorKey: 'startTime',
    header: 'Jadwal',
    cell: ({ cell, row }) => {
      const date = moment(cell.row.original?.startTime).locale('id').format('dddd, D MMM YYYY')
      return (
        <InfoDataDisplay
          title={date}
          subTitle={
            cell.row.original?.startTime
              ? formatStringToStartEndTimeOutput({
                  date: cell.row.original?.startTime,
                  duration: cell.row.original?.duration,
                  timezone: defaultTimezone,
                  timeLabel: 'WIB',
                  isUTC: true,
                })
              : ''
          }
        />
      )
    },
  },
  {
    accessorKey: 'psychologist',
    header: 'Psikolog',
    cell: ({ cell, row }) => {
      return (
        <Link
          onClick={(e) => e.stopPropagation()}
          href={Routes.AdminPsychologistDetail.replace('[id]', cell.row.original?.psychologist?.id)}
          className="font-bold hover:text-main-100"
          passHref
        >
          <UserPhoto
            photo={cell.row.original['psychologist']?.['profilePhoto']}
            title={cell.row.original['psychologist']?.['fullName']}
            subTitle={
              cell.row.original['psychologist']?.field?.length
                ? cell.row.original['psychologist']?.field?.map((val: any) => val.name).join(', ')
                : 'Tidak Tersedia'
            }
          />
        </Link>
      )
    },
  },
  {
    accessorKey: 'client',
    header: 'Klien',
    cell: ({ cell, row }) => {
      return (
        <div className="font-bold hover:text-main-100">
          <UserPhoto
            photo={cell.row.original['client']?.['profilePhoto']}
            title={cell.row.original['client']?.['fullName']}
          />
        </div>
      )
    },
  },
  {
    accessorKey: 'method',
    header: 'Metode',
    cell: ({ cell, row }) => {
      return (
        <AppMediumText>
          {cell.row.original['method'] === CounsellingMethod.CALL ? 'Call' : 'Video Call'}
        </AppMediumText>
      )
    },
  },
  {
    accessorKey: 'payment',
    header: 'Biaya',
    cell: ({ cell, row }) => {
      return (
        <AppMediumText>
          {Intl.NumberFormat('id-ID', {
            style: 'currency',
            currency: 'IDR',
            maximumFractionDigits: 0,
            minimumFractionDigits: 0,
          }).format(Number(cell.row.original?.payment?.amount ?? 0))}
        </AppMediumText>
      )
    },
  },
]

const translateStatus = (status: string) => {
  switch (status) {
    case 'EXPIRED_PAYMENT':
      return 'Pembayaran kadaluarsa'
    case 'RESCHEDULE_BY_CLIENT':
      return 'Klien mengubah jadwal'
    case 'RESCHEDULE_BY_PSYCHOLOGIST':
      return 'Psikolog mengubah jabwal'
    case 'COMPLETED':
      return 'Selesai'
    case 'APPROVED':
      return 'Telah dikonfirmasi'
    case 'CANCELLED_ADMIN':
      return 'Dibatalkan admin'
    case 'CANCELLED_PSYCHOLOGIST':
      return 'Dibatalkan psikolog'
    case 'REJECTED_BY_PSYCHOLOGIST':
      return 'Ditolak Psikolog'
    default:
      return status
  }
}
export const columnsStatus: ColumnDef<any>[] = [
  {
    accessorKey: 'id',
    header: 'Konseling ID',
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ cell, row }) => {
      return <Status variant="primary" label={translateStatus(cell.row.original?.status)} />
    },
  },
  {
    accessorKey: 'jadwal',
    header: 'Jadwal',
    cell: ({ cell, row }) => {
      const date = moment(cell.row.original?.startTime).locale('id').format('dddd, D MMM YYYY')
      return (
        <InfoDataDisplay
          title={date}
          subTitle={
            cell.row.original?.startTime
              ? formatStringToStartEndTimeOutput({
                  date: cell.row.original?.startTime,
                  duration: cell.row.original?.duration,
                  timezone: defaultTimezone,
                  isUTC: true,
                })
              : ''
          }
        />
      )
    },
  },
  {
    accessorKey: 'psikolog',
    header: 'Psikolog',
    cell: ({ cell, row }) => {
      return (
        <Link
          onClick={(e) => e.stopPropagation()}
          href={Routes.AdminPsychologistDetail.replace('[id]', cell.row.original?.psychologist?.id)}
          className="font-bold hover:text-main-100"
          passHref
        >
          <UserPhoto
            photo={cell.row.original['psychologist']?.['profilePhoto']}
            title={cell.row.original['psychologist']?.['fullName']}
            subTitle={
              cell.row.original['problemCategory']?.length
                ? cell.row.original['problemCategory']?.map((val: any) => val.problemCategory).join(', ')
                : 'Tidak Tersedia'
            }
          />
        </Link>
      )
    },
  },
  {
    accessorKey: 'client',
    header: 'Klien',
    cell: ({ cell, row }) => {
      return (
        <div className="font-bold hover:text-main-100">
          <UserPhoto
            photo={cell.row.original['client']?.['profilePhoto']}
            title={cell.row.original['client']?.['fullName']}
          />
        </div>
      )
    },
  },
  {
    accessorKey: 'metode',
    header: 'Metode',
    cell: ({ cell, row }) => {
      return <AppMediumText>{cell.row.original['method']}</AppMediumText>
    },
  },
  {
    accessorKey: 'amount',
    header: 'Biaya',
    cell: ({ cell, row }) => {
      return (
        <AppMediumText>
          {Intl.NumberFormat('id-ID', {
            style: 'currency',
            currency: 'IDR',
            maximumFractionDigits: 0,
            minimumFractionDigits: 0,
          }).format(Number(cell.row.original?.payment?.amount ?? 0))}
        </AppMediumText>
      )
    },
  },
]
