import ButtonPrimary from '../ButtonPrimary'
import { AppModal } from '../Modal/AppModal'

type ConfirmModalProps = {
  title: string
  content: string
  isOpenModal: boolean
  onYes: () => void
  onCancel: () => void
}

export const ConfirmModal = ({ title, content, isOpenModal, onYes, onCancel }: ConfirmModalProps) => {
  return (
    <AppModal
      className={`w-full`}
      open={isOpenModal}
      onClose={() => onCancel()}
      title={title}
      showOverlay={true}
    >
      <div className="flex flex-col bg-white gap-4">
        <div className="flex flex-col items-end gap-4">
          {content ? (
            <div className="flex flex-col justify-center items-center gap-4">
              <p className="text-[16px] text-[#222222]">{content}</p>
            </div>
          ) : null}
          <div className="flex gap-4 items-end justify-end mt-6 w-full md:w-[70%]">
            <ButtonPrimary className="min-w-[140px]" size="sm" variant="outlined" color="gray">
              Tidak
            </ButtonPrimary>
            <ButtonPrimary className="min-w-[140px]" size="sm" variant="contained" onClick={() => onYes()}>
              Iya
            </ButtonPrimary>
          </div>
        </div>
      </div>
    </AppModal>
  )
}
