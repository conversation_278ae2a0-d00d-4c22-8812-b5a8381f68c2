'use client'
import TabItem from '@/components/_common/tabs/TabItem'
import TabList from '@/components/_common/tabs/TabList'
import { HeaderContent } from '@/components/admin/HeaderContent'
import Breadcrumb from '@/components/breadcrumbs/Breadcrumbs'
import { useState } from 'react'
import LogininInfo from './loginInfo'
import PinComponent from './pinComponent'
import NotifSetting from './notifSetting'

export default function SettingComponent() {
  const SettingTab = [
    {
      label: 'Login Info',
      content: <LogininInfo />,
    },
    {
      label: 'PIN Keamanan',
      content: <PinComponent />,
    },
    {
      label: 'Notifikasi',
      content: <NotifSetting />,
    },
  ]
  const [activeTabIndex, setActiveTabIndex] = useState<number>(0)

  return (
    <div className="flex items-center justify-center lg:mb-[198px] lg:mt-[64px] w-full lg:w-[930px] xl:w-[1120px] max-w-[1120px] py-4 lg:py-0 px-4 lg:px-0">
      <div className="flex flex-col gap-6 w-auto md:w-[548px]">
        <div className="flex flex-col gap-6 w-auto md:w-[548px]">
          <Breadcrumb containerClasses="hidden md:flex" pageName="Pengaturan" />
          <HeaderContent className="pb-0 mb-0 md:mb-6 md:pb-[18px]" title="Pengaturan" />
          {/* tab */}
          <div className="gap-4">
            <TabList
              onClickTabs={(index) => {
                console.log(index)
                setActiveTabIndex(index)
              }}
              className="top-navbar z-10 -mt-10 bg-white"
              activeTabIndex={activeTabIndex}
            >
              {SettingTab.map((setting, index) => {
                return (
                  <TabItem className="bg-main-100" key={index} label={setting.label}>
                    {setting.content}
                  </TabItem>
                )
              })}
            </TabList>
          </div>
        </div>
      </div>
    </div>
  )
}
