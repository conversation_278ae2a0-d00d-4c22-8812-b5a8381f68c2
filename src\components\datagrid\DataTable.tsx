'use client'

import React, { MutableRefObject, Suspense, useEffect, useMemo } from 'react'
import { keepPreviousData, QueryClient, QueryClientProvider, useQuery } from '@tanstack/react-query'
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  PaginationState,
  useReactTable,
  VisibilityState,
} from '@tanstack/react-table'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../table'
import { FooterTable } from './FooterTable'
import { IIcons, SVGIcons } from '../_common/icon'
import { RowActionMenu } from './RowActionMenu'
import { httpRequest } from '@/utils/network'
import { config } from '@/constans/config'
import { DateRange } from 'react-day-picker'
import moment from 'moment'

interface DataTableProps<TData, TValue> {
  hideHeader?: boolean
  hideAction?: boolean
  columns: ColumnDef<TData, TValue>[]
  columnVisibility?: VisibilityState | undefined
  data?: TData[]
  fetchData?: Function
  fetchPath?: string
  handleViewItem?: Function
  handleEditItem?: Function
  handleRemoveItem?: Function
  deleteFunc?: Function
  pageFilter?: any[]
  bulkFilter?: string
  dateRange?: DateRange | undefined
  pageSortBy?: any[]
  actionMenuList?: (row: any, refetch: () => void) => any[]
  onClickRow?: (row: any) => void
  setMeta?: (row: any) => void
  refetchRef?: MutableRefObject<() => void>
}

// const queryClient = new QueryClient()

export function DataTable<TData, TValue>({
  columns,
  columnVisibility,
  fetchData,
  fetchPath,
  handleViewItem,
  handleEditItem,
  handleRemoveItem,
  deleteFunc,
  hideAction,
  hideHeader,
  pageFilter,
  pageSortBy,
  actionMenuList,
  onClickRow,
  setMeta,
  refetchRef,
  dateRange,
  bulkFilter,
}: DataTableProps<TData, TValue>) {
  const [pagination, setPagination] = React.useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  })

  const fetchPagedData = async ({
    fetchPath = '',
    pageIndex,
    pageSize,
    pageFilter,
    pageSortBy,
    dateRange,
    bulkFilter,
  }: {
    fetchPath: string
    pageIndex: number
    pageSize: number
    pageFilter?: any[]
    pageSortBy?: any[]
    dateRange?: DateRange | undefined
    bulkFilter?: string
  }) => {
    let paramStr = ''
    const filterCombine = [...(pageFilter ?? [])]
    if (dateRange && dateRange.from && dateRange.to) {
      const tempDateFilter =
        dateRange && dateRange.from && dateRange.to
          ? {
              key: 'createdAt',
              operator: 'date',
              value: {
                gte: moment(dateRange.from).toISOString(),
                lt: moment(dateRange.to).toISOString(),
              },
            }
          : null
      filterCombine.push(tempDateFilter)
    }
    if (bulkFilter) {
      paramStr = `&${bulkFilter}`
    } else if (filterCombine && filterCombine?.length > 0) {
      let paramStrTemp = `&where={ ${filterCombine?.map((item, id) => {
        if (id === filterCombine.length - 1) {
          if (item.operator === 'equal') {
            return `"${item.key}": ${item.value === null ? null : `"${item.value}"`}`
          } else if (item.operator === 'in') {
            return `"${item.key}":{"${item.operator}":${JSON.stringify(item.value)}}`
          } else if (item.operator === 'date') {
            return `"${item.key}":${JSON.stringify(item.value)}`
          } else {
            return `"${item.key}": { "${item.operator}": ["${item.value}"]}`
          }
        } else {
          if (item.operator === 'equal') {
            return `"${item.key}": ${item.value === null ? null : `"${item.value}"`}`
          } else if (item.operator === 'in') {
            return `"${item.key}":{"${item.operator}":${JSON.stringify(item.value)}}`
          } else if (item.operator === 'date') {
            return `"${item.key}":${JSON.stringify(item.value)}`
          } else {
            return `"${item.key}": { "${item.operator}": ["${item.value}"]},`
          }
        }
      })} }`
      paramStr = paramStrTemp
    }
    // if (pageSortBy.length > 0) {
    //   const sortParams = pageSortBy[0]
    //   const sortyByDir = sortParams.desc ? 'desc' : 'asc'
    //   paramStr = `${paramStr}&sortby=${sortParams.id}&direction=${sortyByDir}`
    // }
    try {
      if (fetchData) {
        const response = fetchData(pagination)
        return response
      }
      const response = await httpRequest({
        method: 'get',
        url: `${config?.apiBaseUrl}${fetchPath}?page=${pageIndex + 1}&perPage=${pageSize}${paramStr}`,
      })
      return response
    } catch (e) {
      console.log(e)
    }
  }

  const dataQuery = useQuery({
    queryKey: ['data', pagination],
    queryFn: () =>
      fetchPagedData({
        fetchPath: fetchPath ?? '', // Pastikan fetchPath tidak undefined
        ...pagination,
        pageFilter,
        pageSortBy,
        dateRange,
        bulkFilter,
      }),
    placeholderData: keepPreviousData, // don't have 0 rows flash while changing pages/loading next page
  })
  const defaultData = React.useMemo(() => [], [])

  useEffect(() => {
    if (refetchRef) {
      refetchRef.current = dataQuery.refetch
    }
  }, [dataQuery.refetch, refetchRef])

  const RemoveMenuItem = useMemo(
    () => ({
      name: 'delete',
      icon: <SVGIcons name={IIcons.Time} />,
      label: 'Hapus',
      onClick: (item: any) => {
        // setItemToDelete(item)
        // setOpenDeleteModal(true)
        handleRemoveItem && handleRemoveItem(item)
      },
    }),
    [handleRemoveItem]
  )

  const ViewMenuItem = useMemo(
    () => ({
      name: 'view',
      icon: <SVGIcons name={IIcons.Time} />,
      label: 'View',
      onClick: (item: any) => {
        handleViewItem && handleViewItem(item)
      },
    }),
    [handleViewItem]
  )

  const EditMenuItem = useMemo(
    () => ({
      name: 'edit',
      icon: <SVGIcons name={IIcons.Time} />,
      label: 'Edit',
      onClick: (item: any) => {
        handleEditItem && handleEditItem(item)
      },
    }),
    [handleEditItem]
  )

  const actionsMenu = (row: any) => {
    const actionMenuCustom = actionMenuList ? actionMenuList(row, dataQuery?.refetch) : []
    let actionsMenu: any[] = [...actionMenuCustom]
    if (handleViewItem) {
      actionsMenu = [...actionsMenu, ViewMenuItem]
    }
    if (handleEditItem) {
      actionsMenu = [...actionsMenu, EditMenuItem]
    }
    if (deleteFunc) {
      actionsMenu = [...actionsMenu, RemoveMenuItem]
    }
    return actionsMenu
  }

  const getActionMenuList = actionsMenu

  const dataGridColumns = useMemo(
    () =>
      hideAction
        ? [...columns]
        : [
            ...columns,
            {
              accessorKey: 'action',
              header: 'Aksi',
              cell: (params: { row: any }) => {
                return getActionMenuList ? (
                  <RowActionMenu actions={getActionMenuList(params.row)} row={params.row} />
                ) : (
                  ''
                )
              },
            },
          ],
    [columns, getActionMenuList, hideAction]
  )

  useEffect(() => {
    setMeta && setMeta({ meta: dataQuery.data?.meta, additionalMeta: dataQuery.data?.additionalMeta })
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dataQuery.data])

  const table = useReactTable({
    data: dataQuery.data?.data ?? defaultData,
    columns: dataGridColumns as unknown as ColumnDef<any>[],
    state: {
      columnVisibility: columnVisibility || {},
      pagination,
    },
    onPaginationChange: setPagination,
    rowCount: dataQuery.data?.meta?.total,
    getCoreRowModel: getCoreRowModel(),
    manualPagination: true,
    debugTable: true,
  })

  return (
    <>
      <div className="overflow-hidden">
        <div className="relative w-full overflow-auto">
          <Table>
            {!hideHeader && (
              <TableHeader>
                {table.getHeaderGroups().map((headerGroup) => (
                  <TableRow data-id={headerGroup.id} key={headerGroup.id}>
                    {headerGroup.headers.map((header) => {
                      return (
                        <TableHead key={header.id} data-id={header.id}>
                          {header.isPlaceholder
                            ? null
                            : flexRender(header.column.columnDef.header, header.getContext())}
                        </TableHead>
                      )
                    })}
                  </TableRow>
                ))}
              </TableHeader>
            )}
            <TableBody>
              {table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row) => (
                  <>
                    {!!onClickRow ? (
                      <TableRow
                        className="hover:bg-[#FBFBFB] cursor-pointer"
                        key={row.id}
                        data-id={row.id}
                        data-state={row.getIsSelected() && 'selected'}
                        onClickRow={(e) => {
                          e.stopPropagation()
                          onClickRow(row.original)
                        }}
                      >
                        {row.getVisibleCells().map((cell) => (
                          <TableCell
                            key={cell.id}
                            data-id={cell.id}
                            align={(cell.column.columnDef.meta as any)?.style.textAlign}
                          >
                            {flexRender(cell.column.columnDef.cell, cell.getContext())}
                          </TableCell>
                        ))}
                      </TableRow>
                    ) : (
                      <TableRow
                        className="hover:bg-[#FBFBFB]"
                        key={row.id}
                        data-id={row.id}
                        data-state={row.getIsSelected() && 'selected'}
                      >
                        {row.getVisibleCells().map((cell) => (
                          <TableCell
                            key={cell.id}
                            data-id={cell.id}
                            align={(cell.column.columnDef.meta as any)?.style.textAlign}
                          >
                            {flexRender(cell.column.columnDef.cell, cell.getContext())}
                          </TableCell>
                        ))}
                      </TableRow>
                    )}
                  </>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={columns.length} className="h-24 text-center wrap">
                    No results.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </div>
      <FooterTable
        totalPage={dataQuery.data?.meta?.lastPage}
        fromRow={
          dataQuery.data?.meta?.currentPage === 1
            ? 1
            : (dataQuery.data?.meta?.currentPage - 1) * dataQuery.data?.meta?.perPage + 1
        }
        toRow={
          (dataQuery.data?.meta?.currentPage - 1) * dataQuery.data?.meta?.perPage +
          (dataQuery.data?.data?.length ?? 0)
        }
        totalRow={dataQuery.data?.meta?.total}
        getState={table.getState}
        setPageSize={table.setPageSize}
        previousPage={table.previousPage}
        getCanPreviousPage={table.getCanPreviousPage}
        setPageIndex={table.setPageIndex}
        nextPage={table.nextPage}
        getCanNextPage={table.getCanNextPage}
      />
    </>
  )
}

export const DataGrid = <TData, TValue>({
  hideHeader,
  hideAction,
  columns,
  columnVisibility,
  fetchData,
  fetchPath,
  deleteFunc,
  handleViewItem,
  handleEditItem,
  handleRemoveItem,
  actionMenuList,
  onClickRow,
  setMeta,
  refetchRef,
  pageFilter,
  dateRange,
  bulkFilter,
}: DataTableProps<TData, TValue>) => {
  useEffect(() => {
    refetchRef?.current && refetchRef.current()
  }, [dateRange, refetchRef])
  return (
    <Suspense fallback={<p>Loading data...</p>}>
      <React.StrictMode>
        <DataTable
          fetchPath={fetchPath || undefined} // Opsional jika menggunakan fetchData
          fetchData={fetchData}
          columns={columns}
          columnVisibility={columnVisibility}
          deleteFunc={deleteFunc}
          handleViewItem={handleViewItem}
          handleEditItem={handleEditItem}
          handleRemoveItem={handleRemoveItem}
          hideAction={hideAction}
          hideHeader={hideHeader}
          actionMenuList={actionMenuList}
          onClickRow={onClickRow}
          setMeta={setMeta}
          refetchRef={refetchRef}
          pageFilter={pageFilter}
          dateRange={dateRange}
          bulkFilter={bulkFilter}
        />
      </React.StrictMode>
    </Suspense>
  )
}
