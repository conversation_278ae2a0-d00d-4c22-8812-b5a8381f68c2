'use client'
import { AppModal } from '@/components/_common/Modal/AppModal'
import { useState } from 'react'
import PinPassword from './settingProfile/pinPassword'

export default function FormToggle({
  title,
  content,
  contentType,
  titleModal,
  showModal,
  isActive: externalIsActive,
  onToggle,
}: {
  title: string
  content: any
  contentType: any
  titleModal: string
  showModal: boolean
  isActive?: boolean
  onToggle?: (status: boolean) => void
}) {
  const [isActive, setIsActive] = useState<boolean>(externalIsActive || false)
  const [isOpenModal, setIsOpenModal] = useState<boolean>(false)
  const handleClickAccountInformationOpen = () => {
    setIsOpenModal(true)
  }
  const handleClickAccountInformationClose = () => {
    setIsOpenModal(false)
    setIsActive(false)
  }
  const handleClickNotif = () => {
    setIsOpenModal(false)
  }
  const handleToggleActive = () => {
    const newStatus = !isActive
    setIsActive(newStatus)
    onToggle?.(newStatus)
  }

  return (
    <>
      <div className="flex flex-col gap-[6px] py-6 border-b border-[#EBEBEB]">
        <div className="flex items-center justify-between">
          <span className="text-[14px] font-bold">{title}</span>
          <div id="2" onClick={showModal ? handleClickAccountInformationOpen : handleClickNotif}>
            <button
              onClick={handleToggleActive}
              className={`w-[46px] h-[24px] relative rounded-[12px] transition-all duration-500 ease-in-out ${
                isActive ? 'bg-[#039EE9]' : 'bg-[#EBEBEB]'
              }`}
            >
              <div
                className={`w-5 h-5 absolute transition-all duration-500 ease-in-out top-[2px] rounded-full bg-white ${
                  isActive ? 'left-[24px]' : 'left-[2px]'
                }`}
              ></div>
            </button>
          </div>
        </div>
        <input
          disabled
          className={`${contentType == 'password' ? 'text-gray-200' : 'text-[#222222]'} border-0 bg-transparent`}
          type={contentType}
          value={content}
        />
      </div>

      {/* modal */}
      <AppModal
        className={`w-100 transition-all duration-500 ease-in-out ${isOpenModal ? 'opacity-100 scale-100' : 'opacity-0 scale-0'}`}
        open={isOpenModal}
        onClose={() => {
          handleClickAccountInformationClose()
        }}
        title={titleModal}
        showOverlay={true}
      >
        <PinPassword />
      </AppModal>
    </>
  )
}
