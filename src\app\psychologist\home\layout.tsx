'use client'
import { Flex } from '@/components/_common/ui'
import Image from 'next/image'
import BannerPsikologMobile from '../../../../public/images/bannerPsikologMobile.svg'

export default function AdminLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <>
      <div className="-mt-4 md:-mt-6 -ml-4 md:-ml-6 -mr-4 md:-mr-6 pointer-events-none absolute top-auto w-full xs:w-full sm:w-full md:w-full lg:w-full 2lg:w-full xl:w-full 2xl:w-full">
        <div className="relative h-[144px] w-full">
          <Image
            className=""
            src={'/images/banner/header.svg'}
            alt="banner header"
            fill
            style={{
              objectFit: 'cover',
              objectPosition: 'right',
            }}
          />
        </div>
      </div>

      <div className="z-10 relative w-full">{children}</div>
    </>
  )
}
