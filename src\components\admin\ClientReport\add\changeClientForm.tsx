import Breadcrumb from '@/components/breadcrumbs/Breadcrumbs'
import HeaderUbahClient from './headerUbahClient'
import ClientForm from './clientForm'
import NextAppointmentClientReport from './nextAppointment'
import ButtonPrimary from '@/components/_common/ButtonPrimary'

export default function ChangeClientForm() {
  return (
    <div className="flex flex-col gap-6">
      <Breadcrumb containerClasses="" pageName="Ubah Klien Report" />
      <HeaderUbahClient
        client="Hilmi Salim"
        date="Senin, 1 Agustus 2024"
        time="09:00 - 10:00 WIB"
        via="Telepon"
      />
      <ClientForm />
      <NextAppointmentClientReport />
      <div className="flex items-center justify-end gap-4">
        <ButtonPrimary className="w-full md:w-auto" variant="outlined" color="gray" size="sm">
          Batal
        </ButtonPrimary>
        <ButtonPrimary className="w-full md:w-auto" variant="contained" color="primary" size="sm">
          Simpan
        </ButtonPrimary>
      </div>
    </div>
  )
}
