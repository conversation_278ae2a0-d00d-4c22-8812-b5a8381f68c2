'use client'

import { LabelValue } from '@/components/_common/CardInfo/LabelValue'
import { ShowMore } from '@/components/_common/ShowMore/ShowMore'
import { Card, H4 } from '@/components/_common/ui'
import { longText } from '@/constans/dummy'

const Pendidikan = () => {
  return (
    <>
      <p>S1 Universitas Indonesia (2012-2016)</p>
      <p>S2 Universitas Indonesia (2017-2019)</p>
    </>
  )
}

export const ProfileClient = () => {
  return (
    <div className="grid grid-flow-cols-dense grid-cols-1 xs:grid-cols-1 lg:grid-cols-2 grid-rows-1 gap-4 items-start">
      <Card className="xs:p-3 sm:p-4 md:p-4 lg:p-4 xl:p-4 grid gap-4">
        <H4 bold>Informasi</H4>
        <div className="grid grid-flow-row-dense grid-cols-3 grid-rows-1 gap-4 ">
          <LabelValue label="Nama Pangilan" value="Mahira" />
          <LabelValue label="Email" value={<span className="text-main-100"><EMAIL></span>} />
          <LabelValue label="Pendidikan" value={<Pendidikan />} />
          <LabelValue label="Bio" value={<ShowMore id="bio" text={longText} />} />
        </div>
      </Card>
    </div>
  )
}
