'use client'
import { H4 } from '@/components/_common/ui'
import { ShowMore } from '@/components/_common/ShowMore/ShowMore'
import { InputImage } from '@/components/_common/InputImage'
import { useForm } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import * as yup from 'yup'
import { useEffect, useRef, useState } from 'react'
import { useToast } from '@/components/ui/use-toast'
import AppInput from '@/components/_common/input/Input'
import { AppSelect } from '@/components/_common/Select/AppSelect'
import { FormSetting } from '../FormSetting'
import { PsychologistProfile } from '@/interfaces/profile-service'
import { profileService } from '@/services/profile.service'
import { usePsychologistField } from './hook/usePsychologistField.hook'
import { usePsychologistProblemcategory } from './hook/usePsychologistProblemcategory.hook'
import { MultiSelect } from '@/components/_common/Select/AppMultiSelect'
import { ServiceOptions } from '@/constans/StaticOptions'
import { EducationHistoryProps, EducationInput } from '@/components/_common/EducationInput/EducationInput'
import { EducationHistoryView } from './EducationHistoryView'

const validationSchema = yup.object().shape({
  bio: yup.string().max(1000, 'Maksimal 1000 karakter').nullable(),
  sipp: yup.string().nullable(),
  str: yup.string().nullable(),
  profilePhoto: yup.string().nullable(),
  educationHistory: yup
    .array()
    .of(
      yup.object().shape({
        level: yup.string().nullable(),
        university: yup.string().nullable(),
        graduationYear: yup.string().nullable(),
      })
    )
    .nullable(),
  field: yup.string().nullable(),
  offlineLocation: yup.string().nullable(),
  problemCategory: yup.array().of(yup.string()),
  service: yup.string().nullable(),
})

const PsikologProfileRoot = ({ setting }: { setting: PsychologistProfile }) => {
  const { toast } = useToast()
  const ref = useRef<any>(null)
  const [isLoadingForm, setIsLoadingForm] = useState<boolean>(false)
  const [fieldEdit, setFieldEdit] = useState<string | null>(null)

  const {
    register,
    handleSubmit,
    trigger,
    getValues,
    setValue,
    reset,
    resetField,
    formState: { errors, isLoading, isSubmitting },
  } = useForm({
    resolver: yupResolver(validationSchema),
    defaultValues: {
      bio: '',
      sipp: '',
      str: '',
      profilePhoto: '',
      educationHistory: [],
      offlineLocation: '',
      problemCategory: [],
      field: '',
      service: '',
    },
  })

  useEffect(() => {
    reset({
      bio: setting.bio ?? '',
      sipp: setting.sipp ?? '',
      str: setting.str ?? '',
      profilePhoto: setting.profilePhoto ?? '',
      educationHistory: setting.educationHistory ?? [],
      offlineLocation: setting.offlineLocation ?? '',
      problemCategory: setting.problemCategory?.map((val) => val.id) ?? [],
      field: setting.field?.find((val) => val.id)?.id ?? '',
      service: setting.service ?? '',
    })
  }, [reset, setting])

  const uploadImage = async (imageUpload: any) => {
    if (imageUpload) {
      const formData = new FormData()
      formData.append('profilePhoto', imageUpload)
      await profileService.updatePsychologistProfile(formData)
      toast({
        variant: 'success',
        title: 'Perbaharui photo profil berhasil',
      })
    }
  }

  const handleOnSubmit = async (key: string) => {
    setIsLoadingForm(true)
    const isValid = await trigger([key as any])
    if (isValid) {
      try {
        const formData = new FormData()
        if (['problemCategory', 'educationHistory'].includes(key)) {
          const arrData = getValues(key as any)
          formData.append(`${key}`, JSON.stringify(arrData))
        } else {
          formData.append(key, getValues(key as any))
        }
        await profileService.updatePsychologistProfile(formData)
        toast({
          variant: 'success',
          title: 'Perbaharui data profil berhasil',
        })
        setIsLoadingForm(false)
        setFieldEdit(null)
      } catch (error) {
        toast({
          variant: 'danger',
          title: 'Perbaharui data profil gagal',
        })
        setFieldEdit(null)
        setIsLoadingForm(false)
      }
    } else {
      setIsLoadingForm(false)
    }
  }

  const { data: psychologistField } = usePsychologistField()
  const { data: psychologistProblemCategory } = usePsychologistProblemcategory()

  const psychologistFieldSelected = getValues('field')
    ? psychologistField?.filter((val: any) => getValues('field')?.includes(val.id))
    : []
  const psychologistProblemCategorySelected = getValues('problemCategory')
    ? psychologistProblemCategory?.filter((val: any) => getValues('problemCategory')?.includes(val.id))
    : []

  return (
    <div className="grid gap-y-6">
      <div className="flex flex-row gap-4 mt-4">
        <InputImage
          {...register('profilePhoto')}
          classinfo="w-full text-caption-md font-medium text-gray-300"
          objectPosition="center"
          height={120}
          width={120}
          name="profilePhoto"
          label="Foto"
          accept={'image/jpeg,image/png'}
          maxFileSizeMb={2}
          onChange={(file) => {
            if (file) {
              uploadImage(file)
            }
          }}
          preview={getValues('profilePhoto') || ''}
          inputRef={ref}
        />
      </div>
      <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
        <H4 className="col-span-2" bold>
          Profil Psikolog
        </H4>
        <div className="col-span-2">
          <FormSetting
            viewComponent={<ShowMore id={'show-more-bio'} text={getValues('bio') ?? ''} />}
            editComponent={
              <AppInput
                {...register('bio')}
                className="pt-0"
                type="textarea"
                value={getValues('bio')!}
                onChange={(val) => {
                  setValue('bio', val.target.value, { shouldValidate: true })
                }}
                rows={3}
                name="bio"
                label="Bio"
                errorMsg={!!errors.bio ? String(errors.bio.message) : undefined}
                placeholder="bio"
                maxChar={1000}
              />
            }
            label="Bio"
            isEdit={fieldEdit === 'bio'}
            isLoading={isLoadingForm}
            onEditButton={() => setFieldEdit('bio')}
            onCancel={() => {
              setFieldEdit(null)
              resetField('bio')
            }}
            onSubmit={() => handleOnSubmit('bio')}
          />
          <FormSetting
            viewComponent={
              getValues('field')?.length
                ? psychologistField?.find((val: any) => val.id === getValues('field'))?.name
                : 'Tidak Tersedia'
            }
            editComponent={
              <AppSelect
                {...register('field')}
                options={psychologistField || []}
                onChange={(val) => setValue('field', val, { shouldValidate: true })}
                value={getValues('field') ? String(getValues('field')) : ''}
                className="h-[50px]"
                label="Bidang"
                name="field"
                placeholder="Pilih Bidang"
                errorMsg={!!errors.field ? String(errors.field.message) : undefined}
              />
            }
            label="Bidang"
            isEdit={fieldEdit === 'field'}
            isLoading={isLoadingForm}
            onEditButton={() => setFieldEdit('field')}
            onCancel={() => {
              setFieldEdit(null)
              resetField('field')
            }}
            onSubmit={() => handleOnSubmit('field')}
          />
          <FormSetting
            viewComponent={
              psychologistProblemCategorySelected?.length
                ? psychologistProblemCategorySelected?.map((val: any) => val.problemCategory).join(', ')
                : 'Tidak Tersedia'
            }
            editComponent={
              <>
                <MultiSelect
                  options={psychologistProblemCategory ?? []}
                  defaultValue={
                    psychologistProblemCategorySelected
                      ? ([...psychologistProblemCategorySelected]?.map((val: any) => val.id) as string[])
                      : []
                  }
                  onValueChange={(value: string[]) => {
                    console.log(value, typeof value)
                    setValue('problemCategory', Array.isArray(value) ? value : [value], {
                      shouldValidate: true,
                    })
                  }}
                  label="Spesialisasi"
                  name="problemCategory"
                  placeholder="Pilih Spesialisasi"
                  errorMsg={!!errors.problemCategory ? String(errors.problemCategory.message) : undefined}
                />
              </>
            }
            label="Spesialisasi"
            isEdit={fieldEdit === 'problemCategory'}
            isLoading={isLoadingForm}
            onEditButton={() => setFieldEdit('problemCategory')}
            onCancel={() => {
              setFieldEdit(null)
              resetField('problemCategory')
            }}
            onSubmit={() => handleOnSubmit('problemCategory')}
          />
          <FormSetting
            viewComponent={getValues('service') ?? '-'}
            editComponent={
              <>
                <MultiSelect
                  options={ServiceOptions ?? []}
                  defaultValue={getValues('service') ? getValues('service')?.split(',') : []}
                  onValueChange={(value: string[]) => {
                    setValue('service', value?.join(','), {
                      shouldValidate: true,
                    })
                  }}
                  label="Layanan"
                  name="service"
                  placeholder="Pilih Layanan"
                  errorMsg={!!errors.service ? String(errors.service.message) : undefined}
                />
              </>
            }
            label="Layanan"
            isEdit={fieldEdit === 'service'}
            isLoading={isLoadingForm}
            onEditButton={() => setFieldEdit('service')}
            onCancel={() => {
              setFieldEdit(null)
              resetField('service')
            }}
            onSubmit={() => handleOnSubmit('service')}
          />
          <FormSetting
            viewComponent={getValues('offlineLocation') ?? ''}
            editComponent={
              <AppInput
                {...register('offlineLocation')}
                className="pt-0"
                type="text"
                value={getValues('offlineLocation')!}
                onChange={(val) => {
                  setValue('offlineLocation', val.target.value, { shouldValidate: true })
                }}
                name="offlineLocation"
                label="Tempat Praktik"
                errorMsg={!!errors.offlineLocation ? String(errors.offlineLocation.message) : undefined}
                placeholder="offlineLocation"
              />
            }
            label="Tempat Praktik"
            isEdit={fieldEdit === 'offlineLocation'}
            isLoading={isLoadingForm}
            onEditButton={() => setFieldEdit('offlineLocation')}
            onCancel={() => {
              setFieldEdit(null)
              resetField('offlineLocation')
            }}
            onSubmit={() => handleOnSubmit('offlineLocation')}
          />
          <FormSetting
            viewComponent={
              <EducationHistoryView
                history={(getValues('educationHistory') as EducationHistoryProps[]) ?? []}
              />
            }
            editComponent={
              <EducationInput
                education={(getValues('educationHistory') as EducationHistoryProps[]) ?? []}
                onSubmit={(val) => {
                  setValue('educationHistory', val, { shouldValidate: true })
                }}
              />
            }
            label="Pendidikan"
            isEdit={fieldEdit === 'educationHistory'}
            isLoading={isLoadingForm}
            onEditButton={() => setFieldEdit('educationHistory')}
            onCancel={() => {
              setFieldEdit(null)
              resetField('educationHistory')
            }}
            onSubmit={() => handleOnSubmit('educationHistory')}
          />
          <FormSetting
            viewComponent={getValues('sipp') ?? ''}
            editComponent={
              <AppInput
                {...register('sipp')}
                className="pt-0"
                type="text"
                value={getValues('sipp')!}
                onChange={(val) => {
                  setValue('sipp', val.target.value, { shouldValidate: true })
                }}
                name="sipp"
                label="SIPP"
                errorMsg={!!errors.sipp ? String(errors.sipp.message) : undefined}
                placeholder="sipp"
              />
            }
            label="SIPP"
            isEdit={fieldEdit === 'sipp'}
            isLoading={isLoadingForm}
            onEditButton={() => setFieldEdit('sipp')}
            onCancel={() => {
              setFieldEdit(null)
              resetField('sipp')
            }}
            onSubmit={() => handleOnSubmit('sipp')}
          />
          <FormSetting
            viewComponent={getValues('str') ?? ''}
            editComponent={
              <AppInput
                {...register('str')}
                className="pt-0"
                type="text"
                value={getValues('str')!}
                onChange={(val) => {
                  setValue('str', val.target.value, { shouldValidate: true })
                }}
                name="str"
                label="STR"
                errorMsg={!!errors.str ? String(errors.str.message) : undefined}
                placeholder="str"
              />
            }
            label="STR"
            isEdit={fieldEdit === 'str'}
            isLoading={isLoadingForm}
            onEditButton={() => setFieldEdit('str')}
            onCancel={() => {
              setFieldEdit(null)
              resetField('str')
            }}
            onSubmit={() => handleOnSubmit('str')}
          />
        </div>
      </div>
    </div>
  )
}

export default PsikologProfileRoot
