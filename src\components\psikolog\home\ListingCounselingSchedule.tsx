'use client'
import { ScheduleListProps } from '@/store/psikolog/schedule.reducer'
import ListJadwalPsikolog from './presentational/listJadwalPsikolog'

export default function ListingCounselingSchedule({ scheduleList }: { scheduleList: any[] }) {
  return (
    <>
      <div className="flex flex-col gap-[16px]">
        {scheduleList?.length
          ? scheduleList.map((item: ScheduleListProps, id) => {
              return <ListJadwalPsikolog {...item} key={id} />
            })
          : null}
      </div>
    </>
  )
}
