'use client'

import React from 'react'
import CheckedSvg from '../../../../../public/images/checkedbox.svg'
import CheckSvg from '../../../../../public/images/checkbox.svg'

type OnboardingStepsProps = {
  VERIFIED_EMAIL: boolean
  COMPLETE_PSYCHOLOGIST_PROFILE: boolean
  HAS_AVAILABILITY: boolean
  SHARE_SOCIAL_MEDIA: boolean
  meta: {
    percentage: number
    totalSteps: number
    completedCount: number
    isDisplayed: boolean
    isCompleted: boolean
  }
}
const OnboardingSteps = ({ data }: { data: OnboardingStepsProps }) => {
  // home Checkboxs
  const checkboxs = [
    { id: 'VERIFIED_EMAIL', title: 'Verifikasi Email', checked: data?.VERIFIED_EMAIL ?? false },
    {
      id: 'COMPLETE_PSYCHOLOGIST_PROFILE',
      title: '<PERSON><PERSON><PERSON><PERSON> profile Anda',
      checked: data?.COMPLETE_PSYCHOLOGIST_PROFILE ?? false,
    },
    { id: 'HAS_AVAILABILITY', title: 'Atur jadwal konseling', checked: data?.HAS_AVAILABILITY ?? false },
    {
      id: 'SHARE_SOCIAL_MEDIA',
      title: 'Bagikan ke sosial media Anda',
      checked: data?.SHARE_SOCIAL_MEDIA ?? false,
    },
  ]

  const progressBarWidth = `${data?.meta?.percentage ?? 0}%`

  const isDisplay = data?.meta?.isDisplayed
  if (!isDisplay) return null

  return (
    <>
      <div className="flex flex-col gap-3 bg-white border border-[#EBEBEB] rounded-xl p-6">
        {/* header */}
        <div className="flex justify-between items-center gap-4 md:gap-0">
          <p className="font-bold text-[16px] md:text-[18px] text-[#242424]">
            Mulai bersama Mentalhealing.id dari sini
          </p>
          <p className="font-bold text-[12px] md:text-[14px] text-[#C0C0C0] text-end">
            {data?.meta?.completedCount ?? 0} dari {data?.meta?.totalSteps ?? 0} selesai
          </p>
        </div>
        {/* progress bar */}
        <div className="w-full h-2 rounded-lg bg-[#EBEBEB]">
          <div
            className="h-2 rounded-lg bg-main-100 transition-all ease-in-out duration-700"
            style={{ width: progressBarWidth }}
          ></div>
        </div>
        {/* checkbox */}
        <div className="flex flex-col gap-4 md:gap-3">
          {checkboxs.map((item) => (
            <div key={item.id} className="flex items-center gap-2 md:gap-3">
              <input
                id={`checkboxHome-${item.id}`}
                className="cursor-pointer rounded-full hidden"
                type="checkbox"
                checked={item.checked ?? false}
              />
              {/* label mobile */}
              <label className="cursor-pointer inline-block md:hidden" htmlFor={`checkboxHome-${item.id}`}>
                {item.checked ? <CheckedSvg /> : <CheckSvg />}
              </label>
              {/* label desktop */}
              <label className="cursor-pointer md:inline-block hidden" htmlFor={`checkboxHome-${item.id}`}>
                {item.checked ? <CheckedSvg /> : <CheckSvg />}
              </label>
              <p className="text-[14px] tetx-[#222222]">{item.title}</p>
            </div>
          ))}
        </div>
      </div>
    </>
  )
}
export default OnboardingSteps
