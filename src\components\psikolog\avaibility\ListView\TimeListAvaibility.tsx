import AvaibilityAction from './AvaibilityAction'
import AvaibilityTime from './AvaibilityTime'
import { useToast } from '@/components/ui/use-toast'
import { AvaibilityItemProps, AvaibilityWithActionProps } from '../useAvaibilityList.hook'

export const TimeListAvaibility = ({
  id,
  day,
  timeList,
  isActive,
  addItem,
  onRemoveTime,
  onSetTime,
  hideCopyButton,
  onSetEndTime,
  onSetStartTime,
  refetch,
}: AvaibilityWithActionProps & {
  onRemoveTime?: (val: AvaibilityItemProps) => void
  onSetStartTime?: (val: AvaibilityItemProps) => void
  onSetEndTime?: (val: AvaibilityItemProps) => void
  onSetTime?: (val: { day: string; targetId: number; targetKey: string; value: string }) => void
}) => {
  const { toast } = useToast()

  const handleAddTime = () => {
    if (addItem) {
      addItem(day)
    } else if (timeList.length >= 12) {
      toast({
        variant: 'danger',
        title: 'Anda sudah mencapai jadwal maksimal, jangan lupa beristirahat :D',
      })
      return
    } else {
      // dispatch(addNewTime(day))
    }
  }
  return (
    <>
      {onSetStartTime && onSetEndTime && onRemoveTime ? (
        <AvaibilityTime
          id={id}
          day={day}
          timeList={timeList}
          isActive={isActive}
          onRemoveTime={onRemoveTime}
          onSetEndTime={onSetEndTime}
          onSetStartTime={onSetStartTime}
          // onSetTime={(val) => onSetTime(val)}
        />
      ) : (
        <AvaibilityTime id={id} day={day} timeList={timeList} isActive={isActive} />
      )}
      <div className={`hidden sm:flex`}>
        <AvaibilityAction
          onAdd={() => {
            handleAddTime()
          }}
          onCopy={() => {}}
          day={id}
          hideCopyButton={hideCopyButton}
          refetch={refetch}
        />
      </div>
    </>
  )
}
