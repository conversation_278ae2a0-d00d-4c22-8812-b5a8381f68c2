'use client'
import { useEffect, useState } from 'react'
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion'
import { faqService, CategoryProps } from '@/services/faq.service'
import { Skeleton } from '@/components/ui/skeleton'

type FaqType = {
  answer: string
  category: string
  createdAt: string
  createdBy: string | null
  id: string
  modifiedAt: string
  modifiedBy: null | string
  order: number
  question: string
}
export const Faq = ({ category }: { category: CategoryProps }) => {
  const [faq, setFaq] = useState<FaqType[][]>([])
  const [isLoading, setIsLoading] = useState<boolean>(false)

  const groupingArray = (amount: number, list: FaqType[]) => {
    let groupByN = (n: number, arr: any[]) => {
      let result = []
      for (let i = 0; i < arr.length; i += n) result.push(arr.slice(i, i + n))
      return result
    }
    const newData = groupByN(amount, list)
    return newData
  }

  useEffect(() => {
    setIsLoading(true)
    faqService
      .getFaqByCategory(category)
      .then((response) => {
        if (response) {
          const faqList = groupingArray(response.length / 2, response)
          setFaq(faqList)
        }
      })
      .catch((error) => {
        setIsLoading(false)
      })
      .finally(() => {
        setIsLoading(false)
      })
  }, [category])

  return (
    <div className="flex flex-col gap-y-10 items-center">
      <span className="text-subheading-md md:text-[38px] font-bold text-gray-400 text-center">
        Yang sering ditanyakan
      </span>
      <div className="grid grid-cols-1 md:grid-cols-2 md:gap-6">
        {isLoading && (
          <>
            <Skeleton className="w-[300px] h-[80px] rounded-xl" />
            <Skeleton className="w-[300px] h-[80px] rounded-xl" />
            <Skeleton className="w-[300px] h-[80px] rounded-xl" />
            <Skeleton className="w-[300px] h-[80px] rounded-xl" />
          </>
        )}
        {faq.length
          ? faq.map((group, id) => {
              return (
                <Accordion key={id} type="single" collapsible className="w-full">
                  {group.length &&
                    group.map((item) => {
                      return (
                        <AccordionItem
                          key={item.id}
                          value={item.id}
                          className="p-6 mb-4 gap-y-4 border border-line-200 rounded-card"
                        >
                          <AccordionTrigger className={`py-0 hover:no-underline items-baseline`}>
                            <div className="flex justify-items-start w-full">
                              <span className="text-gray-400 text-body-md font-bold text-left">
                                {item.question}
                              </span>
                            </div>
                          </AccordionTrigger>
                          <AccordionContent className="py-4">
                            <span
                              className="text-[#535353]"
                              dangerouslySetInnerHTML={{ __html: item.answer }}
                            ></span>
                          </AccordionContent>
                        </AccordionItem>
                      )
                    })}
                </Accordion>
              )
            })
          : null}
      </div>
    </div>
  )
}
