import { Skeleton } from '@/components/ui/skeleton'
import WrapperAuth from '../auth/WrapperAuth'

export function LoadingAuth() {
  return (
    <>
      <WrapperAuth>
        <Skeleton className="max-w-[500px] md:w-[500px] rounded-xl" />
        <div className="grid gap-y-4">
          <Skeleton className="mt-2 h-[20px] w-1/4 rounded-xl" />
          <Skeleton className="mt-2 h-[10px] w-1/2 rounded-xl" />
          <Skeleton className="h-[48px] w-full rounded-xl" />

          <Skeleton className="mt-2 h-[20px] w-1/2 rounded-xl" />
          <Skeleton className="mt-2 h-[48px] w-full rounded-xl" />
          <Skeleton className="mt-2 h-[20px] w-1/2 rounded-xl" />
          <Skeleton className="mt-2 h-[48px] w-full rounded-xl" />

          <Skeleton className="h-[48px] w-[150px]" />
        </div>
      </WrapperAuth>
    </>
  )
}
