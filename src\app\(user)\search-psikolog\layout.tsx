import { FooterSection } from '@/components/home/<USER>/Footer/FooterSection'
import { Header } from '@/components/SearchPsikolog/section/Header'
import { STATIC_DATA } from '@/constans/STATIC_DATA'
import { Suspense } from 'react'

export default function UserLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <Suspense>
      <div className="max-w-screen relative w-full">
        <Header title="Psikolog Kami" titleMobile="Psikolog" />
      </div>
      {children}
      <div className="max-w-screen relative w-full pt-10">
        <FooterSection {...STATIC_DATA.Psikolog.footer} showButtonCounselling />
      </div>
    </Suspense>
  )
}
