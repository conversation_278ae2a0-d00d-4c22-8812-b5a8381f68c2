'use client'
import React from 'react'
import { Card } from '@/components/_common/ui'
import { SVGIcons, IIcons } from '@/components/_common/icon'
import Image from 'next/image'

interface AppointmentDetailsProps {
  displayData: {
    formattedDate: string
    method: string
    psychologistImage?: string
    psychologistName: string
    specializations: string
  }
  hasFilledNotes: boolean
  getSelectedCategoryNames: () => string
  openNotesModal: () => void
}

const AppointmentDetails: React.FC<AppointmentDetailsProps> = ({
  displayData,
  hasFilledNotes,
  getSelectedCategoryNames,
  openNotesModal,
}) => {
  return (
    <Card className="p-6 border rounded-lg">
      <div className="mb-6">
        <p className="text-gray-500">Jadwal Konselingmu</p>
        <p className="font-semibold text-lg">{displayData.formattedDate}</p>
        <p className="text-gray-500">{displayData.method === 'Call' ? 'Call' : 'VideoCall'} - Google Meet</p>
      </div>

      <div className="flex items-center gap-4 mb-6">
        <div className="w-16 h-16 rounded-full bg-gray-100 overflow-hidden flex items-center justify-center">
          {displayData.psychologistImage ? (
            <Image
              width={64}
              height={64}
              src={displayData.psychologistImage}
              alt={displayData.psychologistName}
            />
          ) : (
            <SVGIcons name={IIcons.User} className="text-gray-400 h-8 w-8" />
          )}
        </div>
        <div>
          <h3 className="font-semibold">{displayData.psychologistName}</h3>
          <p className="text-gray-500 text-sm">{displayData.specializations}</p>
        </div>
      </div>

      {hasFilledNotes ? (
        <div
          className="flex items-center justify-between text-gray-600 p-4 border rounded-lg cursor-pointer hover:bg-gray-50"
          onClick={openNotesModal}
        >
          <div className="flex items-center">
            <div className="h-8 w-8 rounded-full bg-main-50 flex items-center justify-center mr-3">
              <SVGIcons name={IIcons.Repeat} className="text-main-100" />
            </div>
            <div>
              <p className="font-semibold">Catatan Konseling</p>
              <p className="text-gray-500 text-sm">Kategori: {getSelectedCategoryNames()}</p>
            </div>
          </div>
          <SVGIcons name={IIcons.ArrowRight} className="text-gray-400" />
        </div>
      ) : (
        <div
          className="flex items-center text-gray-600 p-4 border rounded-lg cursor-pointer hover:bg-gray-50"
          onClick={openNotesModal}
        >
          <SVGIcons name={IIcons.Document} className="mr-3" />
          <p className="flex-1">Isi Catatan Konseling</p>
          <SVGIcons name={IIcons.ArrowRight} className="text-gray-400" />
        </div>
      )}
    </Card>
  )
}

export default AppointmentDetails
