/* eslint-disable @next/next/no-img-element */
import <PERSON>quee from 'react-fast-marquee'

const ColaborationsLogo = ({ label, list }: { label: string; list: string[] }) => {
  return (
    <div className="flex flex-col gap-y-6 items-center">
      <span className="text-body-lg font-medium text-gray-300 text-center px-4">{label}</span>
      <div className="flex flex-row items-center grayscale w-full">
        <Marquee direction="left" autoFill={true} className="w-full">
          {list.length &&
            list.map((image) => {
              return (
                <div key={image} className="relative h-[80px] w-auto flex justify-center items-center">
                  <img
                    src={image}
                    alt="logo icon"
                    className="mr-[70px]"
                    style={{
                      width: 'auto',
                      height: '80px',
                      objectFit: 'contain',
                    }}
                    width={50}
                    height={80}
                  />
                </div>
              )
            })}
        </Marquee>
      </div>
    </div>
  )
}

export default ColaborationsLogo
