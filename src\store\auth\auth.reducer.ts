import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import { verifyAuth, UserAuth, onboardingAuth } from './auth.action'
import { User } from 'firebase/auth'
import { setCookie } from 'cookies-next'
import { deleteAuthCookies } from '@/lib/cookies'
import TokenService from '@/services/token.sevice'

export type UserProps = UserAuth & {
  email: string
  firebaseId: string
  isCompleteOnboard: boolean
  role?: string
  id?: string
  photoUrl: string
  firebaseCurrentUser?: User | null
}

type AuthenticationProps = {
  isCompleteOnboard: boolean
  isLoading: boolean
  isSuccess: boolean
  isError: string
  user: UserProps | null
  token: string
  showEmailVerification: boolean
}

const setAuthCookie = (token: string, name: string) => {
  const toBase64 = Buffer.from(token ?? '').toString('base64')

  setCookie(name ?? '', toBase64, {
    maxAge: 30 * 24 * 60 * 60,
    path: '/',
    // more security options here
    // sameSite: 'strict',
    // httpOnly: true,
    // secure: process.env.NODE_ENV === 'production',
  })
}

const authenticationSlice = createSlice({
  name: 'auth',
  initialState: {
    isCompleteOnboard: false,
    isLoading: false,
    isSuccess: false,
    isError: '',
    user: {
      isCompleteOnboard: false,
      token: undefined,
      role: undefined,
      email: '',
      firebaseId: '',
      id: undefined,
      photoUrl: '',
      firebaseCurrentUser: null,
    },
    token: '',
    showEmailVerification: false,
  } as AuthenticationProps,
  reducers: {
    logout(state) {
      state.user = null
      state.isSuccess = false
      state.isError = ''
      state.token = ''

      // Remove Cookies
      deleteAuthCookies()
      TokenService.removeLocalTokenAndRole()
    },
    setShowEmailVerification(state, action) {
      state.showEmailVerification = action.payload
    },
    setError(state, action) {
      state.isError = action.payload
    },
    setAuthInformation(
      state,
      action: PayloadAction<{ isCompleteOnboard: boolean; token: string; role: string; id?: string }>
    ) {
      state.token = action.payload.token
      state.isCompleteOnboard = action.payload.isCompleteOnboard
      state.user = {
        ...state.user,
        role: action.payload.role,
        id: action.payload.id,
      } as UserProps

      // SET TOKEN COOKIES
      setAuthCookie(action.payload.token, 'auth_token')
      setAuthCookie(action.payload.role, 'auth_role')
    },
    setProfile(state, action) {
      state.user = action.payload
    },
    setPhotoURL(state, action) {
      state.user!.photoUrl = action.payload
    },
  },
  extraReducers: (builder) => {
    // verify reducers
    builder.addCase(verifyAuth.pending, (state) => {
      state.isLoading = true
      state.isError = ''
      state.isSuccess = false
    })
    builder.addCase(verifyAuth.fulfilled, (state, action: PayloadAction<any>) => {
      state.isCompleteOnboard = action.payload.user.isCompleteOnboard
      state.isLoading = false
      state.isError = ''
      state.isSuccess = true
      state.user = {
        ...state.user,
        ...action.payload.user,
        photoUrl: action.payload.firebaseCurrentUser?.photoURL,
        firebaseCurrentUser: action.payload.firebaseCurrentUser,
      }
      state.token = action.payload.token

      // SET TOKEN COOKIES
      setAuthCookie(action.payload.token, 'auth_token')
      setAuthCookie(action.payload.user.role, 'auth_role')
    })
    builder.addCase(verifyAuth.rejected, (state, action: PayloadAction<any>) => {
      const errorUsername = action.payload?.response?.data?.errors?.[0].errors?.[0]
      state.isLoading = false
      state.isError = errorUsername
        ? errorUsername
        : action.payload?.response?.data?.message || 'Something went wrong'
      state.isSuccess = false
    })

    // ONBOARD
    builder.addCase(onboardingAuth.pending, (state) => {
      state.isLoading = true
      state.isError = ''
    })
    builder.addCase(onboardingAuth.fulfilled, (state, action: PayloadAction<any>) => {
      state.isCompleteOnboard = action.payload.user.isCompleteOnboard
      state.isLoading = false
      state.isError = ''
      state.isSuccess = true
      state.user = {
        ...state.user,
        ...action.payload.user,
      }
      state.token = action.payload.token
    })
    builder.addCase(onboardingAuth.rejected, (state, action: PayloadAction<any>) => {
      const errorUsername = action.payload?.response?.data?.errors?.[0].errors?.[0]
      state.isCompleteOnboard = false
      state.isLoading = false
      state.isError = errorUsername
        ? errorUsername
        : action.payload?.response?.data?.message || 'Something went wrong'
      state.isSuccess = false
    })
  },
})

export const { logout, setProfile, setError, setAuthInformation, setPhotoURL, setShowEmailVerification } =
  authenticationSlice.actions
export default authenticationSlice.reducer
