'use client'
import { IIcons, SVGIcons } from '@/components/_common/icon'
import { AppBigCaption, AppBigText, Card, H2 } from '@/components/_common/ui'

type CardTotaltype = {
  heading: string
  subHeading?: string
  value?: string
  isBlue?: boolean
}

export const CardTotal = ({ heading, subHeading, value, isBlue }: CardTotaltype) => {
  return (
    <Card className="flex items-start flex-col bg-white p-6 w-full gap-4">
      <div className="flex flex-1 w-full justify-between">
        <AppBigText bold className="flex items-center">
          <span>{heading}</span>
        </AppBigText>
        <SVGIcons className="ml-2" name={IIcons.ArrowRight} />
      </div>
      <div className="flex-1">
        <H2 className={`flex items-center ${isBlue ? 'text-main-100' : 'text-gray-400'}`}>{value}</H2>
      </div>
      <div className="flex-1">
        <AppBigCaption className={`flex items-center`}>{subHeading}</AppBigCaption>
      </div>
    </Card>
  )
}
