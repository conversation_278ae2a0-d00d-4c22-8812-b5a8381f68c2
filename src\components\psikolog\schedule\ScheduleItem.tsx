import ButtonPrimary from '@/components/_common/ButtonPrimary'
import { IIcons } from '@/components/_common/icon'
import { ListInformation } from '@/components/_common/ListInformation'
import { AppBigText } from '@/components/_common/ui'
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion'
import React from 'react'
import { ScheduleItemContent } from './ScheduleItemContent'
import { ScheduleListProps } from '@/store/psikolog/schedule.reducer'
import {
  formatStringToFulldateOutput,
  formatStringToFullDateTimeOutput,
  formatStringToStartEndTimeOutput,
} from '@/utils/displayDate'
import { PaymentStatus } from '@/constans/StaticOptions'
import { differenceInSeconds } from 'date-fns'
import { NoDataFound } from '@/components/_common/NoData/NoDataFound'
import { LoadingAccordion } from '@/components/loading/LoadingAccordion'
import useGetPsychologistTimezone from '@/hooks/useGetPsychologistTimezone.hook'
import useGetTimezoneLabel from '@/hooks/useGetTimezone.hook'
import Link from 'next/link'
import { Routes } from '@/constans/routes'

export type ActionItemProps = {
  onStartCounselling?: (arg: any) => void
  onReschedule?: (arg: any) => void
  onApprove?: (arg: any) => void
  onReject?: (arg: any) => void
  onCreateClientReport?: (arg: any) => void
  onChangeCLientReport?: (arg: any) => void
  onConfirmationSession?: (arg: any) => void
  onCancelSession?: (arg: any) => void
  itemList: ScheduleListProps[]
  isLoadingApp?: boolean
}
type ScheduleItemProps = ActionItemProps & {
  itemList: ScheduleListProps[]
}

export const ScheduleItem = ({
  isLoadingApp,
  itemList,
  onStartCounselling,
  onReschedule,
  onApprove,
  onReject,
  onCreateClientReport,
  onChangeCLientReport,
  onConfirmationSession,
  onCancelSession,
}: ScheduleItemProps) => {
  const listOfContent = itemList && itemList.length > 0 ? itemList : []
  const [value, setValue] = React.useState(listOfContent[0]?.id || '')

  const showCreateClientReportButton = (item: ScheduleListProps) =>
    !!onCreateClientReport && !item.clientReport
  const showApproveCounselingButton = (item: ScheduleListProps) =>
    !!onApprove &&
    (item.status === PaymentStatus.PAID_PAYMENT || item.status === PaymentStatus.RESCHEDULE_BY_CLIENT)
  const showRescheduleButton = (item: ScheduleListProps) =>
    !!onReschedule &&
    (item.status === PaymentStatus.APPROVED ||
      item.status === PaymentStatus.PAID_PAYMENT ||
      item.status === PaymentStatus.RESCHEDULE_BY_CLIENT)
  const showChangeCLientReportButton = (item: ScheduleListProps) =>
    !!onChangeCLientReport && !!item.clientReport
  const showRejectButton = (item: ScheduleListProps) =>
    !!onReject &&
    (item.status === PaymentStatus.PAID_PAYMENT || item.status === PaymentStatus.RESCHEDULE_BY_CLIENT)
  const showStartCounselingButton = (item: ScheduleListProps) =>
    !!onStartCounselling &&
    (item.status === PaymentStatus.APPROVED || item.status === PaymentStatus.PAID_PAYMENT)
  const showCompleteConfirmationButton = (item: ScheduleListProps) =>
    !!onConfirmationSession && item.status === PaymentStatus.INPROGRESS
  const showBadgeCounselingProgress = (item: ScheduleListProps) => {
    return differenceInSeconds(new Date(item.endTime), new Date()) > 0
  }
  const showCanceledSessionButton = (item: ScheduleListProps) =>
    !!onCancelSession && item.status === PaymentStatus.INPROGRESS

  const psychologistTimezone = useGetPsychologistTimezone()
  const timeZoneLabel = useGetTimezoneLabel()

  const titleCounselling = (item: ScheduleListProps) => {
    if (item.status === PaymentStatus.RESCHEDULE_BY_PSYCHOLOGIST) {
      return 'Menunggu Konfirmasi klien '
    } else {
      return 'Konseling untuk '
    }
  }

  const getPreviousSchedule = (item: ScheduleListProps) => {
    if (
      item.status === PaymentStatus.RESCHEDULE_BY_CLIENT ||
      item.status === PaymentStatus.RESCHEDULE_BY_PSYCHOLOGIST
    ) {
      const previousShedule = item.startTime
        ? formatStringToFullDateTimeOutput({
            date: item.startTime,
            timezone: psychologistTimezone,
            timeLabel: timeZoneLabel,
            isUTC: true,
          })
        : '-'
      return (
        <div className="grid pb-4">
          <span className="text-gray-200 text-left">Jadwal sebelumnya: {previousShedule}</span>
        </div>
      )
    }
    return null
  }

  return (
    <Accordion type="single" collapsible className="w-full" value={value} onValueChange={setValue}>
      {isLoadingApp ? (
        <LoadingAccordion />
      ) : listOfContent?.length ? (
        listOfContent.map((list, listIndex) => {
          const startTimeLabel =
            list.status === PaymentStatus.RESCHEDULE_BY_PSYCHOLOGIST && list.startTimeByPsychologist
              ? list.startTimeByPsychologist
              : list.status === PaymentStatus.RESCHEDULE_BY_CLIENT && list.startTimeByClient
                ? list.startTimeByClient
                : list.startTime
          const dateLabel = formatStringToFulldateOutput(startTimeLabel)
          const timeLabel = formatStringToStartEndTimeOutput({
            date: startTimeLabel,
            duration: list?.duration,
            timezone: psychologistTimezone,
            timeLabel: timeZoneLabel,
            isUTC: true,
          })
          const isNavigateToClientDetails = [
            PaymentStatus.APPROVED,
            PaymentStatus.INPROGRESS,
            PaymentStatus.PAID_PAYMENT,
            PaymentStatus.RESCHEDULE_BY_CLIENT,
            PaymentStatus.RESCHEDULE_BY_PSYCHOLOGIST,
          ].includes(list?.status as PaymentStatus)
          return (
            <AccordionItem
              key={listIndex}
              value={list.id}
              className="p-6 my-4 gap-y-4 border border-line-200 rounded-card"
            >
              <AccordionTrigger
                label={value === list.id ? 'Tutup' : 'Lihat Detail'}
                className={`py-0 hover:no-underline items-baseline ${value === list.id ? 'border-b border-line-200' : ''}`}
              >
                <div className="grid justify-items-start">
                  <AppBigText bold className="text-left">
                    {titleCounselling(list)}
                    {isNavigateToClientDetails ? (
                      <Link
                        href={Routes.PsychologistClientDetails.replace('[id]', list.client?.id)}
                        className="text-main-100"
                      >
                        {list.client?.fullName}
                      </Link>
                    ) : (
                      <span>{list.client?.fullName}</span>
                    )}
                  </AppBigText>
                  <div className="grid">
                    <ListInformation
                      className="border-b-0 gap-2"
                      listItem={[
                        { label: dateLabel, icon: IIcons.Calendar },
                        { label: timeLabel, icon: IIcons.Time },
                        {
                          label: list.method === 'Call' ? 'Call' : 'Video Call',
                          icon: list.method === 'Call' ? IIcons.Call : IIcons.VideoCall,
                        },
                      ]}
                    />
                  </div>
                  {getPreviousSchedule(list)}
                  {/* <div className="grid">
                    <span className="text-gray-200">Jadwal sebelumnya: {getPreviousSchedule(list)}</span>
                  </div> */}
                </div>
              </AccordionTrigger>
              <AccordionContent className="py-4">
                <ScheduleItemContent {...list} />
              </AccordionContent>
              <div className="flex flex-col w-full sm:flex-row gap-3">
                {showStartCounselingButton(list) && (
                  <ButtonPrimary
                    className="col-span-2 md:col-span-1 lg:col-span-3 xl:col-span-2"
                    classLabel="text-nowrap"
                    size="sm"
                    variant={'contained'}
                    onClick={() => onStartCounselling && onStartCounselling(list)}
                  >
                    Mulai Konseling
                  </ButtonPrimary>
                )}
                {showCompleteConfirmationButton(list) && (
                  <div className="rounded-2xl flex flex-col sm:flex-row items-center border border-[#039EE9]">
                    <div className="px-4 text-body-md font-bold text-gray-300">
                      <div className="flex space-x-1 justify-center items-baseline">
                        <span className="sr-only">Loading...</span>
                        <span className="text-inherit pl-2">Konseling sedang berjalan</span>
                        <div className="loader flex items-center gap-1">
                          <span className="w-1 h-1 bg-gray-300 rounded-full"></span>
                          <span className="w-1 h-1 bg-gray-300 rounded-full"></span>
                          <span className="w-1 h-1 bg-gray-300 rounded-full"></span>
                        </div>
                      </div>
                    </div>
                    <ButtonPrimary
                      className="col-span-2 md:col-span-1 lg:col-span-3 xl:col-span-2 mr-0 rounded-none rounded-b-2xl sm:rounded-2xl sm:-mr-[2px] w-full sm:w-auto"
                      classLabel="text-nowrap"
                      size="sm"
                      variant={'contained'}
                      onClick={() => onConfirmationSession && onConfirmationSession(list)}
                    >
                      Konfirmasi Selesai
                    </ButtonPrimary>
                  </div>
                )}
                {showCanceledSessionButton(list) && (
                  <ButtonPrimary
                    className="col-span-2 md:col-span-1 lg:col-span-3 xl:col-span-2"
                    classLabel="text-nowrap"
                    size="sm"
                    variant={'outlined'}
                    color="gray"
                    onClick={() => onCancelSession && onCancelSession(list)}
                  >
                    Batalkan Sesi
                  </ButtonPrimary>
                )}
                {showCreateClientReportButton(list) && (
                  <ButtonPrimary
                    className="col-span-2 md:col-span-1 lg:col-span-3 xl:col-span-2"
                    classLabel="text-nowrap"
                    size="sm"
                    variant={'contained'}
                    onClick={() => onCreateClientReport && onCreateClientReport(list)}
                  >
                    Isi Klien Report
                  </ButtonPrimary>
                )}
                {/* <div className="flex flex-col w-full md:flex-row gap-2"> */}
                {showApproveCounselingButton(list) && (
                  <ButtonPrimary
                    className="col-span-2 md:col-span-1 lg:col-span-3 xl:col-span-2 md:min-w-[143px]"
                    size="sm"
                    variant={'contained'}
                    onClick={() => onApprove && onApprove(list)}
                  >
                    Terima
                  </ButtonPrimary>
                )}
                {showRescheduleButton(list) && (
                  <ButtonPrimary
                    className="col-span-2 md:col-span-1 lg:col-span-3 xl:col-span-2"
                    color="gray"
                    size="sm"
                    variant={'outlined'}
                    onClick={() => onReschedule && onReschedule(list)}
                  >
                    Ubah Jadwal
                  </ButtonPrimary>
                )}
                {showChangeCLientReportButton(list) && (
                  <ButtonPrimary
                    className="col-span-2 md:col-span-1 lg:col-span-3 xl:col-span-2"
                    color="gray"
                    size="sm"
                    variant={'outlined'}
                    onClick={() => onChangeCLientReport && onChangeCLientReport(list)}
                  >
                    Ubah Klien Report
                  </ButtonPrimary>
                )}
                {showRejectButton(list) && (
                  <ButtonPrimary
                    className="col-start-2 col-span-2 md:col-span-1 lg:col-span-3 xl:col-span-2 md:min-w-[143px]"
                    color="danger"
                    size="sm"
                    variant={'outlined'}
                    onClick={() => onReject && onReject(list)}
                  >
                    Tolak
                  </ButtonPrimary>
                )}
                {/* </div> */}
              </div>
            </AccordionItem>
          )
        })
      ) : (
        <NoDataFound />
      )}
    </Accordion>
  )
}
