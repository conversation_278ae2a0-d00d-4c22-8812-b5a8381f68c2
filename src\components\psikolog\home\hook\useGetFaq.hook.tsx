import { profileService } from '@/services/profile.service'
import { useQuery } from '@tanstack/react-query'

export const useGetFaq = (id: string) => {
  return useQuery({
    queryKey: ['MentalHealingFAQ', { id }],
    queryFn: () => {
      return profileService
        .getFaq(id)
        .then((response) => {
          return response
        })
        .catch((error) => {
          return null
        })
    },
  })
}
