import { AuthRole } from '@/store/auth/auth.action'
export const PhoneNumberRegExp = /^(\+[1-9]{1,4})([1-9]{1,15}$)/g

export const defaultTimezone = 'Asia/Jakarta'
export enum PaymentStatus {
  PENDING_PAYMENT = 'PENDING_PAYMENT',
  PAID_PAYMENT = 'PAID_PAYMENT',
  PAID = 'PAID',
  EXPIRED_PAYMENT = 'EXPIRED_PAYMENT',
  RESCHEDULE_BY_CLIENT = 'RESCHEDULE_BY_CLIENT',
  RESCHEDULE_BY_PSYCHOLOGIST = 'RESCHEDULE_BY_PSYCHOLOGIST',
  APPROVED = 'APPROVED',
  INPROGRESS = 'INPROGRESS',
  COMPLETED = 'COMPLETED',
  CANCELLED_BY_PSYCHOLOGIST = 'CANCELLED_BY_PSYCHOLOGIST',
  CANCELLED_BY_ADMIN = 'CANCELLED_BY_ADMIN',
  REJECTED_BY_PSYCHOLOGIST = 'REJECTED_BY_PSYCHOLOGIST',
}

export const InProgressStatusForAdmin = [PaymentStatus.INPROGRESS]
export const UpcomingStatusForAdmin = [PaymentStatus.APPROVED]
export const WaitConfirmationsStatusForAdmin = [
  PaymentStatus.PAID_PAYMENT,
  PaymentStatus.RESCHEDULE_BY_CLIENT,
  PaymentStatus.RESCHEDULE_BY_PSYCHOLOGIST,
]
export const WaitPaymentStatusForAdmin = [PaymentStatus.PENDING_PAYMENT]

export const UpcomingStatus = [PaymentStatus.APPROVED, PaymentStatus.INPROGRESS]
export const WaitConfirmationsStatus = [
  PaymentStatus.PAID_PAYMENT,
  PaymentStatus.RESCHEDULE_BY_CLIENT,
  PaymentStatus.RESCHEDULE_BY_PSYCHOLOGIST,
]
export const CompletedStatus = [PaymentStatus.COMPLETED]
export const CancelledStatus = [
  PaymentStatus.CANCELLED_BY_PSYCHOLOGIST,
  PaymentStatus.CANCELLED_BY_ADMIN,
  PaymentStatus.REJECTED_BY_PSYCHOLOGIST,
]
export const GlobalAllStatus = 'All'

export enum CounsellingMethod {
  ALL = GlobalAllStatus,
  CALL = 'Call',
  VIDEO = 'VideoCall',
  CHAT = 'Chat',
}

export enum CLientStatus {
  ALL = GlobalAllStatus,
  ACTIVE = 'Active',
  BLOCKED = 'Blocked',
}

export enum AdminStatus {
  ALL = GlobalAllStatus,
  ACTIVE = 'Active',
  INACTIVE = 'Nonactive',
}

export const SpecializationOptions = [
  { label: 'Adiksi', value: 'Adiksi' },
  { label: 'Akademik', value: 'Akademik' },
  { label: 'Anak & Remaja', value: 'Anak & Remaja' },
  { label: 'Berduka', value: 'Berduka' },
  { label: 'Bipolar', value: 'Bipolar' },
  { label: 'Burnout', value: 'Burnout' },
  { label: 'Depresi', value: 'Depresi' },
  { label: 'Emansipasi', value: 'Emansipasi' },
  { label: 'Gangguan Makan', value: 'Gangguan Makan' },
  { label: 'Gangguan Perilaku', value: 'Gangguan Perilaku' },
  { label: 'Industri & Organisasi', value: 'Industri & Organisasi' },
  { label: 'Insecurity', value: 'Insecurity' },
  { label: 'Karir', value: 'Karir' },
  { label: 'Kecemasan', value: 'Kecemasan' },
  { label: 'Kekerasan', value: 'Kekerasan' },
  { label: 'Keluarga', value: 'Keluarga' },
  { label: 'Kematangan Sekolah', value: 'Kematangan Sekolah' },
  { label: 'Kepercayaan Diri', value: 'Kepercayaan Diri' },
  { label: 'Kepribadian', value: 'Kepribadian' },
  { label: 'Minat & Bakat', value: 'Minat & Bakat' },
  { label: 'Mindfulness', value: 'Mindfulness' },
  { label: 'Mood & Emosi', value: 'Mood & Emosi' },
  { label: 'Motivasi', value: 'Motivasi' },
  { label: 'OCD', value: 'OCD' },
  { label: 'Overthinking', value: 'Overthinking' },
  { label: 'Parenting', value: 'Parenting' },
  { label: 'Pasangan', value: 'Pasangan' },
  { label: 'Pengembangan Kompetensi', value: 'Pengembangan Kompetensi' },
  { label: 'Percintaan', value: 'Percintaan' },
  { label: 'Perilaku', value: 'Perilaku' },
  { label: 'Perkembangan Anak', value: 'Perkembangan Anak' },
  { label: 'Pernikahan', value: 'Pernikahan' },
  { label: 'Phobia', value: 'Phobia' },
  { label: 'Produktivitas', value: 'Produktivitas' },
  { label: 'Rekrutmen', value: 'Rekrutmen' },
  { label: 'Relasi Sosial', value: 'Relasi Sosial' },
  { label: 'Seksualitas', value: 'Seksualitas' },
  { label: 'Self-Development', value: 'Self-Development' },
  { label: 'Skizofrenia', value: 'Skizofrenia' },
  { label: 'Sosio-emosional', value: 'Sosio-emosional' },
  { label: 'Stres', value: 'Stres' },
  { label: 'Suicidal', value: 'Suicidal' },
  { label: 'Training Need Analysis', value: 'Training Need Analysis' },
  { label: 'Trauma', value: 'Trauma' },
]

export const OtherReasonCancelationOptions = 'Alasan Lainnya'
export const ReasonCancelationOptions = [
  {
    value: 'Klien tidak kunjung datang setelah 20 menit menunggu.',
    label: 'Klien tidak kunjung datang setelah 20 menit menunggu.',
  },
  {
    value: 'Klien meminta untuk sesi dibatalkan.',
    label: 'Klien meminta untuk sesi dibatalkan.',
  },
  { value: OtherReasonCancelationOptions, label: 'Alasan Lainnya' },
]

export const AdminRoleOptions = [
  { value: AuthRole.SUPERADMIN, label: 'Super Admin' },
  { value: AuthRole.ADMIN, label: 'Admin' },
]

export const RatingOptions = [
  { value: GlobalAllStatus, label: 'Semua Rating' },
  { value: [1], label: '1 Bintang' },
  { value: [2], label: '2 Bintang' },
  { value: [3], label: '3 Bintang' },
  { value: [4], label: '4 Bintang' },
  { value: [5], label: '5 Bintang' },
]

export const ServiceOptions = [
  { value: 'Konseling Online', label: 'Konseling Online' },
  { value: 'Konseling Offline', label: 'Konseling Offline' },
  { value: 'Home Visit', label: 'Home Visit' },
]

export const LevelEducationOptions = [
  { value: 'S1', label: 'Strata 1' },
  { value: 'S2', label: 'Strata 2' },
  { value: 'S3', label: 'Strata 3' },
]

export enum PsychologistStatus {
  ALL = GlobalAllStatus,
  ACTIVE = 'Active',
  INACTIVE = 'Nonactive',
}

export enum CounsellingTab {
  UPCOMING = 'UPCOMING',
  INPROGRESS = 'INPROGRESS',
  WAITING = 'WAITING',
  WAITING_PAYMENT = 'WAITING_PAYMENT',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
  ALL = 'ALL',
}
