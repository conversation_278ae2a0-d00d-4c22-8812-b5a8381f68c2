import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent } from '@/components/ui/DropdownMenu'
import { H4 } from '../_common/ui'
import { useRouter } from 'next/navigation'
import { IIcons, SVGIcons } from '../_common/icon'
import { NotificationItem } from './NotificationItem'
import { useGetNotification } from '@/hooks/useGetNotification.hook'

export const Notification = ({ onShowAll }: { onShowAll: string }) => {
  const router = useRouter()
  const { data } = useGetNotification()
  const isUnread = data?.some((val: any) => !val.isRead)

  return (
    <>
      <div className="relative cursor-pointer flex lg:hidden" onClick={() => router.push(onShowAll)}>
        <SVGIcons name={IIcons.Bell} className="w-6" />
        {data?.length > 0 && (
          <span className="h-2 w-2 rounded-full bg-emerald-500 absolute ring-1 ring-white top-[2px] right-[2px]"></span>
        )}
      </div>
      <div className="relative hidden lg:flex">
        <DropdownMenu modal={false}>
          <DropdownMenuTrigger className="" asChild>
            <div className="relative">
              <SVGIcons name={IIcons.Bell} className="w-6" />
              {data?.length > 0 && isUnread && (
                <span className="h-2 w-2 rounded-full bg-emerald-500 absolute ring-1 ring-white top-[2px] right-[2px]"></span>
              )}
            </div>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="grid gap-y-4 max-w-[420px] p-0">
            <ul>
              <li className="flex flex-nowrap flex-row px-6 py-4">
                <H4 bold>Notifikasi</H4>
              </li>
              {!data?.length ? (
                <div className="flex justify-center p-2 text-gray-200">Tidak ada notifikasi baru</div>
              ) : (
                data
                  ?.filter((val: any, id: number) => id < 5)
                  .map((item: any, idx: number) => {
                    return <NotificationItem key={idx} {...item} />
                  })
              )}
              <li
                className="flex flex-nowrap flex-row px-6 py-4 bg-main-50 cursor-pointer"
                onClick={() => router.push(onShowAll)}
              >
                <span className="text-body-lg font-bold text-main-100 ml-4">Lihat Semua Notifikasi</span>
              </li>
            </ul>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </>
  )
}
