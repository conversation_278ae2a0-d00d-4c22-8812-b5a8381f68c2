'use client'

import AppInput from '@/components/_common/input/Input'
import { RadioInput } from '@/components/_common/RadioInput/RadioInput'
import { GenderOptions } from '@/constans/onboardOptions'
import { UseFormGetValues, UseFormRegister, UseFormReturn, UseFormSetValue } from 'react-hook-form'

export default function formAddClientStepOne({
  pageNumber,
  register,
  getValues,
  setValue,
  errors,
}: {
  pageNumber: number
  register: UseFormRegister<any>
  getValues: UseFormGetValues<any>
  setValue: UseFormSetValue<any>
  errors: UseFormReturn['formState']['errors']
  onSubmit: () => void
}) {
  return (
    <>
      <div className="flex flex-col gap-[6px] mb-4">
        <h2 className="text-[26px] font-bold">Informasi Akun</h2>
        <span className="text-[12px] text-[#737373]">Step {pageNumber} dari 3</span>
      </div>

      <AppInput
        {...register('fullName')}
        className="pt-0"
        label="Nama Lengkap*"
        type="text"
        name="fullName"
        errorMsg={!!errors.fullName ? String(errors.fullName.message) : undefined}
      />

      <AppInput
        {...register('nickname')}
        className="pt-0"
        label="Nama Panggilan*"
        type="text"
        name="nickname"
        errorMsg={!!errors.nickname ? String(errors.nickname.message) : undefined}
      />
      <AppInput
        {...register('email')}
        className="pt-0"
        label="Email*"
        type="text"
        name="email"
        errorMsg={!!errors.email ? String(errors.email.message) : undefined}
      />
      <AppInput
        className="pt-0"
        label="No. Handphone*"
        type="text"
        {...register('phoneNumber')}
        name="phoneNumber"
        errorMsg={!!errors.phoneNumber ? String(errors.phoneNumber.message) : undefined}
      />
      <RadioInput
        options={GenderOptions}
        name={'gender'}
        value={getValues('gender')!}
        label="Jenis Kelamin*"
        errorMsg={!!errors.gender ? String(errors.gender.message) : undefined}
        onChange={(val) => {
          setValue('gender', val, { shouldValidate: true })
        }}
      />
      <AppInput
        className="pt-0"
        label="Buat Password*"
        type="password"
        {...register('password')}
        name="password"
        errorMsg={!!errors.password ? String(errors.password.message) : undefined}
      />
    </>
  )
}
