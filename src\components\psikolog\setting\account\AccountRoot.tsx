'use client'
import ButtonPrimary from '@/components/_common/ButtonPrimary'
import AppInput from '@/components/_common/input/Input'
import { useForm } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import * as yup from 'yup'
import { useToast } from '@/components/ui/use-toast'
import { useEffect, useState } from 'react'
import { BankAccount, PsychologistProfile } from '@/interfaces/profile-service'
import { profileService } from '@/services/profile.service'
import { FormSetting } from '../FormSetting'
import { AccountInformationType } from '../../income/AccountInformationForm'

const validationSchema = yup.object().shape({
  bankAccount: yup.string().nullable(),
  bankAccountName: yup.string().nullable(),
  bankName: yup.string().nullable(),
})

const AccountRoot = ({ bankAccount: account }: PsychologistProfile) => {
  const { toast } = useToast()
  const [isLoadingForm, setIsLoadingForm] = useState<boolean>(false)
  const [fieldEdit, setFieldEdit] = useState<string | null>(null)

  const {
    register,
    handleSubmit,
    trigger,
    getValues,
    setValue,
    reset,
    resetField,
    formState: { errors, isLoading, isSubmitting },
  } = useForm({
    resolver: yupResolver(validationSchema),
    defaultValues: {
      bankAccount: '',
      bankAccountName: '',
      bankName: '',
    },
  })

  useEffect(() => {
    reset({
      bankAccount: account?.[0]?.bankAccount ?? '',
      bankAccountName: account?.[0]?.bankAccountName ?? '',
      bankName: account?.[0]?.bankName ?? '',
    })
  }, [account, reset])

  async function onSubmit(data: AccountInformationType) {
    try {
      const formData = new FormData()
      for (let val in data) {
        formData.append(val, data?.[val as keyof AccountInformationType] ?? '')
      }
      await profileService.updatePsychologistProfile(formData)
      toast({
        variant: 'success',
        title: 'Berhasil memperbaharui data rekening',
      })
      setIsLoadingForm(false)
      setFieldEdit(null)
    } catch (error) {
      toast({
        variant: 'danger',
        title: 'Perbaharui data rekening gagal',
      })
      setIsLoadingForm(false)
      setFieldEdit(null)
    }
  }

  return (
    <div className="grid gap-y-6">
      <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
        <div className="col-span-2">
          <FormSetting
            viewComponent={
              <>
                <p className="text-gray-400 text-body-lg font-medium">{getValues('bankName') ?? '-'}</p>
                <p className="text-gray-400 text-body-lg font-medium">{getValues('bankAccount') ?? '-'}</p>
                <p className="text-gray-400 text-body-lg font-medium">
                  a/n {getValues('bankAccountName') ?? '-'}
                </p>
              </>
            }
            editComponent={
              <div className="grid gap-2">
                <AppInput
                  {...register('bankName')}
                  className="pt-0"
                  type="text"
                  value={getValues('bankName')!}
                  onChange={(val) => {
                    setValue('bankName', val.target.value, { shouldValidate: true })
                  }}
                  name="bankName"
                  label="Nama Bank"
                  errorMsg={!!errors.bankName ? String(errors.bankName.message) : undefined}
                  placeholder="Contoh: BCA (Bank Central Asia)"
                />
                <AppInput
                  {...register('bankAccount')}
                  className="pt-0"
                  type="text"
                  value={getValues('bankAccount')!}
                  onChange={(val) => {
                    setValue('bankAccount', val.target.value, { shouldValidate: true })
                  }}
                  name="bankAccount"
                  label="Nomor Rekening"
                  errorMsg={!!errors.bankAccount ? String(errors.bankAccount.message) : undefined}
                  placeholder="Masukkan Nomor Rekening"
                />
                <AppInput
                  {...register('bankAccountName')}
                  className="pt-0"
                  type="text"
                  value={getValues('bankAccountName')!}
                  onChange={(val) => {
                    setValue('bankAccountName', val.target.value, { shouldValidate: true })
                  }}
                  name="bankAccountName"
                  label="Atas Nama"
                  errorMsg={!!errors.bankAccountName ? String(errors.bankAccountName.message) : undefined}
                  placeholder="Masukkan Nama Pemilik Rekening"
                />
              </div>
            }
            label={fieldEdit === 'accountInformation' ? 'Informasi Rekening' : 'Akun Bank'}
            isEdit={fieldEdit === 'accountInformation'}
            isLoading={isLoadingForm}
            onEditButton={() => setFieldEdit('accountInformation')}
            onCancel={() => {
              setFieldEdit(null)
              resetField('bankName')
              resetField('bankAccount')
              resetField('bankAccountName')
            }}
            onSubmit={() => handleSubmit(onSubmit)()}
          />
        </div>
      </div>
    </div>
  )
}

export default AccountRoot
