import ButtonPrimary from '@/components/_common/ButtonPrimary'
import AppInput from '@/components/_common/input/Input'
import { useToast } from '@/components/ui/use-toast'
import { BankAccount } from '@/interfaces/profile-service'
import { profileService } from '@/services/profile.service'
import { yupResolver } from '@hookform/resolvers/yup'
import { useForm } from 'react-hook-form'
import * as yup from 'yup'

const validationSchema = yup.object().shape({
  bankAccount: yup.string().notRequired(),
  bankAccountName: yup.string().notRequired(),
  bankName: yup.string().notRequired(),
})

export type AccountInformationType = yup.InferType<typeof validationSchema>

export const AccountInformationForm = ({
  accountInfo,
  onClose,
}: {
  accountInfo: BankAccount | null
  onClose: () => void
}) => {
  const { toast } = useToast()
  const {
    register,
    handleSubmit,
    setValue,
    reset,
    formState: { errors, isLoading, isSubmitting },
  } = useForm({
    resolver: yupResolver(validationSchema),
    defaultValues: {
      bankAccount: accountInfo?.bankAccount,
      bankAccountName: accountInfo?.bankAccountName,
      bankName: accountInfo?.bankName,
    },
  })

  async function onSubmit(data: AccountInformationType) {
    try {
      const formData = new FormData()
      for (let val in data) {
        formData.append(val, data?.[val as keyof AccountInformationType] ?? '')
      }
      await profileService.updatePsychologistProfile(formData)
      toast({
        variant: 'success',
        title: 'Berhasil memperbaharui data rekening',
      })
      setTimeout(() => {
        onClose()
      }, 1000)
    } catch (error) {
      toast({
        variant: 'danger',
        title: 'Data rekening gagal diperbaharui',
      })
    }
  }
  return (
    <div className="grid gap-y-4">
      <AppInput
        {...register('bankName')}
        name="bankName"
        label="Nama Bank"
        type="text"
        onChange={(e) => setValue('bankName', e.target.value, { shouldValidate: true })}
      />
      <AppInput
        {...register('bankAccount')}
        name="bankAccount"
        label="Nomor Rekening"
        type="text"
        onChange={(e) => setValue('bankAccount', e.target.value, { shouldValidate: true })}
      />
      <AppInput
        {...register('bankAccountName')}
        name="bankAccountName"
        label="Atas Nama"
        type="text"
        onChange={(e) => setValue('bankAccountName', e.target.value, { shouldValidate: true })}
      />
      <div className="flex justify-end gap-4">
        <ButtonPrimary
          className="min-w-[140px]"
          variant="outlined"
          size="xs"
          color="gray"
          onClick={() => onClose()}
        >
          Batal
        </ButtonPrimary>
        <ButtonPrimary
          className="min-w-[140px]"
          variant="contained"
          size="xs"
          onClick={() => handleSubmit(onSubmit)()}
        >
          Simpan
        </ButtonPrimary>
      </div>
    </div>
  )
}
