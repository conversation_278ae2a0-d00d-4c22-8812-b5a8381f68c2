'use client'
import AppInput from '@/components/_common/input/Input'
import { yupResolver } from '@hookform/resolvers/yup'
import { useForm } from 'react-hook-form'
import * as yup from 'yup'
import Translation from '@/constans/Translation'
import ButtonPrimary from '@/components/_common/ButtonPrimary'
import { contactService } from '@/services/contact.service'
import { useToast } from '@/components/ui/use-toast'
import { useState } from 'react'

const validationSchema = yup.object().shape({
  name: yup.string().required(Translation.RequiredName),
  email: yup.string().required(Translation.RequiredEmail).email(Translation.ValidEmail),
  phoneNo: yup.string().required(Translation.RequiredPhoneNumber),
  institution: yup.string().required(Translation.RequiredInstitution),
  jobTitle: yup.string().required(Translation.RequiredJobTitle),
  sourceInfo: yup.string().required(Translation.RequiredSourceInfo),
  info: yup.string(),
  numberOfEmployee: yup.string(),
})

type AssessmentForm = {
  name: string
  email: string
  phoneNo: string
  institution: string
  jobTitle: string
  sourceInfo: string
  info?: string
  numberOfEmployee?: string
}

const SocialMediaOption = [
  'Search Engine (Contoh: Google)',
  'Instagram',
  'Tiktok',
  'Youtube',
  'Facebook',
  'LinkedIn',
  'Teman atau Kolega',
  'Lainnya',
]
const NumberEmployeeoption = [
  '0 - 50',
  '50 - 100',
  '101 - 200',
  '201 - 500',
  '501 - 1.000',
  '1001 - 5.000',
  '5.001 - 10.000',
  'Lebih dari 10.000',
]
export const AssessmentForm = ({ isCompany }: { isCompany?: boolean }) => {
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const { toast } = useToast()
  const {
    register,
    handleSubmit,
    setValue,
    getValues,
    reset,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(validationSchema),
    defaultValues: {
      name: '',
      email: '',
      phoneNo: '',
      institution: '',
      jobTitle: '',
      sourceInfo: '',
      info: '',
      numberOfEmployee: '',
    },
  })

  async function onSubmit(data: AssessmentForm) {
    setIsLoading(true)
    try {
      if (isCompany) {
        const payload = {
          fullName: data.name || '',
          email: data.email || '',
          phoneNumber: data.phoneNo || '',
          companyName: data.institution || '',
          companyTotalEmployee: data.numberOfEmployee || '',
          roleAndPosition: data.jobTitle || '',
          knownFrom: data.sourceInfo || '',
          message: data.info || '',
        }
        contactService
          .postAssestmentBusiness(payload)
          .then((resp) => {
            if (resp) {
              toast({
                variant: 'success',
                title: 'Submit data berhasil. Terimakasih',
              })
              reset()
            }
          })
          .catch((error) => {
            if (error?.response?.data?.message?.length) {
              error?.response?.data?.message.map((err: string) => {
                toast({
                  variant: 'danger',
                  title: err,
                })
              })
            }
          })
          .finally(() => {
            setIsLoading(false)
          })
      } else {
        const payload = {
          fullName: data.name || '',
          email: data.email || '',
          phoneNumber: data.phoneNo || '',
          schoolName: data.institution || '',
          roleAndPosition: data.jobTitle || '',
          knownFrom: data.sourceInfo || '',
          message: data.info ?? '',
        }
        contactService
          .postAssestmentEducation(payload)
          .then((resp) => {
            if (resp) {
              toast({
                variant: 'success',
                title: 'Submit data berhasil. Terimakasih',
              })
              reset()
            }
          })
          .catch((error) => {
            if (error?.response?.data?.message?.length) {
              error?.response?.data?.message.map((err: string) => {
                toast({
                  variant: 'danger',
                  title: err,
                })
              })
            }
          })
          .finally(() => {
            setIsLoading(false)
          })
      }
    } catch (error) {
      console.log(error)
      setIsLoading(false)
    } finally {
      setIsLoading(false)
    }
  }
  return (
    <div id="assestment-form" className="flex flex-col gap-y-10 items-center">
      <div className="flex flex-col gap-y-4 items-center">
        <span className="text-subheading-md text-[38px] md:leading-[42px] font-bold text-gray-400 text-center">
          Dapatkan Assessment Gratis
        </span>
        <span className="text-body-lg md:text-body-lg font-medium text-gray-300 text-center">
          <span className="text-main-100 text-center">4 Years MentalHealing.id Anniversary Promo!</span>{' '}
          {isCompany
            ? 'Dapatkan layanan gratis untuk bisnis terpilih.'
            : 'Dapatkan layanan gratis untuk institusi pendidikan terpilih.'}
        </span>
      </div>
      <div className="grid gap-y-6 md:gap-y-10 max-w-[548px] px-4 lg:px-0">
        <AppInput
          {...register('name')}
          errorMsg={!!errors.name ? errors.name.message : undefined}
          className="pt-0"
          label="Nama Lengkap"
          type="text"
          name="name"
        />
        <AppInput
          {...register('email')}
          errorMsg={!!errors.email ? errors.email.message : undefined}
          className="pt-0"
          label="Email"
          type="email"
          name="email"
          note={`Akan diprioritaskan jika menggunakan email ${isCompany ? 'perusahaan' : 'sekolah'}.`}
        />
        <AppInput
          {...register('phoneNo')}
          errorMsg={!!errors.phoneNo ? errors.phoneNo.message : undefined}
          className="pt-0"
          label="No. WhatsApp"
          type="text"
          name="phoneNo"
          note={`Tulis beserta kode negara, contoh: <strong>+628123456789</strong>`}
        />
        <AppInput
          {...register('institution')}
          errorMsg={!!errors.jobTitle ? errors.jobTitle.message : undefined}
          className="pt-0"
          label={isCompany ? 'Nama Perusahaan' : 'Nama Sekolah'}
          type="text"
          name="institution"
        />
        {isCompany && (
          <AppInput
            {...register('numberOfEmployee')}
            options={NumberEmployeeoption}
            onChange={(val) => setValue('numberOfEmployee', val.target.value, { shouldValidate: true })}
            value={getValues('numberOfEmployee') ? String(getValues('numberOfEmployee')) : ''}
            className="pt-0"
            label="Jumlah Karyawan"
            placeholder="salah satu"
            type="select"
            name="numberOfEmployee"
            errorMsg={!!errors.numberOfEmployee ? String(errors.numberOfEmployee.message) : undefined}
          />
        )}
        <AppInput
          {...register('jobTitle')}
          errorMsg={!!errors.jobTitle ? errors.jobTitle.message : undefined}
          className="pt-0"
          label={'Posisi dan Jabatan Anda'}
          type="text"
          name="jobTitle"
        />
        <div className="grid gap-y-1">
          <AppInput
            {...register('sourceInfo')}
            options={SocialMediaOption}
            onChange={(val) => setValue('sourceInfo', val.target.value, { shouldValidate: true })}
            value={getValues('sourceInfo') ? String(getValues('sourceInfo')) : ''}
            className="pt-0"
            label="Dari mana Anda mengetahui MentalHealing.id"
            placeholder="salah satu"
            type="select"
            name="sourceInfo"
            errorMsg={!!errors.sourceInfo ? String(errors.sourceInfo.message) : undefined}
          />
          {getValues('sourceInfo') === 'Lainnya' && (
            <AppInput
              {...register('info')}
              errorMsg={!!errors.info ? errors.info.message : undefined}
              className="pt-0"
              type="text"
              name="info"
              placeholder="Silakan tulis dari mana Anda mengetahui MentalHealing.id"
            />
          )}
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <span className="text-body-lg text-wrap">Setelah submit, kami akan menghubungi Anda.</span>
          <ButtonPrimary
            className="rounded-full"
            disabled={isLoading}
            variant="contained"
            size="sm"
            onClick={() => handleSubmit(onSubmit)()}
          >
            Submit
          </ButtonPrimary>
        </div>
      </div>
    </div>
  )
}
