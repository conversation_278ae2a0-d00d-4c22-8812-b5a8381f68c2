'use client'
import { cn } from '@/lib/utils'
import { useState } from 'react'

export const SpecializationList = ({
  list,
  showItem,
  className,
}: {
  list: string[]
  showItem: number
  className?: string
}) => {
  const [showAll, setShowAll] = useState<boolean>(false)
  const isShowMore = list.length > showItem
  const showMoreLabel = !showAll ? ` +${list.length - showItem} lainnya` : 'Tampilkan sebagian'

  const handleClickShowMore = () => {
    setShowAll((prev) => !prev)
  }
  return (
    <div className="flex flex-wrap gap-x-1">
      {list.map((item, id) => {
        let label = (id < showItem || showAll) && id != list.length - 1 ? item + ',' : item
        if (showAll) {
          return (
            <span className={className} key={item}>
              {label}
            </span>
          )
        } else {
          if (id <= showItem) {
            return (
              <span className={className} key={item}>
                {label}
              </span>
            )
          }
        }
      })}
      {isShowMore && (
        <span
          className={`cursor-pointer ${className}`}
          // onClick={() => handleClickShowMore()}
        >
          {showMoreLabel}
        </span>
      )}
    </div>
  )
}
