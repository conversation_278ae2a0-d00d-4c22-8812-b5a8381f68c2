'use client'
import { createContext, useContext, ReactNode, useState } from 'react'

interface BreakdownAvailability {
  durationInMinute: number
  price: number
}

interface Psychologist {
  id: string
  fullName: string
  profilePhoto?: string
  nickname?: string
  specialization?: string[]
  breakdownAvailability?: BreakdownAvailability[]
}

interface BookingState {
  psychologistId: string
  psychologistName: string
  psychologistImage: string
  specializations: string
  selectedDate: string
  selectedTime: string
  formattedDate: string
  rawSchedule: string
  duration: number // Ensuring this is a number type
  method: 'Call' | 'VideoCall'
  location: 'Online' | 'Offline'
  price: number
  discount: number
  voucherPromo: number
  totalAfterDiscount: number
  finalPrice: number
}

interface BookingContextType {
  bookingState: BookingState | null
  setBookingDetails: (details: BookingState) => void
  clearBookingDetails: () => void
}

const initialBookingState: BookingState = {
  psychologistId: '',
  psychologistName: '',
  psychologistImage: '',
  specializations: '',
  selectedDate: '',
  selectedTime: '',
  formattedDate: '',
  duration: 60,
  rawSchedule: '',
  method: 'VideoCall',
  location: 'Online',
  price: 0,
  discount: 0,
  voucherPromo: 0,
  totalAfterDiscount: 0,
  finalPrice: 0,
}

const BookingContext = createContext<BookingContextType | undefined>(undefined)

export function BookingProvider({ children }: { children: ReactNode }) {
  const [bookingState, setBookingState] = useState<BookingState | null>(null)

  const setBookingDetails = (details: BookingState) => {
    // Ensure duration is always a number
    const formattedDetails = {
      ...details,
      duration: typeof details.duration === 'string' ? parseInt(details.duration, 10) : details.duration,
    }
    setBookingState(formattedDetails)
  }

  const clearBookingDetails = () => {
    setBookingState(null)
  }

  return (
    <BookingContext.Provider value={{ bookingState, setBookingDetails, clearBookingDetails }}>
      {children}
    </BookingContext.Provider>
  )
}

export function useBooking() {
  const context = useContext(BookingContext)
  if (context === undefined) {
    throw new Error('useBooking must be used within a BookingProvider')
  }
  return context
}
