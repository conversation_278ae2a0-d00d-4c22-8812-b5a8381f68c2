import ButtonPrimary from '@/components/_common/ButtonPrimary'
import { IIcons, SVGIcons } from '@/components/_common/icon'
import { AppModal } from '@/components/_common/Modal/AppModal'

// Available voucher interface with display properties
interface AvailableVoucher {
  id: string
  title: string
  description: string
  discountValue: number
  discountType: 'FIXED' | 'PERCENTAGE'
  expiryDate: string
}

interface VoucherModalProps {
  isOpen: boolean
  onClose: () => void
  voucherCode: string
  setVoucherCode: (code: string) => void
  voucherError: string
  selectedVoucher: AvailableVoucher | null
  availableVouchers: AvailableVoucher[]
  isLoading: boolean
  isLoadingVouchers: boolean
  displayData: any
  onApplyVoucher: () => void
  onSelectVoucher: (voucher: AvailableVoucher) => void
  onApplySelectedVoucher: () => void
  onClearSelectedVoucher: () => void
}

const VoucherModal = ({
  isOpen,
  onClose,
  voucherCode,
  setVoucherCode,
  voucherError,
  selectedVoucher,
  availableVouchers,
  isLoading,
  isLoadingVouchers,
  onApplyVoucher,
  onSelectVoucher,
  onApplySelectedVoucher,
  onClearSelectedVoucher,
}: VoucherModalProps) => {
  return (
    <AppModal open={isOpen} onClose={onClose} title="Lebih hemat pakai voucher">
      <div className="p-4">
        <div className="relative mb-4">
          <input
            type="text"
            className={`w-full border ${voucherError ? 'border-red-500' : 'border-gray-300'} rounded-lg p-4 pr-28`}
            placeholder="Masukkan kode voucher"
            value={voucherCode}
            onChange={(e) => setVoucherCode(e.target.value.toUpperCase())}
          />
          <ButtonPrimary
            variant="contained"
            className={`absolute right-2 top-1/2 transform -translate-y-1/2 ${
              isLoading ? 'bg-gray-300 text-gray-500' : ''
            } px-4 py-2 text-sm rounded-lg`}
            onClick={onApplyVoucher}
            disabled={isLoading}
          >
            {isLoading ? 'Memeriksa...' : 'Pakai'}
          </ButtonPrimary>
        </div>
        {voucherError && <p className="text-red-500 -mt-3 mb-3 text-sm">{voucherError}</p>}

        {/* Selected voucher indicator */}
        {selectedVoucher && (
          <div className="mb-4 p-3 bg-main-50 rounded-lg border border-main-100/50 flex items-center justify-between">
            <div className="flex items-center">
              <div className="flex-shrink-0 text-main-100 mr-3">
                <SVGIcons name={IIcons.Repeat} className="h-6 w-6" />
              </div>
              <div>
                <p className="font-semibold">{selectedVoucher.title}</p>
                <p className="text-gray-600 text-sm">ID: {selectedVoucher.id}</p>
              </div>
            </div>
            <button className="text-gray-500" onClick={onClearSelectedVoucher}>
              <SVGIcons name={IIcons.Close} className="h-5 w-5" />
            </button>
          </div>
        )}

        {/* Available vouchers section */}
        {availableVouchers.length > 0 && (
          <>
            <h3 className="font-semibold mb-2 mt-4">Voucher Tersedia</h3>
            <div className="space-y-3 max-h-60 overflow-y-auto">
              {isLoadingVouchers ? (
                <div className="text-center py-4">
                  <p>Memuat voucher...</p>
                </div>
              ) : (
                availableVouchers.map((voucher, index) => (
                  <div
                    key={index}
                    className={`border rounded-lg p-4 flex items-center cursor-pointer ${
                      selectedVoucher?.id === voucher.id ? 'border-main-100 bg-main-50' : ''
                    }`}
                    onClick={() => onSelectVoucher(voucher)}
                  >
                    <div className="flex-shrink-0 mr-4 bg-blue-50 p-3 rounded-lg">
                      <SVGIcons name={IIcons.Document} className="text-main-100 h-6 w-6" />
                    </div>
                    <div className="flex-1">
                      <h3 className="font-semibold">{voucher.title}</h3>
                      <p className="text-gray-500 text-sm">{voucher.description}</p>
                      <p className="text-gray-500 text-xs">Berlaku hingga {voucher.expiryDate}</p>
                    </div>
                    {selectedVoucher?.id === voucher.id && (
                      <div className="flex-shrink-0 text-main-100">
                        <SVGIcons name={IIcons.Repeat} className="h-5 w-5" />
                      </div>
                    )}
                  </div>
                ))
              )}
            </div>
          </>
        )}

        <ButtonPrimary
          variant="contained"
          className="w-full mt-4 py-4"
          onClick={onApplySelectedVoucher}
          disabled={!selectedVoucher || isLoading}
        >
          {isLoading ? 'Memproses...' : 'Terapkan Voucher'}
        </ButtonPrimary>
      </div>
    </AppModal>
  )
}

export default VoucherModal
