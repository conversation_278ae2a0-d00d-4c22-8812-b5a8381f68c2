import { createSlice, PayloadAction } from '@reduxjs/toolkit'

type EndUserProps = {
  filterPsikolog: string | null
}

const endUserSlice = createSlice({
  name: 'enduser',
  initialState: {
    filterPsikolog: '',
  } as EndUserProps,
  reducers: {
    setFilterPsikolog(state, action: PayloadAction<EndUserProps['filterPsikolog']>) {
      state.filterPsikolog = action.payload
    },
    resetFilter(state) {
      state.filterPsikolog = null
    },
  },
})

export const { setFilterPsikolog, resetFilter } = endUserSlice.actions
export default endUserSlice.reducer
