'use client'
import { IIcons, SVGIcons } from '@/components/_common/icon'
import { useGetCounselingCounter } from '../hook/useGetCounselingCounter.hook'
import Link from 'next/link'
import { Routes } from '@/constans/routes'
import { useRouter } from 'next/navigation'
import { DividerVerticalIcon } from '@radix-ui/react-icons'

export default function CounselingCounterStatus() {
  const router = useRouter()
  const { data, isLoading, isPending } = useGetCounselingCounter()

  const jadwalKonselings = [
    {
      id: 1,
      jumlah: data?.upcoming ?? 0,
      title: 'Akan Datang',
      style: 'py-[12px] pl-[0px] lg:pl[24px] flex items-center justify-center',
      link: `${Routes.PsychologistCounseling}?category=upcoming`,
    },
    {
      id: 2,
      jumlah: data?.waiting ?? 0,
      title: 'Menunggu Konfirmasi',
      style: 'py-[12px] pl-[0px] lg:pl[24px] flex items-center justify-center',
      link: `${Routes.PsychologistCounseling}?category=wait-confirmation`,
    },
    {
      id: 3,
      jumlah: data?.completed ?? 0,
      title: 'Selesai',
      style: 'hidden',
      link: `${Routes.PsychologistCounseling}?category=completed`,
    },
  ]
  return (
    <>
      {/* tab ~ dekstop */}
      <div className="hidden md:flex justify-between items-center py-[34px] px-[24px] bg-white border border-[#EBEBEB] rounded-xl">
        <div className="flex items-center justify-normal md:justify-between gap-0 md:gap-4">
          <p className="font-bold text-[16px] md:text-[18px] text-[#242424]">Jadwal Konseling</p>
          <Link href={Routes.PsychologistCounseling}>
            <SVGIcons className="" name={IIcons.ArrowRight} />
          </Link>
        </div>
        {jadwalKonselings.map((jadwalKonseling, id) => (
          <>
            <div
              key={jadwalKonseling.id}
              className="flex items-center justify-center cursor-pointer"
              onClick={() => router.push(jadwalKonseling.link)}
            >
              <div className="flex flex-col items-center justify-center px-[10px] md:px-0">
                <p
                  className={`text-[14px] md:text-[16px] font-bold text-center ${jadwalKonseling.id === 2 ? 'text-[#E42B3B]' : 'text-[#222222]'}`}
                >
                  {jadwalKonseling.jumlah}
                </p>
                <p className="text-[12px] md:text-[14px] text-center">{jadwalKonseling.title}</p>
              </div>
            </div>
            {id !== jadwalKonselings?.length - 1 && <DividerVerticalIcon className="text-gray-100" />}
          </>
        ))}
      </div>
      {/* mobile */}
      <div className="md:hidden flex flex-col py-[34px] px-[24px] bg-white border border-[#EBEBEB] rounded-xl gap-10">
        <div className="flex justify-between gap-0 md:gap-4">
          <p className="font-bold text-[16px] md:text-[18px] text-[#242424]">Jadwal Konseling</p>
          <SVGIcons className="" name={IIcons.ArrowRight} />
        </div>
        <div className="flex items-center justify-center gap-4">
          {jadwalKonselings.map((jadwalKonseling, id) => (
            <>
              <div
                key={jadwalKonseling.id}
                className="flex items-center cursor-pointer"
                onClick={() => router.push(jadwalKonseling.link)}
              >
                <div className="flex flex-col items-center justify-center">
                  <p
                    className={`text-[14px] md:text-[16px] font-bold text-center ${jadwalKonseling.id === 2 ? 'text-[#E42B3B]' : 'text-[#222222]'}`}
                  >
                    {jadwalKonseling.jumlah}
                  </p>
                  <p className="text-[12px] md:text-[14px] text-center">{jadwalKonseling.title}</p>
                </div>
              </div>
              {id !== jadwalKonselings?.length - 1 && <DividerVerticalIcon className="text-gray-100" />}
            </>
          ))}
        </div>
      </div>
    </>
  )
}
