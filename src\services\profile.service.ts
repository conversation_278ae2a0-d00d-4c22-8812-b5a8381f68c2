import { config } from '@/constans/config'
import { PsychologistProfile } from '@/interfaces/profile-service'
import { httpRequest } from '@/utils/network'

type NotificationPayload = {
  NOTIFICATION_COUNSELING_STATUS?: boolean
  NOTIFICATION_NEWS_UPDATE?: boolean
}

export class ProfileService {
  async getAdminProfile() {
    return await httpRequest({
      method: 'get',
      url: `${config?.apiBaseUrl}api/admin/profile`,
    })
  }

  async getPsychologistProfile() {
    return (await httpRequest({
      method: 'get',
      url: `${config?.apiBaseUrl}api/psychologists/profile`,
    })) as PsychologistProfile
  }

  async updatePsychologistProfile(payload: FormData) {
    return await httpRequest({
      method: 'put',
      url: `${config?.apiBaseUrl}api/psychologists/profile`,
      data: payload,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  }

  async updateAdminProfile(payload: FormData) {
    return await httpRequest({
      method: 'patch',
      url: `${config?.apiBaseUrl}api/admin/profile`,
      data: payload,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  }

  async updateTimezone(timezone: string) {
    return await httpRequest({
      method: 'put',
      url: `${config?.apiBaseUrl}api/profile/timezone`,
      data: { timezone },
    })
  }

  async getPageInformationByCategory(id: string) {
    return await httpRequest({
      method: 'get',
      url: `${config?.apiBaseUrl}api/page/category/${id}`,
    })
  }

  async getPageInformationById(id: string) {
    return await httpRequest({
      method: 'get',
      url: `${config?.apiBaseUrl}api/page/${id}`,
    })
  }

  async getFaq(category: string) {
    return await httpRequest({
      method: 'get',
      url: `${config?.apiBaseUrl}api/faq/category/${category}`,
    })
  }

  async getNotifications() {
    return await httpRequest({
      method: 'get',
      url: `${config?.apiBaseUrl}api/profile/notifications?page=1&perPage=1000`,
    })
  }
  async readNotificationsbyId(id: string) {
    return await httpRequest({
      method: 'patch',
      url: `${config?.apiBaseUrl}api/profile/notifications/${id}/read`,
    })
  }
  async readAllNotifications() {
    return await httpRequest({
      method: 'post',
      url: `${config?.apiBaseUrl}api/profile/notifications/read-all`,
    })
  }
  async patchChangePassword(payload: { password: string; newPassword: string }) {
    return await httpRequest({
      method: 'patch',
      url: `${config?.apiBaseUrl}api/profile/change-password`,
      data: payload,
    })
  }

  async clientGetProfileInformation() {
    try {
      const response = await httpRequest({
        method: 'get',
        url: `${config?.apiBaseUrl}api/clients/profile`,
      })

      let profileData = null

      // Check for different response structures
      if (response && typeof response === 'object') {
        // Case 1: The response itself is the data (most likely your case based on logs)
        if (response.id && response.userIdentityId) {
          profileData = response
        }
        // Case 2: Standard axios/fetch response with data property
        else if (response.data && typeof response.data === 'object') {
          profileData = response.data
        }
        // Case 3: Response with body property
        else if (response.body && typeof response.body === 'object') {
          profileData = response.body
        }
      }

      if (!profileData) {
        console.error('Could not extract profile data from response', response)
        throw new Error('Invalid or empty response from server')
      }

      // Return the data in a consistent format
      return {
        success: true,
        data: profileData,
      }
    } catch (error) {
      console.error('Error in clientGetProfileInformation:', error)
      // Re-throw the error for the hook to handle
      throw error
    }
  }

  async clientUpdateProfile(payload: FormData) {
    try {
      const response = await httpRequest({
        method: 'put',
        url: `${config?.apiBaseUrl}api/clients/profile`,
        data: payload,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      })

      // If response exists, consider it successful even if data property is empty
      if (response) {
        return {
          success: true,
          data: response.data || response,
        }
      }

      throw new Error('No response received from server')
    } catch (error) {
      console.error('Error in clientUpdateProfile:', error)
      throw error
    }
  }

  async clientUpdatePassword(payload: { password: string; newPassword: string }) {
    return await httpRequest({
      method: 'patch',
      url: `${config?.apiBaseUrl}api/profile/change-password`,
      data: payload,
      headers: {
        'Content-Type': 'application/json',
      },
    })
  }

  async clientUpdateNotificationStatus(payload: NotificationPayload) {
    return await httpRequest({
      method: 'patch',
      url: `${config?.apiBaseUrl}api/profile/config`,
      data: payload,
      headers: {
        'Content-Type': 'application/json',
      },
    })
  }
}

export const profileService = new ProfileService()
