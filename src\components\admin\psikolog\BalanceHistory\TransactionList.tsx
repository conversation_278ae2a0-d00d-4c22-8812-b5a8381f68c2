import { ClientType } from './column'
import { InfoDataDisplay } from '@/components/datagrid/InfoDataDisplay'
import { AppBigCaption } from '@/components/_common/ui'
import moment from 'moment'

interface TransactionListProps {
  transactions: ClientType[]
  isLoading: boolean
}

export const TransactionList = ({ transactions, isLoading }: TransactionListProps) => {
  if (isLoading) {
    return <div className="flex justify-center items-center py-8">Loading..</div>
  }

  if (transactions.length === 0) {
    return <div className="text-center py-8 text-gray-500">No transactions found</div>
  }

  return (
    <div className="space-y-4">
      {transactions.map((transaction) => (
        <div key={transaction.id} className="flex justify-between items-center p-4 border rounded-lg">
          <div className="flex-1">
            <InfoDataDisplay
              title={transaction.type === 'INCOME' ? 'Ko<PERSON><PERSON> dari <PERSON>ling' : 'Penarikan Saldo'}
              subTitle={moment(transaction.createdAt).locale('id').format('DD MMMM YYYY, HH:mm [WIB]')}
            />
          </div>
          <div className="flex items-center justify-end gap-x-2">
            <div className="flex flex-col gap-y-1">
              <span
                className={`text-right w-full font-bold ${transaction.type === 'INCOME' ? 'text-gray-400' : 'text-blue-500'}`}
              >
                {transaction.type === 'INCOME' ? '' : '-'}
                {Intl.NumberFormat('id-ID', {
                  style: 'currency',
                  currency: 'IDR',
                  maximumFractionDigits: 0,
                  minimumFractionDigits: 0,
                }).format(Number(transaction.amount))}
              </span>
              <AppBigCaption className="text-gray-200">
                {transaction.type === 'INCOME' ? 'Pemasukan' : 'Pengeluaran'}
              </AppBigCaption>
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}
