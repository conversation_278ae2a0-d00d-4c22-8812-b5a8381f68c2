import { useToast } from '@/components/ui/use-toast'
import { dashboardService } from '@/services/dashboard.service'
import { useSelector } from '@/store'
import { AuthRole } from '@/store/auth/auth.action'
import { useQuery } from '@tanstack/react-query'

export const useGetCounselingCounter = () => {
  const { user } = useSelector((state) => state.Authentication)
  const { toast } = useToast()
  return useQuery({
    queryKey: ['CounselingCounter', { role: user?.role }],
    queryFn: () => {
      if (user?.role === AuthRole.PSIKOLOG) {
        return dashboardService
          .getCounselingCounter()
          .then((response) => {
            return response
          })
          .catch((error) => {
            toast({
              title: 'Gagal',
              description: 'Terjadi masalah dengan server, Silahkan hubungi Admin',
              variant: 'danger',
            })
          })
      } else if (user?.role === AuthRole.ADMIN) {
        return dashboardService
          .adminGetCounselingCounter()
          .then((response) => {
            return response
          })
          .catch((error) => {
            toast({
              title: 'Gagal',
              description: '<PERSON><PERSON><PERSON><PERSON> masalah dengan server, Silahkan hubungi Admin',
              variant: 'danger',
            })
          })
      }
    },
  })
}
