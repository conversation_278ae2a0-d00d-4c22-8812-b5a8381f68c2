import AppInput from '@/components/_common/input/Input'
import { regionService } from '@/services/region.service'
import { useEffect, useState } from 'react'
import { OnboardStepOneProps } from './OnboardStepOne'
import { AppSelect } from '@/components/_common/Select/AppSelect'
import { useProvince } from '@/hooks/useProvince.hook'
import { useRegencies } from '@/hooks/useRegencies.hook'
import { useDistricts } from '@/hooks/useDistricts.hook'
import { useSubDistricts } from '@/hooks/useSubdistricts.hook'

type DomiciliesProps = OnboardStepOneProps & {
  onFinalSetValue: (val: string) => void
  provinceProps?: string
  regencieProps?: string
  districtProps?: string
  subDistrictProps?: string
}

export const DomiciliesOptions = ({
  register,
  getValues,
  setValue,
  errors,
  onFinalSetValue,
  provinceProps,
  regencieProps,
  districtProps,
  subDistrictProps,
}: DomiciliesProps) => {
  // const [provinceOptions, setProvinceOptions] = useState<any>([])
  // const [regencieOptions, setRegencieOptions] = useState<any>([])
  // const [districtOptions, setDistrictOptions] = useState<any>([])
  // const [subDistrictOptions, setSubDistrictOptions] = useState<any>([])

  const [province, setProvince] = useState<string>(provinceProps ?? '')
  const [regencie, setRegencie] = useState<string>(regencieProps ?? '')
  const [district, setDistrict] = useState<string>(districtProps ?? '')
  const [subDistrict, setSubDistrict] = useState<string>(subDistrictProps ?? '')

  const { data: provinceOptions } = useProvince()
  const { data: regencieOptions } = useRegencies(province)
  const { data: districtOptions } = useDistricts(regencie)
  const { data: subDistrictOptions } = useSubDistricts(district)

  useEffect(() => {
    if (getValues('province')) {
      setProvince(getValues('province'))
    }
    if (getValues('regencies')) {
      setRegencie(getValues('regencies'))
    }
    if (getValues('districts')) {
      setDistrict(getValues('districts'))
    }
    if (getValues('subDistricts')) {
      setSubDistrict(getValues('subDistricts'))
    }
  }, [getValues])
  return (
    <>
      <AppSelect
        {...register('province')}
        options={provinceOptions || []}
        onChange={(val) => {
          console.log(val)
          setProvince(val)
          setValue('province', val, { shouldValidate: true })
          setValue('regencies', '', { shouldValidate: true })
          setValue('districts', '', { shouldValidate: true })
          setValue('subDistricts', '', { shouldValidate: true })

          // getRegencies(val)
          setRegencie('')
          setDistrict('')
          setSubDistrict('')
        }}
        value={getValues('province') ? String(getValues('province')) : ''}
        className=""
        label="Domisili"
        name="province"
        placeholder="Pilih Provinsi"
        errorMsg={!!errors.province ? String(errors.province.message) : undefined}
      />
      {province && (
        <>
          <AppSelect
            {...register('regencies')}
            options={regencieOptions || []}
            onChange={(val) => {
              setRegencie(val)
              setValue('regencies', val, { shouldValidate: true })
              setValue('districts', '', { shouldValidate: true })
              setValue('subDistricts', '', { shouldValidate: true })

              // getDistricts(val)
              setDistrict('')
              setSubDistrict('')
            }}
            value={getValues('regencies') ? String(getValues('regencies')) : ''}
            className=""
            label=""
            name="regencies"
            placeholder="Pilih Kota/Kabupaten"
            errorMsg={!!errors.regencies ? String(errors.regencies.message) : undefined}
          />
        </>
      )}
      {regencie && (
        <AppSelect
          {...register('districts')}
          options={districtOptions || []}
          onChange={(val) => {
            setDistrict(val)
            setValue('districts', val, { shouldValidate: true })
            setValue('subDistricts', '', { shouldValidate: true })

            // getSubDistricts(val)
            setSubDistrict('')
          }}
          value={getValues('districts') ? String(getValues('districts')) : ''}
          className=""
          label=""
          name="districts"
          placeholder="Pilih Kecamatan"
          errorMsg={!!errors.districts ? String(errors.districts.message) : undefined}
        />
      )}
      {district && (
        <AppSelect
          {...register('subDistricts')}
          options={subDistrictOptions || []}
          onChange={(val) => {
            setSubDistrict(val)
            setValue('subDistricts', val, { shouldValidate: true })
            const concat = `${province}, ${regencie}, ${district}, ${val}`
            onFinalSetValue(concat)
          }}
          value={getValues('subDistricts') ? String(getValues('subDistricts')) : ''}
          className=""
          label=""
          name="subDistricts"
          placeholder="Pilih Desa"
          errorMsg={!!errors.subDistricts ? String(errors.subDistricts.message) : undefined}
        />
      )}
    </>
  )
}
