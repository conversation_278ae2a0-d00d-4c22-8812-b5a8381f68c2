'use client'
import { Card, H2 } from '@/components/_common/ui'
import { useDispatch } from '@/store'
import AvaibilityDay from './ListView/AvaibilityDay'
import { AvaibilitySpecificDate } from './ListView/AvaibilitySpecificDate'
import {
  AvaibilityItemProps,
  AvaibilityProps,
  AvaibilityWeekProps,
  useAvaibilityList,
} from './useAvaibilityList.hook'
import { avaibilityService } from '@/services/avaibility.service'
import { useToast } from '@/components/ui/use-toast'
import { getEndTime, getStartTime, isAfterValidTime } from '../utils/getAvaibilityTime'
import LoadingAvaibility from '@/components/loading/LoadingAvaibility'
import LoadingAvaibilitySpecific from '@/components/loading/LoadingAvaibilitySpecific'

const AvaibilityListVew = () => {
  const { toast } = useToast()
  const dispatch = useDispatch()

  const { data, refetch, isLoading, isPending, isError } = useAvaibilityList()
  const { weeklyAvaibility: avaibility, specificAvaibility } = data || {}

  const handleChangeChecked = async (item: AvaibilityWeekProps) => {
    if (item.isActive) {
      try {
        if (item.id >= 0) {
          await avaibilityService.removeAvaibilityByDay(item.id)
          refetch()
        }
      } catch (error) {
        toast({
          title: 'Gagal',
          description: 'Terjadi masalah dengan jaringan, Silahkan coba lagi',
          variant: 'danger',
        })
      }
    } else {
      try {
        const startTime = getStartTime(item.timeList)
        const endTime = '21:00'
        const payload = {
          timezone: 'Asia/Jakarta',
          startTime: startTime,
          endTime: endTime,
          day: item.id,
        }
        await avaibilityService.createUpdateAvaibility(payload)
        refetch()
      } catch (error) {
        toast({
          title: 'Gagal',
          description: 'Terjadi masalah dengan jaringan, Silahkan coba lagi',
          variant: 'danger',
        })
      }
    }
  }

  const handleAddTime = async ({ id, timeList }: AvaibilityProps) => {
    if (timeList.length >= 12) {
      toast({
        variant: 'danger',
        title: 'Anda sudah mencapai jadwal maksimal, jangan lupa beristirahat :D',
      })
      return
    } else {
      try {
        const startTime = getStartTime(timeList)
        const endTime = getEndTime(startTime)
        const invalidTime = isAfterValidTime(endTime)
        if (invalidTime) {
          toast({
            title: 'Gagal',
            description: 'Jadwal anda hari ini telah penuh',
            variant: 'danger',
          })
          return
        }
        await avaibilityService.createUpdateAvaibility({
          timezone: 'Asia/Jakarta',
          startTime: startTime,
          endTime: endTime,
          day: id,
        })
        refetch && refetch()
      } catch (error) {
        toast({
          title: 'Gagal',
          description: 'Terjadi masalah dengan jaringan, Silahkan coba lagi',
          variant: 'danger',
        })
      }
    }
  }

  const handleRemoveTime = async (val: AvaibilityItemProps) => {
    try {
      await avaibilityService.removeAvaibilityById(val.id ? val.id : '')
      refetch && refetch()
    } catch (error) {
      toast({
        title: 'Gagal',
        description: 'Terjadi kesalahan saat menghapus jadwal. Silahkan coba lagi',
        variant: 'danger',
      })
    }
  }

  const handleSetTime = async (val: AvaibilityItemProps) => {
    try {
      const payload = {
        startTime: val.startTime,
        endTime: val.endTime,
      }
      await avaibilityService.updateAvaibilityById(payload, val.id ? val.id : '')
      refetch && refetch()
    } catch (error: any) {
      const errorMessage =
        error?.response?.data?.message ||
        error?.message ||
        'Terjadi kesalahan saat merubah jadwal. Silahkan coba lagi'
      toast({
        title: 'Gagal',
        description: errorMessage,
        variant: 'danger',
      })
    }
  }

  const isLoadingApp = isPending || isLoading
  return (
    <div className="grid grid-cols-12 gap-4">
      <div className="col-span-12 xl:col-span-7">
        <Card className="text-gray-400 p-6 xs:p-6">
          {isLoadingApp ? (
            <LoadingAvaibility />
          ) : (
            <>
              <H2>Jadwal Mingguan</H2>
              {avaibility &&
                avaibility.map((item: any) => (
                  <AvaibilityDay
                    key={item.day}
                    refetch={refetch}
                    onCheckedChange={() => handleChangeChecked(item)}
                    onRemoveTime={(val) => handleRemoveTime(val)}
                    onSetStartTime={(val) => handleSetTime(val)}
                    onSetEndTime={(val) => handleSetTime(val)}
                    onAddNewTime={() => handleAddTime(item)}
                    {...item}
                  />
                ))}
            </>
          )}
        </Card>
      </div>
      <div className="col-span-12 xl:col-span-5">
        <Card className="text-gray-400 p-6 xs:p-6">
          {isLoadingApp ? (
            <LoadingAvaibilitySpecific />
          ) : (
            <AvaibilitySpecificDate refetch={refetch} specificDateAvaibility={specificAvaibility || []} />
          )}
        </Card>
      </div>
    </div>
  )
}

export default AvaibilityListVew
