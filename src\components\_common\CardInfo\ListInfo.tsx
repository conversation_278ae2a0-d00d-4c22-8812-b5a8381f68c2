'use client'
import React from 'react'

type ListPropsType = {
  list: string[]
  divider?: boolean | React.ReactNode
}

const ListItem = ({ title, length, index }: { title: string; index: number; length: number }) => {
  let item: React.ReactNode[] = [
    <li key={index} className="text-sm text-gray-500">
      {title}
    </li>,
  ]
  if (length > 1) {
    index != length - 1 &&
      item.push(
        <li
          key={'divider-' + index}
          className="flex-none rounded-full w-[6px] h-[6px] bg-line-200 mx-[11px]"
        ></li>
      )
  }
  return item
}

export const ListInfo = ({ list, divider }: ListPropsType) => {
  return (
    <ul className="flex flex-wrap items-center overflow-y-hidden overflow-x-auto">
      {list.map((item, index) => (
        <ListItem key={index} title={item} index={index} length={list.length} />
      ))}
    </ul>
  )
}
