import { counsellingService } from '@/services/counselling.service'
import { useState, useEffect } from 'react'

interface DetailTransactionResponse {
  googleMeetUrl: string | undefined
  id: string
  psychologistId: string
  clientId: string
  startTime: string
  endTime: string
  complaint: string
  expectation: string
  status: string
  description: string
  method: string
  duration: number
  createdAt: string
  problemCategory: Array<{
    id: string
    problemCategory: string
  }>
  client: {
    fullName: string
    profilePhoto: string
  }
  psychologist: {
    fullName: string
    profilePhoto: string
  }
  payment: {
    status: string
    amount: number
    paidAt: string
    gatewayUrl: string
  }
}

export const useGetDetailTransaction = (id: string) => {
  const [data, setData] = useState<DetailTransactionResponse | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchData = async () => {
      if (!id) return

      try {
        setIsLoading(true)
        const response = await counsellingService.clientGetDetailCounsellingById(id)
        console.log('API Response:', response)

        if (!response) {
          throw new Error('No data received')
        }

        // Check if response has data property
        const transactionData = response.data || response
        console.log('Processed Data:', transactionData)

        setData(transactionData)
        setError(null)
      } catch (err) {
        console.error('Fetch Error:', err)
        setError(err instanceof Error ? err.message : 'Failed to fetch data')
        setData(null)
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
  }, [id])

  // Debug log current state
  useEffect(() => {
    console.log('Current Hook State:', { data, isLoading, error })
  }, [data, isLoading, error])

  return { data, isLoading, error }
}
