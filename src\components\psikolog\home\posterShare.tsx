import ButtonPrimary from '@/components/_common/ButtonPrimary'
import { useGetProfile } from '@/hooks/useGetProfile.hook'
import { dashboardService } from '@/services/dashboard.service'
import Image from 'next/image'
import { useEffect, useRef, useState } from 'react'

export default function PosterShare() {
  const exportRef = useRef<any>()
  const [photo, setPhoto] = useState('')

  const { data } = useGetProfile()

  useEffect(() => {
    setPhoto(data?.profilePhoto ?? '')
  }, [data])

  const handleClickDownload = async () => {
    try {
      const payload = {
        onboardingSteps: ['SHARE_SOCIAL_MEDIA'],
      }
      await dashboardService
        .postOnboardingSteps(payload)
        .then(() => {
          window.open('/psychologist/image', '_blank')
        })
        .catch((error) => {
          console.log(error)
        })
    } catch (error) {
      console.log(error)
    }
  }

  return (
    <>
      <div className="flex flex-col bg-white gap-4">
        <div
          ref={exportRef}
          className="flex flex-col gap-4 bg-[#F0FAFF] px-10 pb-10 relative z-20 rounded-[15px] overflow-hidden"
        >
          {/* Logo */}
          <div className="flex items-center gap-2 py-[22px]">
            <Image width={24} height={24} src={'/icons/mh-logo.svg'} alt="Logo" priority />
            <span className="text-[#019EE9] text-[16px] font-bold">MentalHealing.id</span>
          </div>

          {/* Greet */}
          <div className="flex flex-col gap-1">
            <p className="text-[#004262] font-bold text-[14px] lg:text-[20px]">HALO TEMAN HEALING!</p>
            <p className="text-[#019EE9] font-bold text-[16px] lg:text-[30px]">
              KONSELING DENGAN SAYA DI MENTALHEALING.ID
            </p>
          </div>

          {/* Avatar and Details */}
          <div className="grid grid-cols-2 gap-2 items-center pb-[35px]">
            <div className="h-[150px] lg:h-[240px] w-full bg-[#fff] rounded-[15px] relative">
              <Image
                src={photo}
                onError={() => setPhoto('/mascot.svg')}
                alt={'photo-' + data?.nickname}
                fill
                className="object-cover"
              />
            </div>
            <div className="flex flex-col gap-1">
              <p className="text-[#004262] font-bold text-[18px] uppercase">{data?.fullName ?? ''}</p>
              <span className="text-[#019EE9] font-bold text-[12px] uppercase">
                {data && data?.field?.length > 0 ? data?.field?.map((val: any) => val.name).join(', ') : ''}
              </span>
            </div>
          </div>

          {/* Background Wave */}
          <div className="w-full absolute bottom-0 left-0 z-10">
            <div className="relative w-full h-[112px]">
              <Image fill src="/ilustration/posterWave.svg" alt="Wave Illustration" className="w-full" />
            </div>
          </div>

          {/* App Download Links */}
          <div className="flex gap-1 md:gap-3 items-center absolute bottom-2 right-2 left-4 md:bottom-5 md:right-7 md:left-10 z-30">
            <span className="text-white text-[8px] md:text-[10px] font-bold">
              Download aplikasinya sekarang!
            </span>
            <div className="flex gap-2 items-center">
              <div className="relative h-[30px] w-[80px] md:w-[100px]">
                <Image
                  src="/images/play-store.svg"
                  alt="Play Store"
                  className="w-[80px] md:w-[100px] cursor-pointer"
                  fill
                />
              </div>
              {/* hide for while until app uploaded to appstore */}
              {/* <div className="relative h-[35px] w-[80px] md:w-[100px]">
                <Image
                  src="/images/app-store.svg"
                  alt="App Store"
                  className="w-[80px] md:w-[100px] cursor-pointer"
                  fill
                />
              </div> */}
            </div>
          </div>
        </div>

        {/* Download Section */}
        <div className="flex flex-col gap-6 items-center justify-center w-full">
          <p className="text-[16px] text-[#222222]">Download gambar, siap posting deh!</p>
          <ButtonPrimary
            onClick={() => handleClickDownload()}
            className="min-w-[143px] rounded-[15px] text-center"
            variant="contained"
            size="sm"
          >
            Download Gambar
          </ButtonPrimary>
        </div>
      </div>
    </>
  )
}
