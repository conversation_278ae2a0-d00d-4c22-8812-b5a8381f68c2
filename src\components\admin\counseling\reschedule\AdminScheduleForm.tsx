import { dispatch, useSelector } from '@/store'
import { useState } from 'react'
import { useGetScheduleByDate } from '@/hooks/useGetScheduleByDate.hook'
import { MOMENT_INPUT_DATE_FORMAT } from '@/constans/date'
import { useToast } from '@/components/ui/use-toast'
import { counsellingService } from '@/services/counselling.service'
import moment from 'moment-timezone'
import { setRescheduleStep } from '@/store/admin/counseling.reducer'
import { AdminRescheduleNote } from './AdminRescheduleNote'
import { AdminRescheduleSuccessState } from './AdminRescheduleSuccessState'
import { AdminRescheduleDateTime } from './AdminRescheduleDateTime'
import { useGetPsychologistScheduleByDate } from '@/hooks/useGetPsychologistScheduleByDate.hook'
import { PaymentStatus } from '@/constans/StaticOptions'
import 'moment/locale/id'

export default function AdminScheduleForm({
  callbackToggleCalendar,
  item,
  onClose,
}: {
  callbackToggleCalendar?: (arg: boolean) => void
  item: any
  onClose: () => void
}) {
  const { toast } = useToast()
  const counselingDuration = item?.duration || 60
  const { rescheduleStep, rescheduleDetail } = useSelector((state) => state.AdminCounseling)
  const { user } = useSelector((state) => state.Authentication)
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date())
  const [selectedTime, setSelectedTime] = useState<string>('')

  const handleClickSelectedDate = (date: Date | undefined) => {
    setSelectedDate(date)
    setSelectedTime('')
  }
  const handleClickSelectedTime = (time: any) => {
    setSelectedTime(time)
  }
  const handleClickSubmit = async () => {
    if (!rescheduleDetail?.note) {
      toast({
        variant: 'danger',
        description: 'lengkapi data terlebih dahulu.',
      })
      return
    }
    try {
      const scheduleDateTime = moment(`${rescheduleDetail?.date} ${rescheduleDetail?.time}`)
        .locale('id')
        .utc(true)
        .toISOString()
      const payload = {
        imitate:
          item?.status === PaymentStatus.RESCHEDULE_BY_PSYCHOLOGIST
            ? 'Client'
            : item?.status === PaymentStatus.RESCHEDULE_BY_CLIENT
              ? 'Psychologist'
              : 'Psychologist',
        schedule: scheduleDateTime,
        message: rescheduleDetail?.note || '',
      }
      await counsellingService.adminRescheduleCounselling(item?.id, payload)
      dispatch(setRescheduleStep(rescheduleStep + 1))
    } catch (error) {
      toast({
        variant: 'danger',
        description: 'Terjadi kesalahan saat merubah jadwal. Silahkan ulangi beberapa saat lagi.',
      })
    }
  }

  const psychologistId = item?.psychologist?.id

  const {
    data: dataSchedule,
    isLoading,
    isPending,
  } = useGetPsychologistScheduleByDate(
    psychologistId,
    selectedDate ? moment(selectedDate).format(MOMENT_INPUT_DATE_FORMAT) : '',
    counselingDuration
  )

  const timeZone = dataSchedule?.schedule?.[0]?.timezone ?? ''

  return (
    <div className="grid grid-cols-1 grid-rows-1 gap-4">
      {rescheduleStep === 1 ? (
        <AdminRescheduleDateTime
          isLoading={isLoading || isPending}
          timeSelected={selectedTime}
          dateSelected={selectedDate}
          item={item}
          timeList={dataSchedule?.schedule}
          callbackToggleCalendar={callbackToggleCalendar}
          onSelectedDate={(date) => handleClickSelectedDate(date)}
          onSelectedTime={(time) => handleClickSelectedTime(time)}
        />
      ) : rescheduleStep === 2 ? (
        <AdminRescheduleNote
          counselingDuration={counselingDuration}
          timezone={timeZone}
          clientName={item?.client?.fullName}
          onSubmit={() => handleClickSubmit()}
        />
      ) : rescheduleStep === 3 ? (
        <AdminRescheduleSuccessState clientName={item?.client?.fullName} />
      ) : null}
    </div>
  )
}
