import { Card } from '@/components/_common/ui'
import FilterPenalty from './FilterPenalty'
import PenaltyContent from './PenaltyContent'

export default function PenaltyComponent() {
  return (
    <>
      <Card className="border-0 p-0 xs:p-0 sm:border sm:p-6 flex flex-col gap-4 w-full lg:w-[70%]">
        <FilterPenalty />
        <PenaltyContent
          title="Menolak Sesi Konseling Hilmi"
          id="Konsel123234"
          date="17 Juli 2024, 11:37 WIB"
        />
        <PenaltyContent
          title="Menolak Sesi Konseling Hilmi"
          id="Konsel123234"
          date="17 Juli 2024, 11:37 WIB"
        />
        <PenaltyContent
          title="Tidak Mengkonfirmasi Sesi Konseling Hilmi"
          id="Konsel123234"
          date="17 Juli 2025, 11:37 WIB"
        />
      </Card>
    </>
  )
}
