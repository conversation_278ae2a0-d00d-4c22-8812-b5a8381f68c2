import { IIcons, SVGIcons } from '@/components/_common/icon'

export default function PinPasswordButton({
  number,
  backSpace,
  onHandleChange,
}: {
  number: any
  backSpace: boolean
  onHandleChange?: (val: number) => void
}) {
  const buttonPassword = (
    <button value={number} className="text-center text-[26px] text-[#222222] font-bold">
      {backSpace ? <SVGIcons name={IIcons.BackSpace} /> : number}
    </button>
  )
  return (
    <div
      onClick={() => onHandleChange && onHandleChange(number)}
      className="w-[52px] xs:w-[66px] md:w-[98px] h-[56px] hover:bg-[#ebebeb] flex items-center justify-center border border-[#222222] rounded-[70px] cursor-pointer"
    >
      {buttonPassword}
    </div>
  )
}
