'use client'
import { Swiper, SwiperSlide, useSwiper } from 'swiper/react'
import { FreeMode, Navigation } from 'swiper/modules'
import 'swiper/css'
import 'swiper/css/pagination'
import ArrowLeftIcon from '@/assets/icons/arrow-left.svg'
import ArrowRightIcon from '@/assets/icons/arrow-right.svg'
import { useEffect, useRef, useState } from 'react'
import { TestimonyCardItem } from './TestimonyCardItem'
import { testimonyService } from '@/services/testimony.service'

export const Testimony = () => {
  const [slideContent, setSlideContent] = useState<any>([])
  const refPrev = useRef<any>(null)
  const refNext = useRef<any>(null)

  useEffect(() => {
    testimonyService
      .getTestimonyByCategory('B2C')
      .then((response) => {
        if (response.length) {
          setSlideContent(response)
        }
      })
      .catch((error) => {
        console.log(error)
      })
  }, [])

  function SlideNextButton() {
    const swiper = useSwiper()
    return (
      <span
        ref={refNext}
        className="rounded-full border border-line-200 p-1 cursor-pointer"
        onClick={() => swiper.slideNext()}
      >
        <ArrowRightIcon className="w-6 h-6" />
      </span>
    )
  }
  function SlidePrevButton() {
    const swiper = useSwiper()
    return (
      <span
        ref={refPrev}
        className="rounded-full border border-line-200 p-1 cursor-pointer"
        onClick={() => swiper.slidePrev()}
      >
        <ArrowLeftIcon className="w-6 h-6" />
      </span>
    )
  }
  return (
    <div className="w-full max-w-screen xl:max-w-[1120px] relative flex flex-col gap-y-6 md:gap-y-10">
      <div className="flex justify-between px-4">
        <span className="hidden md:flex text-[38px] leading-[42px] font-bold text-[#222222]">
          Testimoni Teman Healing
        </span>
        <span className="md:hidden text-subheading-md font-bold">Testimoni</span>
        <div className="flex gap-x-2 items-center">
          <div className="flex gap-x-2">
            <span
              className="rounded-full border border-line-200 p-1 cursor-pointer w-[34px] h-[34px] bg-white"
              onClick={() => refPrev.current.click()}
            >
              <ArrowLeftIcon className="w-6 h-6" />
            </span>
            <span
              className="rounded-full border border-line-200 p-1 cursor-pointer w-[34px] h-[34px] bg-white"
              onClick={() => refNext.current.click()}
            >
              <ArrowRightIcon className="w-6 h-6" />
            </span>
          </div>
          {/* <span className="hidden md:flex text-main-100 font-bold">Lihat semua</span> */}
        </div>
      </div>
      <div className="relative w-full">
        <Swiper
          slidesPerView={'auto'}
          navigation
          spaceBetween={24}
          freeMode={true}
          pagination={{
            clickable: true,
          }}
          modules={[FreeMode, Navigation]}
          className="mySwiperpsychologist"
        >
          <div className="hidden">
            <SlideNextButton />
            <SlidePrevButton />
          </div>
          {slideContent.length
            ? slideContent.map((testimony: any) => (
                <SwiperSlide
                  className="first:pl-4 last:pr-4"
                  key={testimony.id}
                  style={{ width: 'fit-content' }}
                >
                  <TestimonyCardItem {...testimony} />
                </SwiperSlide>
              ))
            : null}
        </Swiper>
      </div>
    </div>
  )
}
