'use client'

import Image from 'next/image'
import Link from 'next/link'
import { IIcons, SVGIcons } from '../_common/icon'
import Avatar from './Avatar'
import { Separator } from '../ui/separator'
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent } from '@/components/ui/DropdownMenu'
import { H4 } from '../_common/ui'
import { useRouter } from 'next/navigation'
import { Notification } from './Notification'
import TokenService from '@/services/token.sevice'
import { useGetProfile } from '@/hooks/useGetProfile.hook'
import { AuthRole } from '@/store/auth/auth.action'
import { Routes } from '@/constans/routes'

const Navbar = (props: {
  sidebarOpen: string | boolean | undefined
  setSidebarOpen: (arg0: boolean) => void
  onShowAll: string
}) => {
  const router = useRouter()

  const { data } = useGetProfile()
  const fullName = data?.nickname || 'Mental Healing'
  const role = data?.userIdentity?.role || 'Guest'
  const photoProfile = data?.profilePhoto || ''

  const handleClickSidebar = () => {
    props.setSidebarOpen(!props.sidebarOpen)
  }

  const handleSignOut = () => {
    TokenService.removeLocalTokenAndRole()
    router.refresh()
  }

  const handleClickSetting = (activeTab?: string) => {
    if (role === AuthRole.ADMIN || role === AuthRole.SUPERADMIN) {
      router.push(activeTab ? Routes.AdminSetting + '?activeTab=' + activeTab : Routes.AdminSetting)
    } else {
      router.push(
        activeTab ? Routes.PsychologistSetting + '?activeTab=' + activeTab : Routes.PsychologistSetting
      )
    }
  }

  return (
    <header className="h-navbar top-0 z-9999 flex p-4 bg-white lg:translate-x-0 border-b border-line-200">
      <div className="grow place-content-center">
        <Link href="/">
          <Image width={176} height={32} src={'/icons/mh-logo-with-title.svg'} alt="Logo" priority />
        </Link>
      </div>
      <div className="grow place-content-center">
        <div className="flex justify-end">
          <div className="place-content-center px-3">
            <Notification onShowAll={props.onShowAll} />
          </div>
          <div className="place-content-center pr-2 flex lg:hidden">
            <button
              aria-controls="sidebar"
              onClick={(e) => {
                e.stopPropagation()
                handleClickSidebar()
              }}
            >
              {props.sidebarOpen ? (
                <SVGIcons name={IIcons.Close} className="w-6 h-6" />
              ) : (
                <SVGIcons name={IIcons.Burger} className="w-6 h-6" />
              )}
            </button>
          </div>
          <div className="h-auto py-3">
            <Separator orientation="vertical" />
          </div>
          <div className="hidden lg:flex items-center">
            <DropdownMenu modal={false}>
              <DropdownMenuTrigger className="[&>div>svg]:data-[state=open]:rotate-180" asChild>
                <div className="flex">
                  <div className="place-content-center px-2">
                    <SVGIcons name={IIcons.ArrowDown} />
                  </div>
                  <div className="items-center px-2">
                    <Avatar
                      objectPosition="center"
                      // scaleDown
                      width={34}
                      height={34}
                      image={photoProfile}
                      alt={fullName}
                    />
                  </div>
                  <div className="columns text-base">
                    <p className="text-black text-xsm font-bold">{fullName}</p>
                    <p className="text-black text-sm">{role}</p>
                  </div>
                </div>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="grid gap-y-4 min-w-[300px] p-0">
                <ul>
                  <li className="flex flex-nowrap flex-row px-6 py-4">
                    <Avatar
                      objectPosition="center"
                      // scaleDown
                      width={60}
                      height={60}
                      image={photoProfile}
                      alt={fullName}
                    />
                    <div className="flex flex-col h-auto justify-center ml-4">
                      <H4 bold>{fullName}</H4>
                      {/* <span
                        className="cursor-pointer text-body-md text-main-100 hover:text-main-300 font-bold"
                        onClick={() => handleClickSetting('0')}
                      >
                        Lihat Profil
                      </span> */}
                    </div>
                  </li>
                  <Separator orientation="horizontal" />
                  <li
                    className="flex flex-nowrap flex-row px-6 py-4 hover:bg-gray-50 cursor-pointer "
                    onClick={() => handleClickSetting()}
                  >
                    <Image src={'/icons/setting.svg'} width={24} height={24} alt="setting-logo" />
                    <span className="text-body-lg font-bold text-gray-400 ml-4">Pengaturan Akun</span>
                  </li>
                  <Separator orientation="horizontal" />
                  <li
                    className="flex flex-nowrap flex-row px-6 py-4 hover:bg-gray-50 cursor-pointer"
                    onClick={() => handleSignOut()}
                  >
                    <Image src={'/icons/sign-out.svg'} width={24} height={24} alt="setting-logo" />
                    <span className="text-body-lg font-bold text-gray-400 ml-4">Keluar</span>
                  </li>
                </ul>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>
    </header>
  )
}

export default Navbar
