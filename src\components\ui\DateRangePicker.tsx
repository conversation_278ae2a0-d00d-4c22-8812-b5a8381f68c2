'use client'

import * as React from 'react'
import { format, startOfYesterday } from 'date-fns'
import { id } from 'date-fns/locale'
import { DateRange } from 'react-day-picker'

import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Calendar } from '@/components/ui/calendar'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { IIcons, SVGIcons } from '../_common/icon'
import useCalculateWidth from '@/hooks/useCalculateWidth'
import moment from 'moment'

export function DatePickerWithRange({
  className,
  showGotoDate,
  onSelectDate,
  formatDate,
  valueDate,
}: React.HTMLAttributes<HTMLDivElement> & {
  showGotoDate?: boolean
  onSelectDate?: (date: DateRange) => void
  formatDate?: {
    from: string
    to: string
  }
  valueDate?: DateRange
}) {
  const [date, setDate] = React.useState<DateRange | undefined>({
    from: valueDate?.from,
    to: valueDate?.to,
  })

  const handleClickToday = () => {
    setDate({
      from: new Date(),
      to: new Date(),
    })
  }

  const handleClickYesterday = () => {
    setDate({
      from: startOfYesterday(),
      to: new Date(),
    })
  }

  const handleClickWeekAgo = () => {
    var date = new Date()
    date.setDate(date.getDate() - 7)
    setDate({
      from: date,
      to: new Date(),
    })
  }

  const handleClickMonthAgo = () => {
    var date = new Date()
    date.setDate(date.getDate() - 30)
    setDate({
      from: date,
      to: new Date(),
    })
  }

  const handleClickCurrentMonth = () => {
    var date = new Date(),
      year = date.getFullYear(),
      month = date.getMonth()
    var firstDay = new Date(year, month, 1)
    var lastDay = new Date(year, month + 1, 0)
    setDate({
      from: firstDay,
      to: lastDay,
    })
  }

  const handleSelectedDate = (range: DateRange) => {
    setDate({
      from: range.from,
      to: range.to,
    })
    onSelectDate && onSelectDate(range)
  }

  React.useEffect(() => {
    if (date?.from && date?.to) {
      onSelectDate && onSelectDate(date)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [date])

  const width = useCalculateWidth()
  const isSmallScreen = width < 640

  return (
    <div className={cn('w-auto xs:w-full sm:w-auto h-[55px] flex items-center', className)}>
      <Popover>
        <PopoverTrigger asChild>
          <Button
            id="date"
            variant={'outline'}
            size={'lg'}
            className={cn(
              'w-full justify-start text-left font-medium h-full',
              !date && 'text-muted-foreground'
            )}
          >
            <SVGIcons name={IIcons.Calendar} className="mr-2 text-gray-200" />
            {date?.from ? (
              date.to ? (
                <>
                  {format(date.from, formatDate ? formatDate.from : 'LLL dd, y')} -{' '}
                  {format(date.to, formatDate ? formatDate.to : 'LLL dd, y')}{' '}
                  {formatDate ? format(date.to, 'yyyy') : null}
                </>
              ) : (
                format(date.from, 'LLL dd, y')
              )
            ) : (
              <span>Pick a date</span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="end">
          {isSmallScreen && (
            <Calendar
              locale={id}
              initialFocus
              mode="range"
              selected={date}
              onDayClick={(day) => {
                setDate((prev) => {
                  if (prev?.to) {
                    return { from: day, to: undefined }
                  } else if (prev?.from) {
                    return moment(prev?.from).isAfter(day)
                      ? { from: day, to: prev?.from }
                      : { from: prev?.from, to: day }
                  } else {
                    return { from: day, to: undefined }
                  }
                })
              }}
            />
          )}
          <div className="flex flex-wrap">
            {!isSmallScreen && (
              <Calendar
                locale={id}
                initialFocus
                mode="range"
                defaultMonth={date?.from}
                selected={date}
                onDayClick={(day) => {
                  console.log(day)
                  setDate((prev) => {
                    if (prev?.to) {
                      return { from: day, to: undefined }
                    } else if (prev?.from) {
                      return moment(prev?.from).isAfter(day)
                        ? { from: day, to: prev?.from }
                        : { from: prev?.from, to: day }
                    } else {
                      return { from: day, to: undefined }
                    }
                  })
                }}
                numberOfMonths={isSmallScreen ? 1 : 2}
              />
            )}
            {!!showGotoDate && (
              <div className="flex flex-col sm:flex-col p-3 w-auto gap-y-4">
                <span className="cursor-pointer text-body-md hover:text-main-100" onClick={handleClickToday}>
                  Hari ini
                </span>
                <span
                  className="cursor-pointer text-body-md hover:text-main-100"
                  onClick={handleClickYesterday}
                >
                  Kemarin
                </span>
                <span
                  className="cursor-pointer text-body-md hover:text-main-100"
                  onClick={handleClickWeekAgo}
                >
                  7 Hari terakhir
                </span>
                <span
                  className="cursor-pointer text-body-md hover:text-main-100"
                  onClick={handleClickMonthAgo}
                >
                  30 Hari terakhir
                </span>
                <span
                  className="cursor-pointer text-body-md hover:text-main-100 hover:font-bold"
                  onClick={handleClickCurrentMonth}
                >
                  Bulan ini
                </span>
              </div>
            )}
          </div>
        </PopoverContent>
      </Popover>
    </div>
  )
}
