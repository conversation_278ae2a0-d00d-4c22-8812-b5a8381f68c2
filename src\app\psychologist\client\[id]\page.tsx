import Breadcrumb from '@/components/breadcrumbs/Breadcrumbs'
import { Container } from '@/components/_common/ui'
import { Suspense } from 'react'
import DetailClientComponent from '@/components/psikolog/client/DetailClientComponent'

const ClientReportDetails = async ({ params }: { params: Promise<{ id: string }> }) => {
  const clientId = (await params).id
  return (
    <Suspense>
      <Breadcrumb containerClasses="pb-2" pageName="Detail Klien" />
      <Container>
        <DetailClientComponent idClient={clientId} />
      </Container>
    </Suspense>
  )
}

export function generateStaticParams() {
  return [{ id: 'test-id' }]
}

export default ClientReportDetails
