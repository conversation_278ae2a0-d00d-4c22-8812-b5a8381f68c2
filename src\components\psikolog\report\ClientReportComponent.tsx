'use client'
import TabItem from '@/components/_common/tabs/TabItem'
import TabList from '@/components/_common/tabs/TabList'
import { HeaderContent } from '@/components/admin/HeaderContent'
import ClientReportList from './ClientReportList'
import { useRouter } from 'next/navigation'
import { Routes } from '@/constans/routes'
import { useGetClientReportList } from './hook/useGetClientReportList.hook'
import { useEffect, useState } from 'react'

export const CLientReportComponent = () => {
  const router = useRouter()
  const [category, setCategory] = useState<string>()
  const [activeTab, setActiveTab] = useState<number>(0)

  const isGetAllClientReport = activeTab === 0
  const { data, isError, isLoading, isPending, isFetched } = useGetClientReportList(isGetAllClientReport)
  const { clientReport, meta: counselingMeta } = data || {}
  const [meta, setMeta] = useState(counselingMeta)

  useEffect(() => {
    if (isFetched) setMeta(counselingMeta)
  }, [counselingMeta, isFetched])

  console.log(data)

  const isLoadingApp = isLoading || isPending

  const ClientReportListTab = [
    {
      label: `Semua (${meta?.all ?? 0})`,
      content: (
        <ClientReportList
          isLoadingApp={isLoadingApp}
          itemList={clientReport || []}
          onCreateClientReport={(item) =>
            router.push(
              `${Routes.PsychologistCreateClientReport}?name=${item.client?.fullName}&counselingId=${item.id}`
            )
          }
          onChangeClientReport={(item) =>
            router.push(
              `${Routes.PsychologistCreateClientReport}?name=${item.client?.fullName}&counselingId=${item.id}`
            )
          }
        />
      ),
    },
    {
      label: `Menunggu Diisi (${meta?.pending ?? 0})`,
      content: (
        <ClientReportList
          itemList={clientReport || []}
          onCreateClientReport={(item) =>
            router.push(
              `${Routes.PsychologistCreateClientReport}?name=${item.client?.fullName}&counselingId=${item.id}`
            )
          }
        />
      ),
    },
  ]
  return (
    <>
      <HeaderContent title="Klien Report" />
      <TabList
        onClickTabs={(index) => setActiveTab(index)}
        activeTabIndex={activeTab}
        className="sticky top-navbar z-30 bg-white"
      >
        {ClientReportListTab.map((report, index) => {
          return (
            <TabItem key={index} label={report.label}>
              {report.content}
            </TabItem>
          )
        })}
      </TabList>
    </>
  )
}
