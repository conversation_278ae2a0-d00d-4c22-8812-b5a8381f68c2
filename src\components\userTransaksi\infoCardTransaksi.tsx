import { IIcons, SVGIcons } from "../_common/icon";

type IconName = keyof typeof IIcons; // mendefinisikan tipe dari icon
export default function InfoCardTransaksi({icon, title}: {icon: IconName, title: string}) {
    return (
        <>
        {/* jadwal */}
        <div className="flex gap-2">
            <SVGIcons name={IIcons[icon]} className="w-6 h-6" />
            <span className="text-[#222222] text-[16px]">{title}</span>
        </div>
        </>
    )
}
