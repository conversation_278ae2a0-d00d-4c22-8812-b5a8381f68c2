import { AvatarList } from '@/components/_common/AvatarList'
import StarIcon from '@/assets/icons/empty-star-blue.svg'
import LoveIcon from '@/assets/icons/love.svg'

export const RateComponent = () => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 px-4 xl:px-0">
      <div className="flex flex-col gap-y-2 justify-center">
        <span className="text-subheading-md md:text-[38px] md:leading-10 font-bold text-gray-400 text-center md:text-left">
          Mari sehat mental bersama
        </span>
        <span className="text-body-md md:text-body-lg font-medium text-gray-300 text-center md:text-left">
          Sudah banyak Teman Healing yang memilih MentalHealing.id sebagai layanan psikologi terpercaya.
        </span>
      </div>
      <div className="flex flex-col gap-6 justify-center">
        <div className="flex flex-col gap-y-2 w-full bg-[#F8FAFC] rounded-lg p-8">
          <div className="flex flex-col justify-center items-center gap-y-4 w-full">
            <AvatarList
              size={37}
              itemClass={'w-[37px] h-[37px]'}
              shownItem={5}
              imageList={[
                '/images/home/<USER>',
                '/images/home/<USER>',
                '/images/home/<USER>',
                '/images/home/<USER>',
                '/images/home/<USER>',
              ]}
            />
            <span className="text-heading-sm font-bold text-gray-400">10.000+</span>
            <span className="text-body-lg text-gray-300 text-center">
              Teman healing menggunakan layanan kami
            </span>
          </div>
        </div>
        <div className="flex flex-col md:flex-row gap-6 w-full">
          <div className="flex flex-col justify-center items-center gap-y-4 bg-[#F8FAFC] rounded-lg p-8">
            <LoveIcon />
            <span className="text-heading-sm font-bold text-gray-400">99%</span>
            <span className="text-body-lg text-gray-300 text-center">
              Teman Healing terbantu setelah konseling.
            </span>
          </div>
          <div className="flex flex-col justify-center items-center gap-y-4 bg-[#F8FAFC] rounded-lg p-8">
            <StarIcon />
            <span className="text-heading-sm font-bold text-gray-400">4.9/5</span>
            <span className="text-body-lg text-gray-300 text-center">
              Teman Healing puas degan layanan kami.
            </span>
          </div>
        </div>
      </div>
    </div>
  )
}
