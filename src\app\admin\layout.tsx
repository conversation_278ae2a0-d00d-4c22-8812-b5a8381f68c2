'use client'
import ClickOutside from '@/components/_common/ClickOutside'
import { BottomNavigationMenu } from '@/components/navbar/BottomNavigationMenu'
import Navbar from '@/components/navbar/Navbar'
import Sidebar from '@/components/sidebar/Sidebar'
import { Routes } from '@/constans/routes'
import { AdminMenuItems, SuperAdminMenuItems } from '@/constans/SidebarMenu'
import { useSelector } from '@/store'
import { AuthRole } from '@/store/auth/auth.action'
import { useState } from 'react'

export default function AdminLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  const { user } = useSelector((state) => state.Authentication)
  const [sidebarOpen, setSidebarOpen] = useState<boolean>(false)
  const isSuperAdmin = user?.role === AuthRole.SUPERADMIN
  const isAdmin = user?.role === AuthRole.ADMIN

  const sidebarMenulist = isSuperAdmin ? SuperAdminMenuItems : isAdmin ? AdminMenuItems : []

  return (
    <>
      <ClickOutside className="sticky top-0 z-30" onClick={() => setSidebarOpen(false)}>
        <Navbar
          sidebarOpen={sidebarOpen}
          setSidebarOpen={setSidebarOpen}
          onShowAll={Routes.AdminNotification}
        />
        <Sidebar menuList={sidebarMenulist} sidebarOpen={sidebarOpen} setSidebarOpen={setSidebarOpen} />
      </ClickOutside>
      <div className="relative flex flex-1 flex-col lg:ml-sidebar">
        <div className="p-4 md:px-6 2xl:px-8">{children}</div>
      </div>
      <div className="mt-bottomUserMenu flex md:hidden">
        <BottomNavigationMenu menuList={AdminMenuItems} />
      </div>
    </>
  )
}
