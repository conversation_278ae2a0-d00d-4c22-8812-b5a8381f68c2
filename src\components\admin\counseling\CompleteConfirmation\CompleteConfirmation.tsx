import ButtonPrimary from '@/components/_common/ButtonPrimary'
import { AppBigText } from '@/components/_common/ui'
import { Routes } from '@/constans/routes'
import Link from 'next/link'

export default function CompleteConfirmationDialog({ item, onClose }: { item: any; onClose: () => void }) {
  return (
    <div className="grid grid-cols-1 grid-rows-1 gap-4">
      <AppBigText>
        Terima kasih sudah memberikan konseling untuk Klien Hilmi. Selanjutnya Anda perlu isi klien report ya.
      </AppBigText>

      <div className="flex flex-wrap justify-center sm:justify-end items-center gap-2">
        <ButtonPrimary
          onClick={() => onClose && onClose()}
          className="rounded-sm w-full sm:w-auto"
          variant="outlined"
          size="xs"
          color="gray"
        >
          Isi <PERSON>
        </ButtonPrimary>
        <Link href={`${Routes.PsychologistCreateClientReport}?id=${item.id}`}>
          <ButtonPrimary className="rounded-sm w-full sm:w-auto" variant="contained" size="xs">
            Isi Klien Report
          </ButtonPrimary>
        </Link>
      </div>
    </div>
  )
}
