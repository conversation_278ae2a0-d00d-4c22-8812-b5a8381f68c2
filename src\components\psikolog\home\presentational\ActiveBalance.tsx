import { IIcons, SVGIcons } from '@/components/_common/icon'
import { useGetPsychologistBalance } from '@/components/psikolog/income/hook/useGetPsychologistBalance.hook'
import { Routes } from '@/constans/routes'
import { formatStringToIDR } from '@/utils/currency'
import { useRouter } from 'next/navigation'

export default function ActiveBalance() {
  const router = useRouter()
  const { data } = useGetPsychologistBalance()
  const formatBalance = formatStringToIDR(data?.balance ?? 0)

  return (
    <>
      <div className="flex flex-col gap-[16px] bg-white rounded-[15px] border border-[#039EE9] p-[12px] md:p-[24px] w-[260px] md:w-auto h-[107px] md:h-auto">
        <div
          className="flex justify-between items-center cursor-pointer"
          onClick={() => router.push(Routes.PsychologistIncome)}
        >
          <p className="font-bold text-[14px] md:text-[16px] text-[#222222]">Saldo Aktif</p>
          <SVGIcons className="ml-2" name={IIcons.ArrowRight} />
        </div>
        <p className="font-bold text-[28px] md:text-[38px] text-[#039EE9]">{formatBalance}</p>
      </div>
    </>
  )
}
