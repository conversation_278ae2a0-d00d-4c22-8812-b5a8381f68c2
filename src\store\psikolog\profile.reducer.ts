import { PsychologistProfile, UserIdentity } from '@/interfaces/profile-service'
import { createSlice, PayloadAction } from '@reduxjs/toolkit'

const profileSlice = createSlice({
  name: 'psikolog-profile',
  initialState: {
    id: '',
    userIdentityId: '',
    fullName: '',
    nickname: '',
    bio: null,
    phoneNumber: '',
    birthDate: '',
    birthOrder: '',
    religion: '',
    maritalStatus: '',
    domicile: '',
    joinDate: '',
    endDate: null,
    gender: '',
    experience: 0,
    profilePhoto: '',
    video: null,
    counselingCount: 0,
    balance: 0,
    penalty: 0,
    occupation: null,
    workplace: null,
    sipp: '',
    str: null,
    offlineLocation: '',
    createdAt: '',
    createdBy: null,
    modifiedAt: '',
    modifiedBy: null,
    userIdentity: null,
    bankAccount: null,
    educationHistory: null,
    field: null,
    problemCategory: null,
    youtubeVideo: null,
    ethnicity: '',
    service: '',
  } as PsychologistProfile,
  reducers: {
    setProfilePsychologist(state, action: PayloadAction<PsychologistProfile>) {
      state = action.payload
      return state
    },
    setUserConfig(state, action: PayloadAction<UserIdentity['userConfig']>) {
      state.userIdentity = { ...((state.userIdentity ?? {}) as UserIdentity), userConfig: action.payload }
      return state
    },
    setTimezone(state, action: PayloadAction<string>) {
      state.userIdentity = {
        ...((state.userIdentity ?? {}) as UserIdentity),
        userConfig: { ...(state.userIdentity?.userConfig ?? {}), TIMEZONE: action.payload },
      }
      return state
    },
  },
})

export const { setProfilePsychologist, setUserConfig, setTimezone } = profileSlice.actions
export default profileSlice.reducer
