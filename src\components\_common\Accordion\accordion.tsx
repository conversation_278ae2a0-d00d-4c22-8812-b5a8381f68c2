'use client'
import React, { ReactNode, useState } from 'react'
import { IIcons, SVGIcons } from '@/components/_common/icon'

export default function Accordion({ header, content }: { header: string; content: string }) {
  const [accordionOpen, setAccordionOpen] = useState(false)

  return (
    <>
      <div
        onClick={() => setAccordionOpen(!accordionOpen)}
        className="bg-transparent cursor-pointer py-1 xl:py-0 relative z-20 border-b border-[#dadada]"
      >
        {/* Button accordion */}
        <div
          className={`flex justify-between items-center gap-1 w-full bg-transparent transition-all duration-500 ease-in-out ${accordionOpen ? 'py-2' : 'py-4'}`}
        >
          <span className="font-bold flex-1 text-wrap text-[#222222] text-[14px]">{header}</span>
          <SVGIcons
            className={`transition-all duration-500 ease-in-out w-6 ${accordionOpen ? 'rotate-0' : 'rotate-[180deg]'}`}
            name={IIcons.ArrowUp}
          />
        </div>
        {/* isi accordion */}
        <div
          className={`bg-transparent grid overflow-hidden transition-all duration-500 ease-in-out text-slate-600 ${accordionOpen ? 'grid-rows-[1fr] opacity-100 py-[8px] md:py-[1rem]' : 'grid-rows-[0fr] opacity-0 py-0'}`}
        >
          <div
            className={`text-[#535353] text-[12px] md:text-[14px] leading-[28px] overflow-hidden transition-all duration-500 delay-300 ease-in-out pl-4 ${accordionOpen ? 'opacity-100' : 'opacity-0'}`}
            dangerouslySetInnerHTML={{ __html: content }}
          ></div>
        </div>
      </div>
    </>
  )
}
