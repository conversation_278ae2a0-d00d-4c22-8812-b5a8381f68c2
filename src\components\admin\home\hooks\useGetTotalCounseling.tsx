import { dashboardService } from '@/services/dashboard.service'
import { useQuery } from '@tanstack/react-query'

export const useGetTotalCounseling = () => {
  return useQuery({
    queryKey: ['TotalCounselingCounter'],
    queryFn: () =>
      dashboardService
        .adminGetTotalCounselingCounter()
        .then((response) => response)
        .catch((error) => {
          console.log(error)
        }),
  })
}
