import { psychologistService } from '@/services/psychologist.service'
import { useState, useEffect } from 'react'
// Define types for our data structures
interface ProblemCategory {
  problemCategory: string
}

interface EducationHistory {
  major: string
  level: string
  university: string
  graduationYear?: string
}

interface BreakdownAvailability {
  durationInMinute: number
  price: number
}

interface YoutubeVideo {
  title?: string
  link: string
}

interface Psychologist {
  id: string
  fullName: string
  nickname?: string
  profilePhoto?: string
  field?: Array<{ name: string }>
  firstCareerDate?: string
  video?: string
  youtubeVideo?: YoutubeVideo[]
  problemCategory?: ProblemCategory[]
  educationHistory?: EducationHistory[]
  bio?: string
  sipp?: string
  testimonyCount: number
  overallRating?: number
  breakdownAvailability?: BreakdownAvailability[]
}

interface FormattedDate {
  day: string
  date: string
  month: string
  fullDate: string
}

export interface UserPsychologistDetailProps {
  psychologistId: string
}

export const useUserPsychologistDetails = (psychologistId: string) => {
  const [psychologist, setPsychologist] = useState<Psychologist | null>(null)
  const [loadingPsycho, setLoadingPsycho] = useState(true)
  const [errorPsycho, setErrorPsycho] = useState(null)

  const fetchPsychologist = async () => {
    try {
      setLoadingPsycho(true)
      const response = await psychologistService.getPsychologistForUserById(psychologistId)
      setPsychologist(response)
      setErrorPsycho(null)
    } catch (err) {
      setErrorPsycho(err as null)
      setPsychologist(null)
    } finally {
      setLoadingPsycho(false)
    }
  }

  useEffect(() => {
    if (psychologistId) {
      fetchPsychologist()
    }
  }, [psychologistId])

  return {
    psychologist,
    loadingPsycho,
    errorPsycho,
    refetch: fetchPsychologist,
  }
}
