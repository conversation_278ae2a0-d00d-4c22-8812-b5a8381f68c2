import { AuthRole } from '@/store/auth/auth.action'

export interface PsychologistProfile {
  id: string
  userIdentityId: string
  fullName: string
  nickname: string
  bio: string | null
  phoneNumber: string
  birthDate: string
  birthOrder: string
  religion: string
  maritalStatus: string
  domicile: string
  joinDate: string
  endDate: string | null
  gender: string
  experience: number
  profilePhoto: string
  video: string | null
  counselingCount: number
  balance: number
  penalty: number
  occupation: string | null
  workplace: string | null
  sipp: string
  str: string | null
  offlineLocation: string
  createdAt: string
  createdBy: string | null
  modifiedAt: string
  modifiedBy: string | null
  userIdentity: null | UserIdentity
  bankAccount: null | BankAccount[]
  field: null | any[] // Update later
  problemCategory: null | any[] // Update later
  youtubeVideo: null | any[] // Update later
  ethnicity: string
  service: string
  educationHistory: EducationHistory[] | null
}

export interface EducationHistory {
  id?: string
  level: string
  university: string
  graduationYear: Date | any
  major: string
  entryYear: Date | any
}

export interface AdminProfile {
  id: string
  userIdentity: null | UserIdentity
  fullName: string
  nickname: string
  phoneNumber: string
  profilePhoto: string
}

export interface BankAccount {
  id: string
  bankName: string
  bankAccount: string
  bankAccountName: string
  psychologistId: string
  clientId: string | null
  createdAt: string
  createdBy: string | null
  modifiedAt: string
  modifiedBy: string | null
}

export interface UserIdentity {
  id: string
  email: string
  password: string
  passwordSalt: string
  role: AuthRole
  firebaseUid: string
  isEmailVerified: boolean
  userConfig: {
    TIMEZONE: string
  }
  isActive?: boolean
}
