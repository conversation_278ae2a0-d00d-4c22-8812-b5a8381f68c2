// import { useToast } from '@/components/ui/use-toast'
import { clientReportService } from '@/services/clientReport.service'
import { useSelector } from '@/store'
import { AuthRole } from '@/store/auth/auth.action'
import { useQuery } from '@tanstack/react-query'

export const useGetClientReportList = (getAll?: boolean) => {
  const { user } = useSelector((state) => state.Authentication)
  return useQuery({
    queryKey: ['PsychologistClientReports', { getAll }],
    queryFn: () => {
      if (user?.role === AuthRole.PSIKOLOG) {
        if (getAll) {
          return clientReportService
            .getAllClientReport()
            .then((response) => {
              return {
                clientReport: response?.data,
                meta: response?.additionalMeta,
              }
            })
            .catch((error) => {
              // do soemthing
            })
        } else {
          return clientReportService
            .getIncomleteClientReport()
            .then((response) => {
              return {
                clientReport: response?.data,
                meta: response?.additionalMeta,
              }
            })
            .catch((error) => {
              // do soemthing
            })
        }
      }
    },
  })
}
