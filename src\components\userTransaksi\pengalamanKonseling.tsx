'use client'
import { IIcons, SVGIcons } from '../_common/icon'
import { AppModal } from '../_common/Modal/AppModal'
import { useState } from 'react'
import ButtonPrimary from '../_common/ButtonPrimary'

export default function PengalamanKonseling() {
    const [isOpenModal, setIsOpenModal] = useState<boolean>(false)
    const handleClickAccountInformation = () => {
        setIsOpenModal((prevIsOpenModal) => !prevIsOpenModal)
    }

    return (
        <>
            <div className="w-full bg-white border border-[#ebebeb] rounded-[15px] p-3">
                <div className="flex justify-between items-center">
                    <div className="flex flex-col gap-1">
                        <span className="text-[#222222] text-[14px] font-bold">Pengalaman Konselingmu</span>
                        <div className="flex items-center gap-1">
                            <SVGIcons name={IIcons.Star} className="w-6 h-6" />
                            <SVGIcons name={IIcons.Star} className="w-6 h-6" />
                            <SVGIcons name={IIcons.Star} className="w-6 h-6" />
                            <SVGIcons name={IIcons.Star} className="w-6 h-6" />
                            <SVGIcons name={IIcons.Star} className="w-6 h-6" />
                        </div>
                    </div>
                    <div className="flex gap-1 items-center">
                        <div onClick={handleClickAccountInformation} className="cursor-pointer">
                            <SVGIcons name={IIcons.Edit} className="w-6 h-6" />
                        </div>
                        <SVGIcons name={IIcons.ArrowDown} className="w-6 h-6" />
                    </div>
                </div>
            </div>

            {/* modal */}
            <AppModal
                className={`md:w-[600px] h-auto md:h-[95%] scrollbar-hide transition-all duration-500 ease-in-out ${isOpenModal ? 'opacity-100 scale-100' : 'opacity-0 scale-0'}`}
                open={isOpenModal}
                onClose={() => {
                handleClickAccountInformation()
                }}
                title={'Testimoni'}
                showOverlay={true}
            >
                <div className="w-full mt-[100px]">
                    <div className="flex flex-col items-center justify-center gap-2 w-full bg-[#E7F7FF] absolute left-0 top-[60px] py-8">
                        <span>Pengalaman Konselingmu</span>
                        <div className="flex items-center gap-1">
                            <SVGIcons name={IIcons.Star} className="w-6 h-6" />
                            <SVGIcons name={IIcons.Star} className="w-6 h-6" />
                            <SVGIcons name={IIcons.Star} className="w-6 h-6" />
                            <SVGIcons name={IIcons.Star} className="w-6 h-6" />
                            <SVGIcons name={IIcons.Star} className="w-6 h-6" />
                        </div>
                    </div>
                    <div className="flex flex-col gap-4">
                        <div className="flex flex-col gap-3">
                            <span>Testimoni</span>
                            <textarea
                                name=""
                                id=""
                                rows={5}
                                className="rounded-[15px] border border-[#ebebeb] p-4 focus:outline-[#039EE9]"
                            ></textarea>
                        </div>
                        <div className="flex flex-col gap-3 pb-1">
                            <span>Catatan untuk psikolog</span>
                            <textarea
                                name=""
                                id=""
                                rows={5}
                                className="rounded-[15px] border border-[#ebebeb] p-4 focus:outline-[#039EE9]"
                            ></textarea>
                        </div>
                    </div>
                    <span className="">Hanya ditampilkan ke Psikolog</span>
                    <div className="mt-10 flex justify-end">
                        <ButtonPrimary className="min-w-[143px] rounded-[15px]" variant={'contained'} size="base">
                        Simpan
                        </ButtonPrimary>
                    </div>
                </div>
            </AppModal>
        </>
    )
}
