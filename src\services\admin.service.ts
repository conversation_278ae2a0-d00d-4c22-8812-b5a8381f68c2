import { config } from '@/constans/config'
import { AdminStatus } from '@/constans/StaticOptions'
import { httpRequest } from '@/utils/network'

export class AdminService {
  async postAdmin(payload: FormData) {
    return await httpRequest({
      method: 'post',
      url: `${config?.apiBaseUrl}api/admin`,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      data: payload,
    })
  }

  async postAdminDisable(id: string) {
    return await httpRequest({
      method: 'post',
      url: `${config?.apiBaseUrl}api/user-identity/${id}/disable`,
    })
  }
  async postAdminEnable(id: string) {
    return await httpRequest({
      method: 'post',
      url: `${config?.apiBaseUrl}api/user-identity/${id}/enable`,
    })
  }

  async getAdminList(status: string, searchName: string) {
    const statusQuery = status === AdminStatus.ALL ? '' : `"userIdentity":{"isActive": ${status}},`
    const searchNameQuery = searchName !== '' ? `"${searchName}"` : '""'
    const urlEndpoint = `${config?.apiBaseUrl}api/admin?where={${statusQuery} "fullName":{"contains":${searchNameQuery}}}&page=1&perPage=100`
    return await httpRequest({
      method: 'get',
      url: urlEndpoint,
    })
  }
}

export const adminService = new AdminService()
