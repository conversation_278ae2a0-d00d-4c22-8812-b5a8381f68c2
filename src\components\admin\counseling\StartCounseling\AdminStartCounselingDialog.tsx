import ButtonPrimary from '@/components/_common/ButtonPrimary'
import { AppBigText } from '@/components/_common/ui'
import { useToast } from '@/components/ui/use-toast'
import Link from 'next/link'

export default function AdminStartCounselingDialog({ item, onClose }: { item: any; onClose: () => void }) {
  const { toast } = useToast()

  const handleClickStartCounseling = async () => {
    try {
      // await counsellingService.startCounselling(item.id)
      toast({
        title: 'Berhasil',
        description: 'Tunggu beberapa saat, anda akan diarahkan ke halaman konseling.',
        variant: 'success',
      })
      onClose && onClose()
      setTimeout(() => {
        window.open(item.googleMeetUrl, '_blank')
      }, 3000)
    } catch (error) {
      toast({
        title: 'Terjadi Masalah Jaringan',
        description: 'Terjadi masalah pada server, silahkan hubungi admin untuk bantuan.',
        variant: 'danger',
      })
    }
  }

  return (
    <>
      <div className="grid grid-cols-1 grid-rows-1 gap-4">
        <AppBigText>
          Dengan klik mulai konseling, artinya Anda sudah siap untuk menjalankan sesi konseling dengan klien.
        </AppBigText>
        <div className="grid gap-2">
          <AppBigText>
            Setelah sesinya selesai, pastikan Anda klik <span className="font-bold">Sesi Sudah Selesai</span>,
            dan isi <span className="font-bold">Klien Report</span>.
          </AppBigText>
        </div>
        <div className="grid gap-2 p-4 border border-line-200 rounded-card">
          <AppBigText>Link Google Meet:</AppBigText>
          <Link className="text-[#039EE9]" href={item.googleMeetUrl ?? '/'} target="_blank">
            {item.googleMeetUrl}
          </Link>
        </div>
        <div className="flex flex-wrap justify-center sm:justify-end items-center gap-2 pt-6">
          <ButtonPrimary
            onClick={() => onClose && onClose()}
            className="rounded-sm w-full sm:w-auto"
            variant="outlined"
            size="xs"
            color="gray"
          >
            Kembali
          </ButtonPrimary>
          <ButtonPrimary
            onClick={() => handleClickStartCounseling()}
            className="rounded-sm w-full sm:w-auto"
            variant="contained"
            size="xs"
          >
            Yuk, Mulai Konseling
          </ButtonPrimary>
        </div>
      </div>
    </>
  )
}
