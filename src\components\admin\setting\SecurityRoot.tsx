import { FormSetting } from './FormSetting'
import { useForm } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import * as yup from 'yup'
import { useToast } from '@/components/ui/use-toast'
import { useEffect, useState } from 'react'
import { AdminProfile } from '@/interfaces/profile-service'
import { profileService } from '@/services/profile.service'
import AppInput from '@/components/_common/input/Input'
import Translation from '@/constans/Translation'
import { useDispatch } from '@/store'
import { logout } from '@/store/auth/auth.reducer'
import { useRouter } from 'next/navigation'

const validationSchema = yup.object().shape({
  email: yup.string().nullable(),
  oldPassword: yup.string().required(Translation.PasswordRequired),
  password: yup
    .string()
    .min(6, Translation.PasswordMinChar)
    .matches(/[A-Z]/, Translation.RequiredUppercase)
    .matches(/[a-z]/, Translation.RequiredLowercase)
    .matches(/\d/, Translation.RequiredNumber)
    .required(Translation.PasswordRequired),
  rePassword: yup.string().oneOf([yup.ref('password')], Translation.RepasswordMustMatch),
})

const SecurityRoot = ({ userIdentity }: AdminProfile) => {
  const router = useRouter()
  const dispatch = useDispatch()
  const { toast } = useToast()
  const [isLoadingForm, setIsLoadingForm] = useState<boolean>(false)
  const [fieldEdit, setFieldEdit] = useState<string | null>(null)

  const {
    register,
    handleSubmit,
    trigger,
    getValues,
    setValue,
    reset,
    resetField,
    formState: { errors, isLoading, isSubmitting },
  } = useForm({
    resolver: yupResolver(validationSchema),
    defaultValues: {
      email: '',
      oldPassword: '',
      password: '',
      rePassword: '',
    },
  })

  useEffect(() => {
    reset({
      email: userIdentity?.email ?? '',
      oldPassword: '',
      password: '',
      rePassword: '',
    })
  }, [reset, userIdentity?.email])

  const handleOnSubmit = async (key: string) => {
    setIsLoadingForm(true)
    const isValid = await trigger(
      key === 'password' ? ['oldPassword', 'password', 'rePassword'] : (key as any)
    )
    if (isValid) {
      try {
        const payload = {
          password: getValues('oldPassword'),
          newPassword: getValues('password'),
        }
        await profileService.patchChangePassword(payload)
        toast({
          variant: 'success',
          title: 'Perbaharui data profil berhasil',
        })
        setIsLoadingForm(false)
        setFieldEdit(null)
        dispatch(logout())
        router.refresh()
      } catch (error: any) {
        console.log(error)
        toast({
          variant: 'danger',
          title: error?.response?.data?.message ?? 'Perbaharui data profil gagal',
        })
        setIsLoadingForm(false)
        setFieldEdit(null)
      }
    } else {
      setIsLoadingForm(false)
    }
  }

  return (
    <div className="grid gap-y-6">
      <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
        <div className="col-span-2">
          <FormSetting
            viewComponent={getValues('email') ?? ''}
            editComponent={
              <>
                {getValues('email') ?? ''}
                <AppInput
                  {...register('email')}
                  className="pt-0"
                  type="text"
                  onChange={(val) => {
                    setValue('email', val.target.value, { shouldValidate: true })
                  }}
                  name="email"
                  label="Email"
                  errorMsg={!!errors.email ? String(errors.email.message) : undefined}
                  placeholder="email"
                />
              </>
            }
            label="Email"
            isEdit={fieldEdit === 'email'}
            disableEdit={true}
            isLoading={isLoadingForm}
            onEditButton={() => setFieldEdit('email')}
            onCancel={() => {
              setFieldEdit(null)
              resetField('email')
            }}
            onSubmit={() => handleOnSubmit('email')}
          />
          <FormSetting
            viewComponent={
              <div className="flex gap-2 items-center py-2">
                <span className="rounded-full h-2 w-2 bg-line-200"></span>
                <span className="rounded-full h-2 w-2 bg-line-200"></span>
                <span className="rounded-full h-2 w-2 bg-line-200"></span>
                <span className="rounded-full h-2 w-2 bg-line-200"></span>
                <span className="rounded-full h-2 w-2 bg-line-200"></span>
                <span className="rounded-full h-2 w-2 bg-line-200"></span>
              </div>
            }
            editComponent={
              <div className="grid gap-2">
                <AppInput
                  {...register('oldPassword')}
                  className="pt-0"
                  type="password"
                  onChange={(val) => {
                    setValue('oldPassword', val.target.value, { shouldValidate: true })
                  }}
                  name="oldPassword"
                  label="Password Lama"
                  errorMsg={!!errors.oldPassword ? String(errors.oldPassword.message) : undefined}
                />
                <AppInput
                  {...register('password')}
                  className="pt-0"
                  type="password"
                  onChange={(val) => {
                    setValue('password', val.target.value, { shouldValidate: true })
                  }}
                  name="password"
                  label="Password"
                  errorMsg={!!errors.password ? String(errors.password.message) : undefined}
                />
                <AppInput
                  {...register('rePassword')}
                  className="pt-0"
                  label="Ulangi Password"
                  type="password"
                  name="rePassword"
                  onChange={(val) => {
                    setValue('rePassword', val.target.value, { shouldValidate: true })
                  }}
                  errorMsg={!!errors.rePassword ? errors.rePassword.message : undefined}
                />
              </div>
            }
            label="Password"
            isEdit={fieldEdit === 'password'}
            isLoading={isLoadingForm}
            onEditButton={() => setFieldEdit('password')}
            onCancel={() => {
              setFieldEdit(null)
              resetField('password')
            }}
            onSubmit={() => handleOnSubmit('password')}
          />
        </div>
      </div>
    </div>
  )
}

export default SecurityRoot
