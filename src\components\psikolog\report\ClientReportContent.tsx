import { LabelValue } from '@/components/_common/CardInfo/LabelValue'
import { ClientReportItem } from './ClientReportItem'
import {
  formatStringToDateOutput,
  formatStringToFullDateTimeOutput,
  formatStringToStartEndTimeOutput,
} from '@/utils/displayDate'
import moment from 'moment'
import useGetPsychologistTimezone from '@/hooks/useGetPsychologistTimezone.hook'
import useGetTimezoneLabel from '@/hooks/useGetTimezone.hook'
import { format } from 'path'

export const ClientReportItemContent = ({ clientReport, complaint = '' }: ClientReportItem) => {
  const {
    anamnesis = '',
    intervention = '',
    task = '',
    notesForClient = '',
    assessmentFile = '',
    createdAt = '',
    nextCounselingAnswer = '',
    nextCounselingDate = '',
    nextCounselingDuration = '',
  } = clientReport || {}

  const psychologistTimezone = useGetPsychologistTimezone()
  const timeZoneLabel = useGetTimezoneLabel()

  // const dateLabel = formatStringToTimeOutput(counselingDetails?.startTime)
  const nextCounseling = formatStringToStartEndTimeOutput({
    date: nextCounselingDate,
    duration: Number(nextCounselingDuration),
    timezone: psychologistTimezone,
    timeLabel: timeZoneLabel,
    isUTC: true,
  })

  const nextTimeCounseling = nextCounselingAnswer ? nextCounseling : '-'
  const nextCounselingDateTime = nextCounselingAnswer
    ? `${formatStringToDateOutput(nextCounselingDate)}, ${nextTimeCounseling}`
    : '-'
  const createdAtDate = formatStringToFullDateTimeOutput({
    date: createdAt,
    timezone: psychologistTimezone,
    timeLabel: timeZoneLabel,
  })
  return (
    <div className="grid grid-cols-3 grid-rows-1 gap-y-4">
      <span className="col-span-3">
        <LabelValue
          labelClass="text-body-sm font-medium text-gray-300"
          valueClass="font-bold text-gray-400"
          label="Anamnesa"
          value={anamnesis}
        />
      </span>
      <span className="col-span-3">
        <LabelValue
          labelClass="text-body-sm font-medium text-gray-300"
          valueClass="font-bold text-gray-400"
          label="Intervensi"
          value={intervention}
        />
      </span>
      <span className="col-span-3">
        <LabelValue
          labelClass="text-body-sm font-medium text-gray-300"
          valueClass="font-bold text-gray-400"
          label="Tugas"
          value={task}
        />
      </span>
      <span className="col-span-3">
        <LabelValue
          labelClass="text-body-sm font-medium text-gray-300"
          valueClass="font-bold text-gray-400"
          label="Catatan untuk Klien"
          value={notesForClient}
        />
      </span>
      {/* <span className="col-span-3">
        <LabelValue
          labelClass="text-body-sm font-medium text-gray-300"
          valueClass="font-bold text-gray-400"
          label="Assesment File"
          value={assessmentFile}
        />
      </span> */}
      <span className="col-span-3">
        <LabelValue
          displayRows
          labelClass="text-body-sm font-medium text-gray-300"
          valueClass="font-bold text-gray-400"
          label="Detail Keluhan"
          value={complaint}
        />
      </span>
      <span className="col-span-3">
        <LabelValue
          displayRows
          labelClass="text-body-sm font-medium text-gray-300"
          valueClass="font-bold text-gray-400"
          label="Jadwal Konseling Berikutnya"
          value={nextCounselingDateTime}
        />
      </span>
      <LabelValue
        displayRows
        labelClass="text-body-sm font-medium text-gray-300"
        valueClass="font-bold text-gray-400"
        label={`Jadwal dibuat: ${createdAtDate}`}
        value=""
      />
    </div>
  )
}
