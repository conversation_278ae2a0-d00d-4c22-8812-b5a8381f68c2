import React, { forwardRef } from 'react'
import { cn } from '@/lib/utils'
import { IIcons, SVGIcons } from '../icon'
import {
  Sheet,
  SheetClose,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from '@/components/ui/sheet'

export type AppModalProps = {
  open: boolean
  onClose: (arg?: any) => void
  title?: string | React.ReactNode
  children: React.ReactNode
  footer?: React.ReactNode
  className?: string
  [key: string]: any
}

export const AppSheet = forwardRef<any, any>(function AppSheet(
  { title, open, onClose, className, children, side = 'bottom', ...props },
  ref
) {
  const classByside = side === 'bottom' ? 'overflow-y-auto rounded-t-2xl' : 'overflow-y-auto'
  return (
    <Sheet open={open}>
      <SheetContent
        side={side}
        overlay={side === 'right' ? false : true}
        onClose={onClose}
        className={cn(classByside, className)}
        {...props}
      >
        <SheetHeader className="flex relative">
          <SheetTitle className="text-left text-subheading-md font-bold text-gray-400 mr-6">
            {title}
          </SheetTitle>
          <SheetClose onClick={onClose && onClose} className="absolute right-0 !mt-0 test123">
            <SVGIcons name={IIcons.Close} />
          </SheetClose>
          <SheetDescription className="hidden"></SheetDescription>
        </SheetHeader>
        {children}
      </SheetContent>
    </Sheet>
  )
})
