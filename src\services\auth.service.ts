import { config } from '@/constans/config'
import { httpRequest } from '@/utils/network'

export type OnboardClient = {
  password: string
  fullName?: string
  nickname?: string
  birthDate?: string
  gender?: string
  phoneNumber?: string
  birthOrder?: string
  religion?: string
  ethnicity?: string
  domicile?: string
  maritalStatus?: string
  education?: string
  occupation?: string
  joinDate?: string
  endDate?: string
  profilePhoto?: string
  bankName?: string
  bankAccount?: string
  bankAccountName?: string
}

export class AuthService {
  async onboardingAuthPost(payload: OnboardClient) {
    return await httpRequest({
      method: 'post',
      url: `${config?.apiBaseUrl}api/auth/onboarding/client`,
      data: payload,
    })
  }
  async onboardingAuth<PERSON>ostPsychologist(payload: OnboardClient) {
    return await httpRequest({
      method: 'post',
      url: `${config?.apiBaseUrl}api/auth/onboarding/psychologist`,
      data: payload,
    })
  }

  async verifyingEmail(email: string) {
    return await httpRequest({
      method: 'get',
      url: `${config?.apiBaseUrl}api/auth/check-email/?email=${email}`,
    })
  }

  async signinWithEmail(email: string) {
    return await httpRequest({
      method: 'post',
      url: `${config?.apiBaseUrl}api/auth/sign-with-email`,
      data: { email },
    })
  }

  async forgotPassword(email: string) {
    return await httpRequest({
      method: 'post',
      url: `${config?.apiBaseUrl}api/auth/forgot-password`,
      data: { email },
    })
  }

  async resetPassword(password: string, token: string) {
    return await httpRequest({
      method: 'post',
      url: `${config?.apiBaseUrl}api/auth/reset-password?token=${token}`,
      data: { password },
    })
  }
}

export const authService = new AuthService()
