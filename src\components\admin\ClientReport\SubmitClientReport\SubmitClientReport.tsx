'use client'
import { ListInformation } from '@/components/_common/ListInformation'
import { HeaderContent } from '@/components/admin/HeaderContent'
import { useRouter, useSearchParams } from 'next/navigation'
import { IIcons } from '@/components/_common/icon'
import AppInput from '@/components/_common/input/Input'
import ButtonPrimary from '@/components/_common/ButtonPrimary'
import { Switch } from '@/components/ui/switch'
import { useEffect, useState } from 'react'
import { Separator } from '@/components/ui/separator'
import { useToast } from '@/components/ui/use-toast'
import { Controller, useForm } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import * as yup from 'yup'
import { MOMENT_INPUT_DATE_FORMAT } from '@/constans/date'
import { counsellingService } from '@/services/counselling.service'
import { clientReportService } from '@/services/clientReport.service'
import moment from 'moment'
import { useGetClientReport } from '../hook/useGetClientReport.hook'
import { psychologistService } from '@/services/psychologist.service'
import { WeeklyCalendarPickerCustom } from '@/components/_common/WeeklyDatePicker/WeeklyCalendarCustom'

const validationSchema = yup.object().shape({
  anamnesis: yup.string().nullable(),
  intervention: yup.string().nullable(),
  notesForClient: yup.string().nullable(),
  task: yup.string().nullable(),
})

export const SubmitClientReportComponent = () => {
  const { toast } = useToast()
  const router = useRouter()
  const searchParam = useSearchParams()
  const clientName = searchParam.get('name')
  const reportId = searchParam.get('reportId')
  const [date, setDate] = useState<Date | undefined>(new Date())
  const [isChecked, setIsChecked] = useState<boolean>(false)
  const [selectedTime, setSelectedTime] = useState<string | null>('')
  const [selectedDuration, setSelectedDuration] = useState<number>(60)
  const [isEdit, setIsEdit] = useState<boolean>(false)
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [availableDates, setAvailableDates] = useState<Date[]>([])
  const [schedules, setSchedules] = useState<any[]>([])

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors, isLoading: isLoadingForm, isSubmitting },
  } = useForm({
    resolver: yupResolver(validationSchema),
    defaultValues: {
      anamnesis: '',
      intervention: '',
      notesForClient: '',
      task: '',
    },
  })

  const { data: dataClientReport } = useGetClientReport(reportId || '')

  if (availableDates.length > 0) console.log(availableDates)

  useEffect(() => {
    const fetchAvailability = async () => {
      try {
        const dates = await psychologistService.adminGetPsychologistDateAvailability(
          dataClientReport?.psychologist.id
        )
        const formattedDates = dates.map((date: string) => new Date(date)) // Konversi string ke Date
        setAvailableDates(formattedDates)
      } catch (error) {
        console.error('Error fetching psychologist availability:', error)
      }
    }

    if (dataClientReport?.psychologist.id !== undefined) {
      fetchAvailability()
    }
  }, [dataClientReport?.psychologist.id])

  useEffect(() => {
    if (dataClientReport) {
      reset({
        anamnesis: dataClientReport?.anamnesis,
        intervention: dataClientReport?.intervention,
        notesForClient: dataClientReport?.notesForClient,
        task: dataClientReport?.task,
      })

      const nextDate = dataClientReport?.nextCounselingDate
        ? moment(dataClientReport?.nextCounselingDate).toDate()
        : moment().add(1, 'days').toDate()
      const nextTime = dataClientReport?.nextCounselingDate
        ? moment(dataClientReport?.nextCounselingDate).format('HH:mm')
        : null
      setIsEdit(true)
      setIsChecked(dataClientReport?.nextCounselingAnswer)
      setDate(nextDate)
      setSelectedTime(nextTime)
      setSelectedDuration(dataClientReport?.nextCounselingDuration ?? 60)
    }
  }, [dataClientReport, reset])

  useEffect(() => {
    const fetchSchedule = async () => {
      if (dataClientReport?.psychologist.id !== undefined && date) {
        try {
          const schedule = await counsellingService.getPsychologistScheduleBydate(
            dataClientReport?.psychologist.id,
            moment(date).format('YYYY-MM-DD')
          )

          setSchedules(schedule || [])
        } catch (error) {
          console.error('Error fetching schedule:', error)
        }
      }
    }

    fetchSchedule()
  }, [dataClientReport?.psychologist.id, date])

  async function onSubmit(data: any) {
    setIsLoading(true)
    try {
      const formData = new FormData()
      for (let val in data) {
        formData.append(val, data[val])
      }
      if (isChecked) {
        if (date && selectedTime && selectedDuration) {
          const payloadNextDate = moment(date).format(MOMENT_INPUT_DATE_FORMAT)
          const payloadNextDateTime = moment(`${payloadNextDate} ${selectedTime}`).toISOString()
          console.log(payloadNextDateTime)
          formData.append('nextCounselingAnswer', true as any)
          formData.append('nextCounselingDate', payloadNextDateTime as any)
          formData.append('nextCounselingDuration', selectedDuration as any)
        } else {
          toast({
            variant: 'danger',
            title: 'Lengkapi data jadwal sesi lanjutan terlebih dahulu.',
          })
          setIsLoading(false)
          return
        }
      } else {
        formData.append('nextCounselingAnswer', false as any)
        formData.append('nextCounselingDate', null as any)
        formData.append('nextCounselingDuration', null as any)
      }
      await clientReportService.adminUpdateClientReport(formData, reportId || '')
      toast({
        variant: 'success',
        title: 'Klien report berhasil disimpan',
      })
      setTimeout(() => {
        setIsLoading(false)
        router.back()
      }, 1000)
    } catch (error) {
      toast({
        variant: 'danger',
        title: 'Klien report gagal disimpan.',
      })
      setTimeout(() => {
        setIsLoading(false)
        router.back()
      }, 1000)
    }
  }

  return (
    <>
      <HeaderContent className="mb-1" title={`Tulis Klien Report untuk ${clientName}`} />
      <div className="flex mb-4">
        <span>Sesi konseling: </span>
        <ListInformation
          className="border-b-0 py-0 ml-2"
          listItem={[
            { label: 'Senin, 1 Agustus 2024', icon: IIcons.Calendar },
            { label: '13.00 - 14.00 WIB', icon: IIcons.Time },
            { label: 'Call', icon: IIcons.Call },
          ]}
        />
      </div>
      <div className="grid grid-cols-3 grid-rows-1">
        <div className="grid col-span-2 gap-y-4">
          <>
            <Controller
              control={control}
              render={({ field }) => (
                <AppInput {...field} label="Keluhan/Anamnesa Klien" rows={3} type="textarea" maxChar={3000} />
              )}
              name="anamnesis"
              defaultValue=""
            />
            <Controller
              control={control}
              render={({ field }) => (
                <AppInput {...field} label="Penanganan/Intervensi" rows={3} type="textarea" maxChar={3000} />
              )}
              name="intervention"
              defaultValue=""
            />
            <Controller
              control={control}
              render={({ field }) => (
                <AppInput
                  {...field}
                  note="Catatan ini akan ditampilkan ke Klien"
                  label="Catatan untuk Klien"
                  rows={3}
                  type="textarea"
                  maxChar={3000}
                />
              )}
              name="notesForClient"
              defaultValue=""
            />
            <Controller
              control={control}
              render={({ field }) => (
                <AppInput
                  {...field}
                  note="Tugas ini akan ditampilkan ke Klien"
                  label="Tugas yang Diberikan"
                  rows={3}
                  type="textarea"
                  maxChar={3000}
                />
              )}
              name="task"
              defaultValue=""
            />

            <div className={`form-container grid gap-[8px] border-t border-line-200 mt-2`}>
              <div className={`relative`}>
                <div className="flex flex-col items-start input-form w-full">
                  <div className="flex flex-row items-center justify-between py-2">
                    <div className="mr-4">
                      <Switch checked={isChecked} onCheckedChange={() => setIsChecked((prev) => !prev)} />
                    </div>
                    <div className="flex flex-col space-y-0.5">
                      <label className="text-body-lg font-bold text-gray-400">Jadwalkan sesi lanjutan</label>
                      <span className="text-caption-md font-medium text-gray-300">
                        Klien dapat langsung proses booking sesuai dengan jadwal rekomendasi Anda.
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            {isChecked && (
              <div className="grid gap-2">
                <div className={`form-container grid gap-[8px]`}>
                  <label htmlFor={'weekly-date'} className="text-body-md font-bold text-gray-400">
                    Durasi Konseling
                  </label>
                  <div className={`relative`}>
                    <div className="flex flex-col items-start input-form w-full">
                      <div className="flex flex-row items-center justify-between gap-2">
                        {[
                          { label: '60 Menit', value: 60 },
                          { label: '120 Menit', value: 120 },
                        ].map((time, index) => {
                          return (
                            <ButtonPrimary
                              className={`rounded-sm ${selectedDuration === time.value && 'bg-[#039EE9]/5'}`}
                              textSize="text-body-md"
                              textColor="text-gray-400"
                              key={index}
                              variant="outlined"
                              size="xs"
                              onClick={() => setSelectedDuration(time.value)}
                            >
                              {time.label}
                            </ButtonPrimary>
                          )
                        })}
                      </div>
                    </div>
                  </div>
                </div>
                <div className={`form-container grid gap-[8px]`}>
                  <label htmlFor={'weekly-date'} className="text-body-md font-bold text-gray-400">
                    Tanggal
                  </label>
                  <div className={`relative`}>
                    <div className="flex flex-col items-start input-form w-full">
                      <div className="flex flex-row items-center justify-between">
                        <WeeklyCalendarPickerCustom
                          date={date}
                          onSelect={setDate}
                          availableDates={availableDates}
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <div className={`form-container grid gap-[8px]`}>
                  <label htmlFor={'weekly-date'} className="text-body-md font-bold text-gray-400">
                    Waktu
                  </label>
                  <div className={`relative`}>
                    <div className="flex flex-col items-start input-form w-full">
                      <div className="flex flex-col gap-2">
                        {schedules
                          .filter((data: any) =>
                            selectedDuration ? data.durationInMinute === selectedDuration : true
                          )
                          .map((data) =>
                            data.schedule?.map((schedule: any) => (
                              <div key={schedule.date} className="flex flex-wrap gap-2 mt-1">
                                {schedule.times?.map((times: any) => (
                                  <ButtonPrimary
                                    className={`rounded-sm ${
                                      selectedTime === times.time ? 'bg-main-100/10' : ''
                                    }`}
                                    textSize="text-body-md"
                                    textColor="text-gray-400"
                                    key={times.time}
                                    variant="outlined"
                                    size="xs"
                                    type="button"
                                    onClick={() => setSelectedTime(times.time)}
                                  >
                                    {times.time}
                                  </ButtonPrimary>
                                ))}
                              </div>
                            ))
                          )}
                      </div>
                    </div>
                  </div>
                  {!schedules && (
                    <span className="text-caption-md font-medium text-gray-300 py-2">
                      Tidak menemukan waktu yang tersedia disini? Pastikan Anda sudah atur Jadwal Ketersediaan
                      dulu ya!
                    </span>
                  )}
                </div>
              </div>
            )}
            <Separator orientation="horizontal" />
            <div className="flex justify-end gap-4 mt-4">
              <ButtonPrimary
                className="min-w-[140px]"
                variant="outlined"
                size="xs"
                color="gray"
                type="button"
                onClick={() => router.back()}
                disabled={isLoading || isSubmitting || isLoadingForm}
              >
                Batal
              </ButtonPrimary>
              <ButtonPrimary
                className="min-w-[140px]"
                variant="contained"
                size="xs"
                type="button"
                onClick={() => handleSubmit(onSubmit)()}
                isLoading={isLoading || isSubmitting || isLoadingForm}
                disabled={isLoading || isSubmitting || isLoadingForm}
              >
                Simpan
              </ButtonPrimary>
            </div>
          </>
        </div>
      </div>
    </>
  )
}
