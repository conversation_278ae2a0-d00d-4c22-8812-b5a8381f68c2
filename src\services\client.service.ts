import { config } from '@/constans/config'
import { httpRequest } from '@/utils/network'

export class ClientsService {
  async getClientsDetails(id: string) {
    return await httpRequest({
      method: 'get',
      url: `${config?.apiBaseUrl}api/clients/${id}`,
    })
  }

  async postClient(payload: FormData) {
    return await httpRequest({
      method: 'post',
      url: `${config?.apiBaseUrl}api/clients`,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      data: payload,
    })
  }
}

export const clientsService = new ClientsService()
