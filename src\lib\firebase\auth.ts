import {
  type User,
  GoogleAuthProvider,
  signInWithPopup,
  signInWithCustomToken,
  onAuthStateChanged as _onAuthStateChanged,
} from 'firebase/auth'

import { firebaseAuth } from './config'

export function onAuthStateChanged(callback: (authUser: User | null) => void) {
  return _onAuthStateChanged(firebaseAuth, callback)
}

export async function signInWithGoogle() {
  const provider = new GoogleAuthProvider()

  try {
    const result = await signInWithPopup(firebaseAuth, provider)

    if (!result || !result.user) {
      throw new Error('Google sign in failed')
    }
    const user = result.user
    const providerId = user.providerId
    const token = await user.getIdToken()
    return { token, providerId, user }
  } catch (error) {
    console.error('Error signing in with Google', error)
  }
}

export async function refreshToken(token: string) {
  const customToken = token
  try {
    const result = await signInWithCustomToken(firebaseAuth, customToken)

    if (!result || !result.user) {
      throw new Error('Sign in with custom token failed')
    }

    // const user = result.user
    // const providerId = user.providerId
    // const token = await user.getIdToken()
    return { token: customToken }
  } catch (error) {
    console.error('Error refresh custom token', error)
  }
}

export async function signOutWithGoogle() {
  try {
    await firebaseAuth.signOut()
  } catch (error) {
    console.error('Error signing out with Google', error)
  }
}
