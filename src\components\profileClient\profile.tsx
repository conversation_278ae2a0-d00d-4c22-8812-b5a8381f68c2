'use client'

import { Header<PERSON>ontent } from '@/components/admin/HeaderContent'
import CardSetting from '@/components/profileClient/cardSetting'
import SearchPsikolog from '@/components/profileClient/searchPsikolog'
import Banner from './banner'
import CardSettingsMobile from './cardSettingsMobile'
import Link from 'next/link'
import ButtonPrimary from '../_common/ButtonPrimary'
import { Routes } from '@/constans/routes'
import MobileSosmed from './mobileSosmed'
import { IIcons } from '../_common/icon'
import { useState, useEffect } from 'react'
import { useProfileInformation } from './hook/useClientInformationProfile'

export default function Profile() {
  const { profileData, loading, error } = useProfileInformation()
  const [phoneNumber, setPhoneNumber] = useState<number | null>(null)

  useEffect(() => {
    if (profileData?.phoneNumber) {
      try {
        setPhoneNumber(parseInt(profileData.phoneNumber, 10))
      } catch (error) {
        console.error('Error parsing phone number:', error)
      }
    }
  }, [profileData])

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <p>Loading profile information...</p>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <p className="text-red-500">Error loading profile: {error.message}</p>
      </div>
    )
  }

  return (
    <>
      <div className="lg:mt-[64px] pt-0 md:pt-4 lg:pt-0 px-0 md:px-4 lg:px-0 lg:w-[930px] xl:w-[1120px] max-w-[1120px] flex flex-col justify-center gap-0 md:gap-6 w-full">
        {/* header */}
        <HeaderContent className="mb-0 hidden sm:hidden md:flex" title="Akun" />
        {/* banner */}
        <Banner
          name={profileData?.fullName || ''}
          email={profileData?.userIdentity?.email || ''}
          noHp={phoneNumber || 0}
        />
        {/* mobile settings */}
        <div className="flex md:hidden flex-col py-6 px-4 bg-white gap-6 -mt-5 relative z-10 rounded-t-[15px]">
          <CardSettingsMobile
            routeInfo={Routes.UserPersonalInfo}
            routeSetting={Routes.UserSetting}
            routePusat={Routes.UserPusatBantuan}
            routeKebijakan={Routes.UserKebijakanPrivasi}
            routeSyarat={Routes.UserSyaratDanKetentuan}
            header="Pengaturan"
            title="Informasi Personal"
            title2="Pengaturan"
            title3=""
            icon="User"
            icon2="Setting"
            icon3="User"
            style="hidden"
          />
          <CardSettingsMobile
            routeInfo={Routes.UserPersonalInfo}
            routeSetting={Routes.UserSetting}
            routePusat={Routes.UserPusatBantuan}
            routeKebijakan={Routes.UserKebijakanPrivasi}
            routeSyarat={Routes.UserSyaratDanKetentuan}
            header="Bantuan"
            title="Pusat Bantuan"
            title2="Kebijakan Privasi"
            title3="Syarat dan Ketentuan"
            icon="Question"
            icon2="Document"
            icon3="Document"
            style="flex"
          />
        </div>
        {/* mobile sosmed */}
        <div id="sosmedProfile" className="md:hidden flex flex-col gap-4 pl-4">
          <h4 className="font-bold text-[16px] text-[#1F282D]">Follow media sosial kami</h4>
          <div className="w-full overflow-x-scroll">
            <div id="sosmedBtn" className="flex gap-4 items-center w-[425px]">
              <MobileSosmed icon="/icons/instagram.svg" iconName="Instagram" />
              <MobileSosmed icon="/icons/twit.svg" iconName="Twitter" />
              <MobileSosmed icon="/icons/fb.svg" iconName="Facebook" />
            </div>
          </div>
        </div>
        {/* made with love */}
        <span className="text-[#737373] text-center text-body-sm font-medium py-6 px-4 block md:hidden">
          💙 Made with love by{' '}
          <Link className="underline" href={'https://www.likearth.co'} target="_blank" passHref={true}>
            Mentalhealing.id Team
          </Link>
        </span>
        {/* exit btn mobile */}
        <div className="px-4 mb-[150px] w-full block md:hidden">
          <ButtonPrimary className="w-full rounded-[15px]" color="gray" variant={'outlined'} size="sm">
            Keluar
          </ButtonPrimary>
        </div>
        {/* card Settings */}
        <div className="flex flex-col w-full gap-6 overflow-hidden">
          <div className="hidden md:flex flex-wrap gap-6 pb-6 border-b border-[#EBEBEB]">
            {/* card */}
            <CardSetting
              icon={IIcons.User}
              title="Informasi Personal"
              desc="Atur nama, tempat tanggal lahir, jenis kelamin, domisili, status menikah, informasi rekening."
              route={Routes.UserPersonalInfo}
            />
            <CardSetting
              icon={IIcons.Setting}
              title="Pengaturan"
              desc="Login info seperti email, nomor HP, dan Password. PIN keamanan, notifikasi."
              route={Routes.UserSetting}
            />
          </div>
          <div className="hidden md:flex flex-wrap lg:flex-nowrap gap-6">
            {/* card */}
            <CardSetting
              icon={IIcons.Question}
              title="Pusat Bantuan"
              desc="Temukan jawaban atas pertanyaan kamu di sini. Kami pun selalu siap membantu kamu."
              route={Routes.UserPusatBantuan}
            />
            <CardSetting
              icon={IIcons.Document}
              title="Kebijakan Privasi"
              desc="Sebagai komitmen kami dalam menjaga kerahasiaan dan kenyamanan kamu."
              route={Routes.UserKebijakanPrivasi}
            />
            <CardSetting
              icon={IIcons.Document}
              title="Syarat dan Ketentuan"
              desc="Sebagai komitmen kami dalam menjaga kerahasiaan dan kenyamanan kamu.Konseling nyaman dan tenang bersama kami. Kesehatan mentalmu adalah prioritas kami."
              route={Routes.UserSyaratDanKetentuan}
            />
          </div>
        </div>
      </div>
      {/* cari psikolog */}
      <SearchPsikolog route={Routes.UserSearchPsikolog} />
    </>
  )
}
