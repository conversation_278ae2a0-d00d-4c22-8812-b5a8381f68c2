'use client'

import * as React from 'react'
import { format, setDefaultOptions } from 'date-fns'

import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Calendar } from '@/components/ui/calendar'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { IIcons, SVGIcons } from '../_common/icon'
import { id } from 'date-fns/locale'
import { CaptionLayout } from 'react-day-picker'
setDefaultOptions({ locale: id })

type DatePickerProps = {
  label?: string
  placeholder?: string
  errorMsg?: string
  className?: string
  captionLayout?: CaptionLayout | undefined
  fromYear?: number
  toYear?: number
  date: Date | undefined
  prefixIcon?: boolean
  onSelect?: (date: Date | undefined) => void
  availableDates?: Date[]
  disabled?: (date: Date) => boolean
  compact?: boolean
}

export function DatePicker({
  onSelect,
  date,
  label,
  errorMsg,
  placeholder,
  className,
  captionLayout,
  fromYear,
  prefixIcon,
  toYear,
  availableDates,
  disabled,
  compact,
  ...props
}: DatePickerProps) {
  const [month, setMonth] = React.useState<Date | undefined>(new Date())
  const [isCalendarOpen, setIsCalendarOpen] = React.useState<boolean>(false)

  // Create disabled function based on available dates
  const isDateDisabled = React.useCallback(
    (date: Date) => {
      if (disabled) {
        return disabled(date)
      }
      if (availableDates && availableDates.length > 0) {
        // Format both dates as YYYY-MM-DD in local timezone for comparison
        const formatDateLocal = (d: Date) => {
          const year = d.getFullYear()
          const month = String(d.getMonth() + 1).padStart(2, '0')
          const day = String(d.getDate()).padStart(2, '0')
          return `${year}-${month}-${day}`
        }

        const dateStr = formatDateLocal(date)
        return !availableDates.some((availableDate) => formatDateLocal(availableDate) === dateStr)
      }
      return false
    },
    [availableDates, disabled]
  )
  return (
    <div className="form-container grid gap-[8px] pt-0">
      {label && <label className="text-body-md font-bold text-gray-400">{label}</label>}
      <Popover open={isCalendarOpen} onOpenChange={setIsCalendarOpen}>
        <PopoverTrigger asChild>
          <Button
            variant={'outline'}
            className={cn(
              'w-[280px] justify-start text-left font-normal border-line-200',
              !date && 'text-muted-foreground',
              className
            )}
          >
            {compact ? (
              <div className="flex flex-col items-center w-full h-full justify-center">
                <SVGIcons name={IIcons.Calendar} className="text-main-100 mb-1" />
                <span className="text-xs text-gray-500">{format(new Date(), 'MMM')}</span>
              </div>
            ) : (
              <>
                {prefixIcon && <SVGIcons name={IIcons.Calendar} className="mr-2 text-gray-200" />}
                {date ? format(date, 'PPP') : <span>{placeholder ?? ''}</span>}
              </>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0">
          <Calendar
            locale={id}
            mode="single"
            selected={date}
            month={month}
            onMonthChange={(month) => setMonth(month)}
            onSelect={(e) => {
              onSelect && onSelect(e)
              setIsCalendarOpen(false)
            }}
            disabled={isDateDisabled}
            captionLayout={captionLayout}
            fromYear={fromYear}
            toYear={toYear}
            {...props}
          />
        </PopoverContent>
      </Popover>
      {errorMsg && <div className="text-xs text-red-500 text-left mt-1">{errorMsg}</div>}
    </div>
  )
}
