'use client'
import { But<PERSON> } from '@/components/ui/button'
import Title from '../Title'
import WrapperAuth from '../WrapperAuth'
import Image from 'next/image'
import ImageGoogle from '@/assets/icons/google.png'
import AppInput from '@/components/_common/input/Input'
import ButtonPrimary from '@/components/_common/ButtonPrimary'
import { Divider } from '@/components/_common/Divider/Divider'

const RegisterComponent = () => {
  return (
    <WrapperAuth>
      <div className="grid gap-y-4">
        <Title
          title="Lengkapi Data"
          subTitle="<PERSON><PERSON> langkah kecil Anda untuk lebih sehat mental dan menjadi versi terbaik diri. "
        />
        <span>Informasi Akun</span>
        <AppInput className="pt-0" label="Nama Lengkap" type="text" value="" onChange={() => {}} />
        <AppInput className="pt-0" label="Nama <PERSON>" type="text" value="" onChange={() => {}} />
        <AppInput className="pt-0" label="No. Handphone" type="text" value="" onChange={() => {}} />
        <AppInput className="pt-0" label="Email" type="text" value="" onChange={() => {}} />
        <AppInput className="pt-0" label="Buat Password" type="password" value="" onChange={() => {}} />
        <div className="flex justify-end space-x-2">
          <ButtonPrimary variant="outlined" color="gray" size="xs" className="p-3 min-w-[140px] space-x-2">
            Lewati
          </ButtonPrimary>
          <ButtonPrimary variant="contained" size="xs" className="p-3 min-w-[140px] space-x-2">
            Selanjutnya
          </ButtonPrimary>
        </div>
      </div>
    </WrapperAuth>
  )
}

export default RegisterComponent
