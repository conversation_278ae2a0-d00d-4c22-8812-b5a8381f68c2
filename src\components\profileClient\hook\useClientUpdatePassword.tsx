import { useState } from 'react'
import { profileService } from '@/services/profile.service'

interface ErrorResponse {
  message: string
  error: string
  statusCode: number
}

export const useUpdatePassword = () => {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<ErrorResponse | null>(null)
  const [success, setSuccess] = useState(false)

  const updatePassword = async (currentPassword: string, newPassword: string) => {
    try {
      setLoading(true)
      setError(null)
      setSuccess(false)

      const payload = {
        password: currentPassword,
        newPassword: newPassword,
      }
      await profileService.clientUpdatePassword(payload)
      setSuccess(true)
    } catch (err: any) {
      // Handle structured error response
      if (err.response?.data) {
        setError(err.response.data)
      } else {
        setError({
          message: 'Gagal gagal memperbarui password',
          error: 'Error',
          statusCode: 500,
        })
      }
    } finally {
      setLoading(false)
    }
  }

  return {
    updatePassword,
    loading,
    error,
    success,
  }
}
