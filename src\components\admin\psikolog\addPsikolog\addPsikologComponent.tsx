'use  client'

import * as yup from 'yup'
import Breadcrumb from '@/components/breadcrumbs/Breadcrumbs'
import { HeaderContent } from '../../HeaderContent'
import { useState } from 'react'
import { RemoveIndex } from '@/utils/type'
import Translation from '@/constans/Translation'
import { yupResolver } from '@hookform/resolvers/yup'
import { useForm } from 'react-hook-form'
import ButtonPrimary from '@/components/_common/ButtonPrimary'
import { useToast } from '@/components/ui/use-toast'
import { useRouter } from 'next/navigation'
import { AppModal } from '@/components/_common/Modal/AppModal'
import AddPsikologStepOne from './addPsikologStepOne'
import AddPsikologStepTwo from './addPsikologStepTwo'
import AddPsikologStepThree from './addPsikologStepThree'
import AddPsikologStepFour from './addPsikologStepFour'
import { psychologistService } from '@/services/psychologist.service'
import { Routes } from '@/constans/routes'

const validationSchema = yup.object().shape({
  email: yup.string().required(Translation.RequiredEmail).email(Translation.ValidEmail),
  password: yup
    .string()
    .min(6, Translation.PasswordMinChar)
    .matches(/[A-Z]/, Translation.RequiredUppercase)
    .matches(/[a-z]/, Translation.RequiredLowercase)
    .matches(/\d/, Translation.RequiredNumber)
    .required(Translation.PasswordRequired),
  birthOrder: yup.string().required(),
  fullName: yup.string().required(Translation.RequiredFullName),
  nickname: yup.string().required(Translation.RequiredNickName),
  phoneNumber: yup.string().required(Translation.RequiredPhoneNumber),
  gender: yup.string().required(Translation.RequiredGender),
  bio: yup.string().nullable(),
  sipp: yup.string().nullable(),
  str: yup.string().nullable(),
  birthDate: yup.date().nullable(),
  childTo: yup.string().required('Wajib diisi').matches(/\d/, Translation.RequiredNumber),
  totalSibling: yup.string().required('Wajib diisi').matches(/\d/, Translation.RequiredNumber),
  occupation: yup.string().nullable(),
  maritalStatus: yup.string().nullable(),
  domicile: yup.string().nullable(),
  ethnicity: yup.string().nullable(),
  religion: yup.string().nullable(),
  offlineLocation: yup.string().nullable(),
  workplace: yup.string().nullable(),
  service: yup.string().nullable(),
  bankName: yup.string().nullable(),
  bankAccount: yup.string().nullable(),
  bankAccountName: yup.string().nullable(),
  field: yup.string().nullable(),
  problemCategory: yup
    .array()
    .of(yup.string())
    .min(1, 'Minimal pilih satu spesialisasi')
    .required('Spesialisasi wajib dipilih'),
  youtubeVideos: yup.string().nullable(),
  educationHistory: yup
    .array()
    .of(
      yup.object().shape({
        level: yup.string().required('Tingkat pendidikan wajib diisi'),
        university: yup.string().required('Nama universitas wajib diisi'),
        graduationYear: yup.date().defined().required('Tahun lulus wajib diisi'),
        major: yup.string().nullable(),
        entryYear: yup.date().nullable(),
      })
    )
    .min(1, 'Minimal satu pendidikan wajib diisi'),
  profilePhoto: yup
    .mixed()
    .nullable()
    .test('isFileOrNull', 'Format file tidak valid', (value) => {
      if (!value) return true
      return value instanceof File
    }),

  video: yup
    .mixed()
    .nullable()
    .test('isFileOrNull', 'Format file tidak valid', (value) => {
      if (!value) return true
      return value instanceof File
    }),
})

export type PsikologType = RemoveIndex<yup.InferType<typeof validationSchema>>

export default function AddPsikologComponent() {
  const router = useRouter()
  const { toast } = useToast()
  const [pageNumberStep, setPageNumberStep] = useState<number>(1)
  const [isOpenModal, setIsOpenModal] = useState<boolean>(false)
  const [isLoading, setIsLoading] = useState<boolean>(false)

  const {
    register,
    handleSubmit,
    trigger,
    getValues,
    setValue,
    reset,
    formState: { errors, isLoading: isLoadingForm, isSubmitting },
  } = useForm({
    resolver: yupResolver(validationSchema),
    defaultValues: {
      email: '',
      password: '',
      fullName: '',
      nickname: '',
      birthDate: null,
      gender: '',
      phoneNumber: '',
      childTo: '',
      totalSibling: '',
      birthOrder: '',
      educationHistory: [
        {
          level: '',
          university: '',
          graduationYear: undefined,
          major: '',
          entryYear: null,
        },
      ],
      problemCategory: [],
      religion: '',
      ethnicity: '',
      domicile: '',
      maritalStatus: '',
      occupation: '',
      bankName: '',
      bankAccount: '',
      bankAccountName: '',
    },
  })

  async function onSubmit(data: PsikologType) {
    setIsLoading(true)
    try {
      const formData = new FormData()

      Object.entries(data).forEach(([key, value]) => {
        if (value instanceof File || value instanceof Blob) {
          formData.append(key, value)
        } else if (Array.isArray(value) || typeof value === 'object') {
          formData.append(key, JSON.stringify(value))
        } else {
          formData.append(key, value.toString())
        }
      })

      console.log('Form data:', formData)
      // const createdPsikolog = await psychologistService.postPsychologist(formData)

      // if (!createdPsikolog) {
      //   throw new Error('API response is empty')
      // }

      // toast({
      //   variant: 'success',
      //   title: 'Submit data berhasil. Terimakasih',
      // })

      // reset()
      // router.replace(Routes.AdminPsychologistDetail.replace('[id]', createdPsikolog.id))
    } catch (error) {
      console.error('Error during submission:', error)
      toast({
        variant: 'danger',
        title: 'Terjadi kesalahan saat menyimpan data, silahkan ulangi beberapa saat lagi.',
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleBack = () => {
    if (pageNumberStep > 1) {
      setPageNumberStep((prev) => prev - 1)
    }
  }

  const handleNext = async () => {
    if (pageNumberStep === 1) {
      const isValid = await trigger(['fullName', 'nickname', 'gender', 'phoneNumber', 'email', 'password'])
      if (!isValid) return
      setPageNumberStep((prev) => prev + 1)
    } else if (pageNumberStep === 2) {
      // Add education validation
      const isValid = await trigger(['childTo', 'totalSibling', 'educationHistory', 'problemCategory'])
      if (!isValid) return
      setPageNumberStep((prev) => prev + 1)
    } else if (pageNumberStep === 3) {
      setPageNumberStep((prev) => prev + 1)
    } else {
      handleSubmit(onSubmit)()
    }
  }

  const handleClickAccountInformation = () => {
    setIsOpenModal((prevIsOpenModal) => !prevIsOpenModal)
  }

  return (
    <>
      <Breadcrumb containerClasses="pb-2" pageName="Add" />
      <HeaderContent title="Tambah Psikolog" />
      <div className="grid gap-y-4">
        {pageNumberStep === 1 && (
          <AddPsikologStepOne
            pageNumberStep={pageNumberStep}
            register={register}
            getValues={getValues}
            setValue={setValue}
            errors={errors}
            onSubmit={() => trigger()}
          />
        )}
        {pageNumberStep === 2 && (
          <AddPsikologStepTwo
            pageNumberStep={pageNumberStep}
            register={register}
            getValues={getValues}
            setValue={setValue}
            errors={errors as any}
            onSubmit={() => trigger()}
          />
        )}
        {pageNumberStep === 3 && (
          <AddPsikologStepThree
            pageNumberStep={pageNumberStep}
            register={register}
            getValues={getValues}
            setValue={setValue}
            errors={errors}
            onSubmit={() => trigger()}
          />
        )}
        {pageNumberStep === 4 && (
          <AddPsikologStepFour
            pageNumberStep={pageNumberStep}
            register={register}
            getValues={getValues}
            setValue={setValue}
            errors={errors}
            onSubmit={() => trigger()}
          />
        )}

        <div className="flex justify-end space-x-2">
          <ButtonPrimary
            onClick={handleClickAccountInformation}
            className="min-w-[140px]"
            size="base"
            variant="outlined"
            color="gray"
          >
            Batal
          </ButtonPrimary>
          {pageNumberStep > 1 && (
            <ButtonPrimary
              variant="outlined"
              size="xs"
              className="p-3 min-w-[140px] space-x-2"
              onClick={() => handleBack()}
              color="gray"
            >
              Kembali
            </ButtonPrimary>
          )}
          <ButtonPrimary
            isLoading={isSubmitting || isLoading || isLoadingForm}
            disabled={isSubmitting || isLoading || isLoadingForm}
            variant="contained"
            size="xs"
            className="p-3 min-w-[140px] space-x-2"
            onClick={() => handleNext()}
          >
            {pageNumberStep === 4 ? 'Simpan' : 'Selanjutnya'}
          </ButtonPrimary>
        </div>

        <AppModal
          className={`w-full transition-all duration-500 ease-in-out ${isOpenModal ? 'opacity-100 scale-100' : 'opacity-0 scale-0'}`}
          open={isOpenModal}
          onClose={() => {
            handleClickAccountInformation()
          }}
          title={'Batalkan proses tambah Psikolog?'}
          showOverlay={true}
        >
          <div className="flex flex-col bg-white gap-4">
            <div className="flex flex-col items-end gap-4">
              <div className="flex flex-col justify-center items-center gap-4">
                <p className="text-[16px] text-[#222222]">
                  Data yang Anda sudah masukkan tidak akan tersimpan.
                </p>
              </div>
              <div className="flex gap-4 items-end justify-end mt-6 w-full md:w-[70%]">
                <ButtonPrimary
                  className="min-w-[140px]"
                  size="base"
                  variant="outlined"
                  color="gray"
                  onClick={() => {
                    handleClickAccountInformation()
                  }}
                >
                  Tidak
                </ButtonPrimary>
                <ButtonPrimary
                  className="min-w-[140px]"
                  size="base"
                  variant="contained"
                  onClick={() => router.push('/admin/psychologist')}
                >
                  Iya
                </ButtonPrimary>
              </div>
            </div>
          </div>
        </AppModal>
      </div>
    </>
  )
}
