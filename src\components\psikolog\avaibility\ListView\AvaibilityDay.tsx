import { Switch } from '@/components/ui/switch'
import { TimeListAvaibility } from './TimeListAvaibility'
import AvaibilityAction from './AvaibilityAction'
import { useDispatch } from '@/store'
import { useToast } from '@/components/ui/use-toast'
import { avaibilityService } from '@/services/avaibility.service'
import { getEndTime, getStartTime } from '../../utils/getAvaibilityTime'
import { AvaibilityItemProps, AvaibilityProps, AvaibilityWithActionProps } from '../useAvaibilityList.hook'

const AvaibilityDay = ({
  id,
  day,
  isActive,
  timeList,
  onCheckedChange,
  refetch,
  onAddNewTime,
  onRemoveTime,
  onSetStartTime,
  onSetEndTime,
}: AvaibilityWithActionProps & {
  onRemoveTime?: (val: AvaibilityItemProps) => void
  onSetStartTime?: (val: AvaibilityItemProps) => void
  onSetEndTime?: (val: AvaibilityItemProps) => void
  onAddNewTime?: () => void
}) => {
  const { toast } = useToast()

  return (
    <div className="flex flex-wrap flex-row items-start py-6 min-h-[96px] border-b border-line-200">
      <div className="flex justify-between space-x-2 w-full sm:w-3/12">
        <div className="flex items-center space-x-2 h-[46px] w-full sm:w-3/12">
          <Switch checked={isActive} onCheckedChange={onCheckedChange} id="switch-day" />
          <label className="text-body-lg font-bold text-gray-400" htmlFor="switch-day">
            {day}
          </label>
        </div>
        <div className="flex sm:hidden">
          <AvaibilityAction
            onAdd={() => {
              onAddNewTime && onAddNewTime()
            }}
            onCopy={() => {}}
            day={id}
            refetch={refetch}
          />
        </div>
      </div>
      <TimeListAvaibility
        id={id}
        day={day}
        isActive={isActive}
        timeList={timeList}
        addItem={onAddNewTime}
        onRemoveTime={(val) => onRemoveTime && onRemoveTime(val)}
        onSetStartTime={(val) => onSetStartTime && onSetStartTime(val)}
        onSetEndTime={(val) => onSetEndTime && onSetEndTime(val)}
        onSetTime={(val) => {
          console.log(val)
        }}
        refetch={refetch}
      />
    </div>
  )
}

export default AvaibilityDay
