'use client'
import { HeaderContent } from '@/components/admin/HeaderContent'
import Breadcrumb from '@/components/breadcrumbs/Breadcrumbs'
import { useGetPageInformation } from '@/components/profileClient/hook/useGetPageInformation.hook'

export default function DetailHelpCenter({ params }: { params: { id: string } }) {
  const { id } = params
  const { data: dataDetailHelpCenter } = useGetPageInformation(id ?? 'not-found')

  return (
    <div className="lg:mb-[196px] lg:mt-[64px] px-4 lg:px-0 w-full lg:w-[930px] xl:w-[1120px] max-w-[1120px] flex flex-col justify-center gap-6 relative z-0 py-4 lg:py-0 mb-[150px] md:mb-0">
      <Breadcrumb containerClasses="hidden md:flex" pageName={dataDetailHelpCenter?.title} />
      <HeaderContent className="mb-0 block" title={dataDetailHelpCenter?.title} />
      <div className="flex flex-col gap-4">
        <div className="flex flex-col gap-0">
          <h3 className="text-[#222222] font-bold text-[20px]">{dataDetailHelpCenter?.subtitle}</h3>
          <span className="text-[12px] text-[#535353]">{dataDetailHelpCenter?.description}</span>
        </div>
        <div dangerouslySetInnerHTML={{ __html: dataDetailHelpCenter?.content }}></div>
      </div>
    </div>
  )
}
