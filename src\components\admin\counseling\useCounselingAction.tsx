import { TitleWithBadge } from '@/components/psikolog/schedule/ScheduleComponent'
import { useToast } from '@/components/ui/use-toast'
import { IModalActionState } from '@/interfaces/components/psikolog/schedule'
import { dispatch, useSelector } from '@/store'
import {
  resetRescheduleState,
  setModalState,
  setRejectStep,
  setRescheduleStep,
} from '@/store/admin/counseling.reducer'
import { ScheduleListProps } from '@/store/psikolog/schedule.reducer'

export const useCounselingAction = (refetch: any) => {
  const { toast } = useToast()
  const { modalState, rescheduleStep } = useSelector((state) => state.AdminCounseling)
  const handleCLickStartCounselling = (item?: any) => {
    dispatch(
      setModalState({
        modal:
          modalState.modal === IModalActionState.START_COUNSELING ? null : IModalActionState.START_COUNSELING,
        item,
      })
    )
  }

  const handleCLickReschedule = (item?: any) => {
    console.log(modalState)
    dispatch(
      setModalState({
        modal: modalState.modal === IModalActionState.RESCHEDULE ? null : IModalActionState.RESCHEDULE,
        item,
      })
    )
    dispatch(setRescheduleStep(1))
  }

  const handleCLickReject = (item?: any) => {
    dispatch(
      setModalState({
        modal: modalState.modal === IModalActionState.REJECT ? null : IModalActionState.REJECT,
        item,
      })
    )
    dispatch(setRejectStep(1))
  }

  const handleClickApproveCounseling = async (item: ScheduleListProps) => {
    dispatch(
      setModalState({
        modal: modalState.modal === IModalActionState.APPROVE ? null : IModalActionState.APPROVE,
        item,
      })
    )
  }

  const handleCancelSession = async (item: ScheduleListProps) => {
    dispatch(
      setModalState({
        modal:
          modalState.modal === IModalActionState.CANCEL_SESSION ? null : IModalActionState.CANCEL_SESSION,
        item,
      })
    )
  }
  const handleContactClient = async (item: any) => {
    if (!item?.client?.phoneNumber) {
      toast({
        description: 'Nomor klien tidak valid atau belum terdaftar',
        variant: 'danger',
      })
      return
    }
    if (window) {
      const clientName = (item?.client?.nickname ?? '').replace(' ', '+')
      const externalLink = `https://api.whatsapp.com/send/?phone=${item?.client?.phoneNumber}&text=Hi+${clientName}%2C+%28isi+pesan+kamu+disini%29&type=phone_number&app_absent=0`
      window.open(externalLink, '_blank')
    }
  }
  const handleContactPsychologist = async (item: any) => {
    if (!item?.psychologist?.phoneNumber) {
      toast({
        description: 'Nomor psikolog tidak valid atau belum terdaftar',
        variant: 'danger',
      })
      return
    }
    if (window) {
      const psychologistName = (item?.psychologist?.nickname ?? '').replace(' ', '+')
      const externalLink = `https://api.whatsapp.com/send/?phone=${item?.psychologist?.phoneNumber}&text=Hi+${psychologistName}%2C+%28isi+pesan+kamu+disini%29&type=phone_number&app_absent=0`
      window.open(externalLink, '_blank')
    }
  }

  let titleModal: string | React.ReactNode = ''

  if (modalState.modal === IModalActionState.RESCHEDULE) {
    titleModal = rescheduleStep === 3 ? 'Jadwal Baru Berhasil Dikirim' : 'Ubah Jadwal'
  } else if (modalState.modal === IModalActionState.REJECT) {
    titleModal = 'Tolak Jadwal'
  } else if (modalState.modal === IModalActionState.START_COUNSELING) {
    titleModal = (
      <TitleWithBadge
        startTime={modalState.item?.startTime ?? ''}
        endTime={modalState.item?.endTime ?? ''}
        fullName={modalState.item?.client?.fullName ?? ''}
        method={modalState.item?.method === 'Call' ? 'Call' : 'Video Call'}
        duration={modalState.item?.duration ?? 60}
      />
    )
  } else if (modalState.modal === IModalActionState.APPROVE) {
    titleModal = 'Terima Konseling?'
  } else if (modalState.modal === IModalActionState.CONFIRMATION_COMPLETELY) {
    titleModal = 'Selamat, Konseling sudah selesai!'
  } else if (modalState.modal === IModalActionState.CANCEL_SESSION) {
    titleModal = (
      <TitleWithBadge
        title={`Batalkan Sesi dengan ${modalState.item?.client?.fullName ?? 'Klien'}`}
        startTime={modalState.item?.startTime ?? ''}
        endTime={modalState.item?.endTime ?? ''}
        fullName={modalState.item?.client?.fullName ?? ''}
        method={modalState.item?.method === 'Call' ? 'Call' : 'Video Call'}
        duration={modalState.item?.duration ?? 60}
      />
    )
  }
  const modalOpen =
    modalState.modal === IModalActionState.RESCHEDULE ||
    modalState.modal === IModalActionState.REJECT ||
    modalState.modal === IModalActionState.START_COUNSELING ||
    modalState.modal === IModalActionState.APPROVE ||
    modalState.modal === IModalActionState.CONFIRMATION_COMPLETELY ||
    modalState.modal === IModalActionState.CANCEL_SESSION

  const handleClose = (isRefetch?: boolean) => {
    dispatch(
      setModalState({
        modal: null,
        item: null,
      })
    )
    dispatch(resetRescheduleState())
    isRefetch && refetch()
  }

  const isRefetchOncloseModalX =
    modalState.modal === IModalActionState.REJECT || modalState.modal === IModalActionState.RESCHEDULE

  return {
    handleCLickStartCounselling,
    handleCLickReschedule,
    handleCLickReject,
    handleClickApproveCounseling,
    handleCancelSession,
    handleContactClient,
    handleContactPsychologist,
    handleClose,
    modalState,
    rescheduleStep,
    isRefetchOncloseModalX,
    modalOpen,
    titleModal,
  }
}
