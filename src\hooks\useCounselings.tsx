import { config } from '@/constans/config'
import { CancelledStatus, PaymentStatus, WaitConfirmationsStatus } from '@/constans/StaticOptions'
import { httpRequest } from '@/utils/network'
import { useState, useEffect } from 'react'
import { counsellingService } from '@/services/counselling.service'

interface PsychologistData {
  id: string
  fullName: string
  nickname: string
  profilePhoto: string
  occupation: string | null
}

interface PaymentData {
  id: string
  status: string
  amount: number
  paidAt: string | null
  gatewayUrl: string
}

interface ProblemCategory {
  id: string
  problemCategory: string
}

export interface CounselingData {
  id: string
  psychologistId: string
  clientId: string
  startTime: string
  endTime: string
  complaint: string
  status: string
  method: string
  location: string
  duration: number
  psychologist: PsychologistData
  payment: PaymentData
  problemCategory: ProblemCategory[]
}

interface CounselingResponse {
  data: CounselingData[]
  meta: {
    page: number
    perPage: number
    total: number
    totalPages: number
  }
}

export const useCounseling = () => {
  const [counselingList, setCounselingList] = useState<CounselingData[]>([])
  const [loading, setLoading] = useState<boolean>(false)
  const [error, setError] = useState<string | null>(null)
  const [meta, setMeta] = useState({
    page: 1,
    perPage: 10,
    total: 0,
    totalPages: 0,
  })

  const fetchCounselingData = async (categories: PaymentStatus[], page: number = 1, perPage: number = 10) => {
    setLoading(true)
    setError(null)

    try {
      const response = await counsellingService.clientGetListCounselling(categories, page, perPage)
      console.log('Counseling data response:', response.data)
      setCounselingList(response.data)
      setMeta(response.meta)

      return response
    } catch (err) {
      setError('Failed to fetch counseling data')
      console.error('Error fetching counseling data:', err)
      return null
    } finally {
      setLoading(false)
    }
  }

  const getUpcomingCounseling = () => {
    return fetchCounselingData([PaymentStatus.APPROVED, PaymentStatus.PAID_PAYMENT, PaymentStatus.INPROGRESS])
  }

  const getPendingPaymentCounseling = () => {
    return fetchCounselingData([
      PaymentStatus.PENDING_PAYMENT,
      PaymentStatus.PAID_PAYMENT,
      PaymentStatus.RESCHEDULE_BY_CLIENT,
      PaymentStatus.RESCHEDULE_BY_PSYCHOLOGIST,
    ])
  }

  const getHistoryCounseling = () => {
    return fetchCounselingData([
      PaymentStatus.COMPLETED,
      PaymentStatus.PAID_PAYMENT,
      PaymentStatus.PAID,
      PaymentStatus.EXPIRED_PAYMENT,
    ])
  }

  return {
    counselingList,
    loading,
    error,
    meta,
    fetchCounselingData,
    getUpcomingCounseling,
    getPendingPaymentCounseling,
    getHistoryCounseling,
  }
}
