import { IIcons, SVGIcons } from '@/components/_common/icon'
import { ReactNode } from 'react'

interface TotalCardsProps {
  title: string
  midTitle: string
  midTitleColor: string
  bottomSubStyle: string
  borderColor: string
  note?: ReactNode
}
export default function TotalCardAdmin({
  title,
  midTitle,
  note,
  bottomSubStyle,
  midTitleColor,
  borderColor,
}: TotalCardsProps) {
  return (
    <>
      <div
        className={`flex flex-col gap-[16px] bg-white rounded-[15px] border p-[12px] md:p-[24px] w-[290px] md:w-auto h-[147px] md:h-auto ${borderColor}`}
      >
        <div className="flex justify-between items-center">
          <p className="font-bold text-[14px] md:text-[16px] text-[#222222]">{title}</p>
          <SVGIcons className="ml-2" name={IIcons.ArrowRight} />
        </div>
        <div className="flex flex-col gap-[8px]">
          <p className={`font-bold text-[28px] ${midTitleColor}`}>{midTitle}</p>
          <span className={`text-[12px] text-[##535353] ${bottomSubStyle}`}>{note}</span>
        </div>
      </div>
    </>
  )
}
