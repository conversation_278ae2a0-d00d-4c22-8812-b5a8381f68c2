import CalendarIcon from '@/assets/icons/sidebar/calendar.svg'
import CounselingIcon from '@/assets/icons/sidebar/counseling.svg'
import NotesIcon from '@/assets/icons/sidebar/file-text.svg'
import HomeIcon from '@/assets/icons/sidebar/home.svg'
import CounselingGroupIcon from '@/assets/icons/sidebar/message-square.svg'
import SearchIcon from '@/assets/icons/sidebar/search.svg'
import SettingIcon from '@/assets/icons/sidebar/settings.svg'
import TestimoniIcon from '@/assets/icons/sidebar/testimoni.svg'
import TimeIcon from '@/assets/icons/sidebar/time.svg'
import UserIcon from '@/assets/icons/sidebar/user.svg'
import MoneyIcon from '@/assets/icons/sidebar/money.svg'
import ReportIcon from '@/assets/icons/sidebar/report.svg'
import BellIcon from '@/assets/icons/navbar/bell.svg'
import BurgerIcon from '@/assets/icons/navbar/burger.svg'
import ArrowDownIcon from '@/assets/icons/arrow-down.svg'
import ArrowLeftIcon from '@/assets/icons/arrow-left.svg'
import ArrowRightIcon from '@/assets/icons/arrow-right.svg'
import ArrowUpIcon from '@/assets/icons/arrow-up.svg'
import ChatIcon from '@/assets/icons/chat.svg'
import ArrowUpDownIcon from '@/assets/icons/arrow-up-down.svg'
import ClockIcon from '@/assets/icons/clock.svg'
import CallIcon from '@/assets/icons/call.svg'
import CloseIcon from '@/assets/icons/close.svg'
import AddIcon from '@/assets/icons/add.svg'
import CopyIcon from '@/assets/icons/copy.svg'
import EditIcon from '@/assets/icons/edit.svg'
import ListIcon from '@/assets/icons/list.svg'
import RepeatIcon from '@/assets/icons/repeat.svg'
import DashIcon from '@/assets/icons/dash.svg'
import MoneyGrayIcon from '@/assets/icons/money.svg'
import ShieldIcon from '@/assets/icons/shield.svg'
import SettingGrayIcon from '@/assets/icons/setting.svg'
import SignIcon from '@/assets/icons/sign-out.svg'
import PlayIcon from '@/assets/icons/play.svg'
import ReportGrayIcon from '@/assets/icons/report.svg'
import TestimoniGrayIcon from '@/assets/icons/testimoni.svg'
import TextareaIcon from '@/assets/icons/textarea.svg'
import EyeIcon from '@/assets/icons/eye.svg'
import EyeSlashIcon from '@/assets/icons/eye-dash.svg'
import ImageIcon from '@/assets/icons/image.svg'
import UploadIcon from '@/assets/icons/upload-image.svg'
import StarIcon from '@/assets/icons/star.svg'
import FilterIcon from '@/assets/icons/filter.svg'
import Question from '@/assets/icons/question.svg'
import Document from '@/assets/icons/document.svg'
import BackSpace from '@/assets/icons/backSpace.svg'
import VideoCallIcon from '@/assets/icons/video-call.svg'
import SettingIconThinIcon from '@/assets/icons/sidebar/setting.svg'
import Share from '@/assets/icons/share.svg'
import SuitCase from '@/assets/icons/suitcase.svg'

import { FC, SVGProps } from 'react'

export enum IIcons {
  Home = 'home',
  BackSpace = 'backSpace',
  Question = 'question',
  Document = 'document',
  Counseling = 'counseling',
  CounselingGroup = 'counselingGroup',
  Notes = 'notes',
  Testimoni = 'testimoni',
  Setting = 'setting',
  Burger = 'burger',
  Bell = 'bell',
  User = 'user',
  Clock = 'clock',
  Video = 'video',
  Time = 'time',
  Search = 'search',
  Calendar = 'calendar',
  ArrorLeft = 'arrorLeft',
  ArrowRight = 'arrowRight',
  ArrowUp = 'arrowUp',
  ArrowDown = 'arrowDown',
  ArrowUpDown = 'arrowUpDown',
  Chat = 'chat',
  Call = 'Call',
  Close = 'Close',
  Add = 'Add',
  Copy = 'Copy',
  Edit = 'Edit',
  List = 'List',
  Repeat = 'Repeat',
  Dash = 'Dash',
  MoneyGray = 'MoneyGray',
  Shield = 'Shield',
  SettingGray = 'SettingGray',
  Sign = 'Sign',
  Play = 'Play',
  ReportGray = 'ReportGray',
  TestimoniGray = 'TestimoniGray',
  Textarea = 'Textarea',
  Eye = 'Eye',
  EyeSlash = 'EyeSlash',
  Image = 'Image',
  Upload = 'Upload',
  Money = 'Money',
  Report = 'Report',
  Star = 'Star',
  Filter = 'Filter',
  VideoCall = 'VideoCall',
  SettingIconThin = 'SettingIconThin',
  SuitCase = 'suitCase',
}

type ISVGICons = {
  name: IIcons
  className?: string
}

export const SVGIcons = ({ name, className }: ISVGICons) => {
  let IconValue: FC<SVGProps<SVGElement>> = HomeIcon
  switch (name) {
    case IIcons.Home:
      IconValue = HomeIcon
      break
    case IIcons.BackSpace:
      IconValue = BackSpace
      break
    case IIcons.Counseling:
      IconValue = CounselingIcon
      break
    case IIcons.Document:
      IconValue = Document
      break
    case IIcons.Question:
      IconValue = Question
      break
    case IIcons.CounselingGroup:
      IconValue = CounselingGroupIcon
      break
    case IIcons.Notes:
      IconValue = NotesIcon
      break
    case IIcons.Testimoni:
      IconValue = TestimoniIcon
      break
    case IIcons.Setting:
      IconValue = SettingIcon
      break
    case IIcons.Burger:
      IconValue = BurgerIcon
      break
    case IIcons.Bell:
      IconValue = BellIcon
      break
    case IIcons.User:
      IconValue = UserIcon
      break
    case IIcons.Clock:
      IconValue = ClockIcon
      break
    case IIcons.Time:
      IconValue = TimeIcon
      break
    case IIcons.Search:
      IconValue = SearchIcon
      break
    case IIcons.Calendar:
      IconValue = CalendarIcon
      break
    case IIcons.ArrorLeft:
      IconValue = ArrowLeftIcon
      break
    case IIcons.ArrowRight:
      IconValue = ArrowRightIcon
      break
    case IIcons.ArrowUp:
      IconValue = ArrowUpIcon
      break
    case IIcons.ArrowDown:
      IconValue = ArrowDownIcon
      break
    case IIcons.Chat:
      IconValue = ChatIcon
      break
    case IIcons.ArrowUpDown:
      IconValue = ArrowUpDownIcon
      break
    case IIcons.Call:
      IconValue = CallIcon
      break
    case IIcons.Close:
      IconValue = CloseIcon
      break
    case IIcons.Add:
      IconValue = AddIcon
      break
    case IIcons.Copy:
      IconValue = CopyIcon
      break
    case IIcons.Edit:
      IconValue = EditIcon
      break
    case IIcons.List:
      IconValue = ListIcon
      break
    case IIcons.Repeat:
      IconValue = RepeatIcon
      break
    case IIcons.Dash:
      IconValue = DashIcon
      break
    case IIcons.MoneyGray:
      IconValue = MoneyGrayIcon
      break
    case IIcons.Shield:
      IconValue = ShieldIcon
      break
    case IIcons.SettingGray:
      IconValue = SettingGrayIcon
      break
    case IIcons.Sign:
      IconValue = SignIcon
      break
    case IIcons.Play:
      IconValue = PlayIcon
      break
    case IIcons.ReportGray:
      IconValue = ReportGrayIcon
      break
    case IIcons.TestimoniGray:
      IconValue = TestimoniGrayIcon
      break
    case IIcons.Textarea:
      IconValue = TextareaIcon
      break
    case IIcons.Eye:
      IconValue = EyeIcon
      break
    case IIcons.EyeSlash:
      IconValue = EyeSlashIcon
      break
    case IIcons.Image:
      IconValue = ImageIcon
      break
    case IIcons.Upload:
      IconValue = UploadIcon
      break
    case IIcons.Money:
      IconValue = MoneyIcon
      break
    case IIcons.Report:
      IconValue = ReportIcon
      break
    case IIcons.Star:
      IconValue = StarIcon
      break
    case IIcons.Filter:
      IconValue = FilterIcon
      break
    case IIcons.VideoCall:
      IconValue = VideoCallIcon
      break
    case IIcons.SettingIconThin:
      IconValue = SettingIconThinIcon
      break
    case IIcons.SuitCase:
      IconValue = SuitCase
      break
    default:
      IconValue
      break
  }
  return <IconValue className={className ?? ''} stroke="currentColor" />
}
