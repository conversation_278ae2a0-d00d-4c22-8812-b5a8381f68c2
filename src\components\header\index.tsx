import { IIcons, SVGIcons } from '../_common/icon'

const Header = (props: {
  sidebarOpen: string | boolean | undefined
  setSidebarOpen: (arg0: boolean) => void
}) => {
  return (
    <header className="sticky flex">
      <div className="flex">
        <div className="flex items-center gap-2 sm:gap-4 lg:hidden">
          <button
            aria-controls="sidebar"
            onClick={(e) => {
              e.stopPropagation()
              props.setSidebarOpen(!props.sidebarOpen)
            }}
            className="lg:hidden"
          >
            <SVGIcons name={IIcons.Burger} className="w-6 h-6" />
          </button>
        </div>
      </div>
    </header>
  )
}

export default Header
