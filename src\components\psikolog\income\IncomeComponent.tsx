'use client'
import ButtonPrimary from '@/components/_common/ButtonPrimary'
import { IIcons, SVGIcons } from '@/components/_common/icon'
import { AppBigText, AppMediumText, Card, H4, Typography } from '@/components/_common/ui'
import { HeaderContent } from '@/components/admin/HeaderContent'
import { DataGrid } from '@/components/datagrid/DataTable'
import { columns } from './column'
import { DatePickerWithRange } from '@/components/ui/DateRangePicker'
import { useToast } from '@/components/ui/use-toast'
import { useEffect, useRef, useState } from 'react'
import { Button } from '@/components/ui/button'
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent } from '@/components/ui/DropdownMenu'
import { Checkbox } from '@/components/ui/checkbox'
import { AppModal } from '@/components/_common/Modal/AppModal'
import { AccountInformationForm } from './AccountInformationForm'
import { useGetPsychologistBalance } from '@/components/psikolog/income/hook/useGetPsychologistBalance.hook'
import moment from 'moment-timezone'
import { useGetProfile } from '@/hooks/useGetProfile.hook'
import Link from 'next/link'
import { DateRange } from 'react-day-picker'
import { formatStringToFullDateTimeOutput, formatStringToTimeOutput } from '@/utils/displayDate'
import useGetPsychologistTimezone from '@/hooks/useGetPsychologistTimezone.hook'
import useGetTimezoneLabel from '@/hooks/useGetTimezone.hook'

const FILTER_BALANCE = [
  { id: 1, title: 'Komisi dari konseling', key: 'INCOME', isChecked: false },
  { id: 2, title: 'Penarikan Saldo', key: 'OUTCOME', isChecked: false },
]

export const IncomeComponent = () => {
  const refetchTransaction = useRef<any>()
  const [isOpenModal, setIsOpenModal] = useState<boolean>(false)
  const [pageFilter, setPageFilter] = useState<any>([])
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: moment().subtract(30, 'days').toDate(),
    to: moment().toDate(),
  })
  const [filter, setFilter] =
    useState<{ id: number; title: string; key: string; isChecked: boolean }[]>(FILTER_BALANCE)
  const [isOpen, setIsOpen] = useState(false)

  const handleClickAccountInformation = () => {
    setIsOpenModal((prevIsOpenModal) => !prevIsOpenModal)
  }

  const { data } = useGetPsychologistBalance()
  const date = moment.utc(new Date(data?.lastUpdate), 'DD-MM-YYYY h:mm:ss A')

  const isUpdateToday = date && date.clone().diff(moment(new Date()), 'days') === 0
  const psychologistTimezone = useGetPsychologistTimezone()
  const timeZoneLabel = useGetTimezoneLabel()
  const lastUpdateOutput = data?.lastUpdate
    ? isUpdateToday
      ? `hari ini, ${formatStringToTimeOutput({
          date: date.clone().toISOString(),
          isUTC: true,
          timezone: psychologistTimezone,
          timeLabel: timeZoneLabel,
        })}`
      : `${formatStringToFullDateTimeOutput({
          date: date.clone().toISOString(),
          isUTC: true,
          timezone: psychologistTimezone,
          timeLabel: timeZoneLabel,
        })}`
    : ''
  const { data: dataBankAccount, refetch } = useGetProfile()
  const bankAccount = dataBankAccount?.bankAccount?.[0] ?? null

  const handleChecked = (checked: string | boolean, key: string) => {
    setFilter((prev) =>
      prev.map((val) => (String(val.id) === String(key) ? { ...val, isChecked: !!checked } : val))
    )
  }

  const handleSubmit = () => {
    const list = [...filter].filter((val) => val.isChecked).map((item) => item.key)
    const payload = [...filter].filter((val) => val.isChecked).length
      ? {
          key: 'type',
          operator: 'in',
          value: list,
        }
      : null

    const mergeFilter = [...(payload ? [payload] : [])]

    setPageFilter(mergeFilter)
  }

  useEffect(() => {
    refetchTransaction.current && refetchTransaction.current()
  }, [pageFilter])

  const whatsappText = encodeURI(`Hai Mental Healing, saya [Nama Psikolog] mau tarik saldo sebesar...`)

  return (
    <>
      <HeaderContent title="Pendapatan" />
      <div className="grid gap-4">
        <div className="grid grid-flow-row-dense grid-cols-12 grid-rows-1 gap-4">
          <Card
            className={`w-full grid col-span-12 xs:col-span-12 sm:col-span-8 xl:col-span-6 items-center gap-x-6 p-4 xs:p-4 sm:p-4 xl:p-6`}
          >
            <div className="grid items-center grid-cols-2 xs:grid-cols-2 sm:grid-cols-3 gap-4">
              <div className="text-wrap grid gap-y-[11px] col-span-2">
                <H4 className="text-gray-300">Anda memiliki saldo aktif</H4>
                <Typography className="text-[38px] leading-[42px] text-main-100 font-bold">
                  {Intl.NumberFormat('id-ID', {
                    style: 'currency',
                    currency: 'IDR',
                    maximumFractionDigits: 0,
                    minimumFractionDigits: 0,
                  }).format(Number(data?.balance || 0))}
                </Typography>
                <AppBigText>Update terakhir {lastUpdateOutput}</AppBigText>
              </div>
              <Link
                href={`https://api.whatsapp.com/send/?phone=6285173025865&text=${whatsappText}&type=phone_number&app_absent=0`}
                passHref={true}
                target="_blank"
                className="flex justify-center items-center w-full"
              >
                <ButtonPrimary size="base" variant="contained" className="w-full">
                  Tarik Saldo
                </ButtonPrimary>
              </Link>
            </div>
          </Card>
          <Card
            className={`w-full items-center col-span-12 xs:col-span-12 sm:col-span-4 xl:col-span-3 p-4 xs:p-4 sm:p-4 xl:p-6`}
          >
            <div className="text-wrap grid grid-rows-1 grid-cols-1 content-space-between gap-y-[11px]">
              <div className="flex justify-between">
                <AppMediumText>Informasi rekening</AppMediumText>
                <span className="cursor-pointer group" onClick={handleClickAccountInformation}>
                  <SVGIcons className='group-hover:text-main-50"' name={IIcons.Edit} />
                </span>
              </div>
              <div className="flex flex-col gap-2">
                <AppBigText bold>{bankAccount?.bankAccount ?? '-'}</AppBigText>
                <AppMediumText>a/n {bankAccount?.bankAccountName ?? '-'}</AppMediumText>
                <AppMediumText>{bankAccount?.bankName ?? '-'}</AppMediumText>
              </div>
            </div>
          </Card>
        </div>
        <div className="grid grid-cols-12">
          <Card className="grid col-span-12 sm:col-span-12 xl:col-span-9 p-4 xs:p-4 sm:p-4 xl:p-6">
            <div className="flex flex-col">
              <div className="flex flex-wrap gap-y-4 gap-x-0 sm:gap-4 mb-4 justify-between">
                <div className="flex flex-wrap w-full sm:w-auto gap-y-4 gap-x-0 sm:gap-4">
                  <DropdownMenu
                    modal={false}
                    onOpenChange={() => {
                      setIsOpen((prev) => !prev)
                    }}
                    open={isOpen}
                  >
                    <DropdownMenuTrigger className="[&>svg]:data-[state=open]:rotate-180" asChild>
                      <Button
                        variant="outline"
                        className="flex items-center gap-x-2 h-auto min-h-[50px] rounded-2xl w-full sm:w-auto"
                      >
                        <span>Semua Jenis Transaksi</span> <SVGIcons name={IIcons.ArrowDown} />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="grid p-4 gap-y-4">
                      {/* <DropdownMenuLabel className="px-0">Salin ke hari lain</DropdownMenuLabel> */}
                      {filter.map((item) => {
                        return (
                          <div key={item.title} className="flex justify-between items-center space-x-2">
                            <label
                              htmlFor={item.title}
                              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                            >
                              {item.title}
                            </label>
                            <Checkbox
                              checked={item.isChecked}
                              onCheckedChange={(checked) => handleChecked(checked, String(item.id))}
                              className="rounded-[4px] disabled:opacity-50 disabled:data-[state=checked]:bg-gray-100 disabled:data-[state=checked]:border-0"
                              id={item.title}
                            />
                          </div>
                        )
                      })}
                      <ButtonPrimary
                        variant="contained"
                        size="xs"
                        className="w-full"
                        onClick={() => handleSubmit()}
                      >
                        Terapkan
                      </ButtonPrimary>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
                <DatePickerWithRange
                  valueDate={dateRange}
                  formatDate={{ from: 'd LLL', to: 'd LLL' }}
                  className="h-[42px] sm:h-[55px] xs:w-auto"
                  onSelectDate={(date: DateRange) => setDateRange(date)}
                />
              </div>
            </div>
            <DataGrid
              fetchPath="api/psychologists/balance/transactions"
              hideHeader
              hideAction
              columns={columns}
              pageFilter={pageFilter}
              dateRange={dateRange}
              refetchRef={refetchTransaction}
            />
          </Card>
        </div>
      </div>
      <AppModal
        className="w-full"
        open={isOpenModal}
        onClose={() => {
          handleClickAccountInformation()
        }}
        title={'Atur Informasi Rekening'}
        showOverlay={true}
      >
        <AccountInformationForm
          onClose={() => {
            handleClickAccountInformation()
            refetch()
          }}
          accountInfo={bankAccount}
        />
      </AppModal>
    </>
  )
}
