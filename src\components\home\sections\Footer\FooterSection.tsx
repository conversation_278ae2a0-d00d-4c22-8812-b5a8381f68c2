'use client'
import { SelectSeparator } from '@/components/ui/select'
import Image from 'next/image'
import MHLogoWhite from '@/assets/icons/mh-logo-white.svg'
import ButtonPrimary from '@/components/_common/ButtonPrimary'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import InstagramIcon from '@/assets/icons/ig.svg'
import YoutubeIcon from '@/assets/icons/yt.svg'
import TiktokIcon from '@/assets/icons/tiktok.svg'
import LinkedInIcon from '@/assets/icons/linkedin-white.svg'

export const FooterSection = ({
  img,
  showButtonCounselling,
  isMobile,
  ...rest
}: {
  img?: string
  heading: string
  subHeading: string
  isMobile?: boolean
  showButtonCounselling?: boolean
}) => {
  const path = usePathname()
  const routePathFAQ = ['/home', '/mh-business', '/mh-education'].includes(path) ? '#faq' : '/home#faq'
  return (
    <div className="grid grid-cols-1">
      <div className="relative w-full h-[25px] xs:[30px] md:h-[60px] lg:h-[70px] xl:h-[100px]">
        <Image
          src={'/ilustration/footer-top.svg'}
          alt="footer-img"
          fill
          className="object-cover object-top"
        />
      </div>
      <div className="relative w-full h-[585px] md:h-[763px] bg-[#E6F5FD]">
        <FooterContent {...rest} showButtonCounselling={showButtonCounselling} isMobile={isMobile} />
        <Image
          src={img ? img : '/ilustration/footer.svg'}
          alt="footer-img"
          fill
          className="object-contain xl:object-cover object-bottom"
        />
      </div>
      <div className="bg-[#015780] border-0 grid justify-center w-full px-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-y-6 gap-x-2 py-10 w-full max-w-[1120px] text-wrap text-white">
          <div className="grid gap-y-4">
            <MHLogoWhite className="h-[32px]" />
            <div className="grid">
              <span className="text-body-sm font-bold">PT. Mental Healing Indonesia</span>
              <span className="text-body-sm font-medium">
                Everyone deserves to become the best version of themselves.
              </span>
            </div>
            <div className="grid gap-y-2">
              <span className="text-body-sm font-bold">FOLLOW US</span>
              <div className="flex gap-x-3">
                <Link href={'https://www.linkedin.com/company/mentalhealing-id/'} target="_blank">
                  <LinkedInIcon />
                </Link>
                <Link href={'https://www.instagram.com/mentalhealing.id/'} target="_blank">
                  <InstagramIcon />
                </Link>
                <Link href={'https://www.tiktok.com/@mentalhealing.id'} target="_blank">
                  <TiktokIcon />
                </Link>
                <Link href={'https://www.youtube.com/@MentalHealingIndonesia'} target="_blank">
                  <YoutubeIcon />
                </Link>
              </div>
            </div>
          </div>
          <div className="flex flex-col gap-y-3">
            <span className="text-body-sm font-bold uppercase">Organisasi</span>
            <Link href={'/about-us'}>
              <span className="text-body-sm font-medium">Tentang Kami</span>
            </Link>
            <Link href={'https://forms.gle/6gdj8mn6GCVMhGeW8'} target="_blank">
              <span className="text-body-sm font-medium">Jadi Psikolog Kami</span>
            </Link>
            <span className="text-body-sm font-medium">Karir</span>
          </div>
          <div className="flex flex-col gap-y-3">
            <span className="text-body-sm font-bold uppercase">Layanan</span>
            <Link href={'/search-psikolog'}>
              <span className="text-body-sm font-medium">Konseling Premium dengan Psikolog</span>
            </Link>
            <Link href={'/mh-business'}>
              <span className="text-body-sm font-medium">Mental Healing untuk Bisnis</span>
            </Link>
            <Link href={'/mh-education'}>
              <span className="text-body-sm font-medium">Mental Healing untuk Pendidikan</span>
            </Link>
          </div>
          <div className="flex flex-col gap-y-3">
            <span className="text-body-sm font-bold uppercase">bantuan</span>
            <Link
              href={
                'https://api.whatsapp.com/send/?phone=6285173025865&text=Hi+Mental+Healing%2C+%28isi+pesan+kamu+disini%29&type=phone_number&app_absent=0'
              }
              target="_blank"
            >
              <span className="text-body-sm font-medium">Hubungi Kami</span>
            </Link>
            <Link href={routePathFAQ}>
              <span className="text-body-sm font-medium">FAQs</span>
            </Link>
          </div>
        </div>
        <SelectSeparator className="border-white" />
        <span className="text-white text-body-sm font-medium py-6">
          Copyright 2020 - 2024 | 💙 Made with love by{' '}
          <Link className="underline" href={'https://www.likearth.co'} target="_blank" passHref={true}>
            Likearth Studio
          </Link>
        </span>
      </div>
    </div>
  )
}

const FooterContent = ({
  heading,
  subHeading,
  isMobile,
  showButtonCounselling,
}: {
  heading: string
  subHeading: string
  isMobile?: boolean
  showButtonCounselling?: boolean
}) => {
  return (
    <div className="flex flex-col items-center justify-center py-24 gap-y-4 px-4">
      <span className="text-heading-md md:leading-[62px] md:text-[50px] text-[#004262] font-bold max-w-[930px] text-center z-1">
        {heading}
      </span>
      <span
        className="text-body-lg md:text-[20px] text-[#0170A5] leading-[26px] font-medium max-w-[900px] text-center z-1"
        dangerouslySetInnerHTML={{ __html: subHeading }}
      ></span>
      <div className={`flex flex-col md:flex-row items-center justify-center gap-4 z-1`}>
        {showButtonCounselling && (
          <Link
            href={
              'https://api.whatsapp.com/send/?phone=6285173025865&text=Hi+Mental+Healing%2C+%28isi+pesan+kamu+disini%29&type=phone_number&app_absent=0'
            }
            target="_blank"
          >
            <ButtonPrimary className="rounded-full" variant="contained" size="sm">
              Daftar Konseling
            </ButtonPrimary>
          </Link>
        )}
        <Link
          href={
            'https://api.whatsapp.com/send/?phone=6285173025865&text=Hi+Mental+Healing%2C+%28isi+pesan+kamu+disini%29&type=phone_number&app_absent=0'
          }
          target="_blank"
        >
          <ButtonPrimary
            className="rounded-full"
            variant={showButtonCounselling ? 'outlined' : 'contained'}
            size="sm"
          >
            Hubungi Kami
          </ButtonPrimary>
        </Link>
      </div>
    </div>
  )
}
