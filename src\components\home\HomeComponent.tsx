'use client'
import { MentalHealingFeature } from './sections/MentalHealingFeature/MentalHealingFeature'
import { TypeCounselling } from './sections/OurTypeCounselling/TypeCounselling'
import { OurPsychologist } from './sections/OurPsychologist/OurPsychologist'
import Image from 'next/image'
import { RateComponent } from './sections/Rate/RateComponent'
import { Testimony } from './sections/Testimony/Testimony'
import { HowToComponent } from './sections/HowTo/HowToComponent'
import { Faq } from '@/components/MHProject/sections/Faq'
import { SLiderHome } from './sections/SliderHome/Slider'
import { useEffect } from 'react'
import { useToast } from '../ui/use-toast'
import { useDispatch, useSelector } from '@/store'
import { setShowEmailVerification } from '@/store/auth/auth.reducer'

export const HomePageComponent = () => {
  const { toast } = useToast()
  const dispatch = useDispatch()
  const { showEmailVerification } = useSelector((state) => state.Authentication)

  useEffect(() => {
    console.log(showEmailVerification)
    if (showEmailVerification) {
      dispatch(setShowEmailVerification(false))
      toast({
        variant: 'success',
        title: 'Email berhasil terverifikasi, silakan login di aplikasi.',
      })
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  return (
    <>
      <div id="home" className="max-w-screen relative w-full">
        <SLiderHome />
      </div>
      <div className="gap-y-[120px] w-full flex flex-col justify-center items-center">
        <MentalHealingFeature />
        {/* <div className="">
        <LandingPageMenu
          label="Selalu sedia untuk bantu kamu menyelesaikan masalah:"
          labelMobile="Spesialisasi"
          onSelect={(val) => handleClick(val)}
        />
      </div> */}
        <div
          id="psychologist"
          className="min-h-[500px] max-w-screen w-full relative flex justify-center bg-gradient-to-t from-[#E6F5FD] to-white pb-20"
        >
          <OurPsychologist />
          <Image
            src={'/ilustration/leaf-left.svg'}
            width={130}
            height={130}
            alt="leaf-left"
            className="absolute bottom-0 left-0 z-0"
          />
          <Image
            src={'/ilustration/leaf-right.svg'}
            width={130}
            height={130}
            alt="leaf-right"
            className="absolute bottom-0 right-0 z-0"
          />
        </div>
        <div className="gap-y-[40px] w-full flex flex-col justify-center items-center">
          <TypeCounselling />
        </div>
        <div className="grid grid-cols-1 gap-y-15 max-w-[1120px] w-full z-1">
          {/* <TypeCounselling /> */}
          <div className="grid grid-cols-1 gap-y-10 max-w-[1120px] w-full z-1">
            <RateComponent />
          </div>
        </div>
        <div className="flex flex-col justify-center items-center gap-y-[120px] max-w-screen w-full relative z-1 pt-6 md:pt-15 bg-gradient-to-b from-[#E6F5FD] to-50% to-white">
          <Image
            src="/ilustration/cloud.svg"
            width={105}
            height={55}
            alt="cloud"
            className="absolute -left-2 top-28 animate-[bounce_6s_infinite]"
          />
          <Image
            src="/ilustration/cloud.svg"
            width={65}
            height={34}
            alt="cloud"
            className="absolute right-1/3 top-6 animate-[bounce_5s_infinite]"
          />
          <Image
            src="/ilustration/cloud.svg"
            width={100}
            height={58}
            alt="cloud"
            className="absolute -right-0 top-4 animate-[bounce_7s_infinite]"
          />
          <Testimony />
          <div className="grid grid-cols-1 gap-y-10 max-w-[1120px] w-full z-1 px-4">
            <HowToComponent />
          </div>
        </div>
        <div id="faq" className="grid grid-cols-1 gap-y-10 max-w-[1120px] w-full z-1 px-4">
          <Faq category="B2C" />
        </div>
      </div>
    </>
  )
}
