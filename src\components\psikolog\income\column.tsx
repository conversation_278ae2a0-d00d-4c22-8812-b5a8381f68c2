'use client'

import { ColumnDef } from '@tanstack/react-table'
import { AppBigCaption } from '@/components/_common/ui'
import { InfoDataDisplay } from '@/components/datagrid/InfoDataDisplay'
import moment from 'moment'

export type ClientType = {
  id: string
  walletId: string
  amount: number
  type: string
  name: string
  description: string
  createdAt: string
  payment: [
    {
      id: string
      counselingId: string
      transactionid: string
      voucherId: null | string
      paidAt: string
      status: string
      amount: number
      gateway: string
      gatewayAdditionalData: {
        id: string
        amount: number
        status: string
        created: string
        is_high: boolean
        paid_at: string
        updated: string
        user_id: string
        currency: string
        payment_id: string
        external_id: string
        paid_amount: number
        ewallet_type: string
        merchant_name: string
        payment_method: string
        payment_channel: string
        payment_method_id: string
      }
      createdAt: string
      modifiedAt: string
    },
  ]
  withdrawal: any[]
}

export const columns: ColumnDef<ClientType>[] = [
  {
    accessorKey: 'name',
    header: 'name',
    cell: ({ cell, row }) => {
      const dateOfTransaction = moment(row.original.createdAt).format('DD MMMM YYYY, HH:mm')
      return <InfoDataDisplay title={row.getValue('name')} subTitle={dateOfTransaction} />
    },
  },
  {
    accessorKey: 'saldo',
    header: 'Saldo',
    meta: {
      style: {
        textAlign: 'right',
      },
    },
    cell: ({ row }) => {
      const amount = row.original.amount
      const transactionType = row.original.type === 'INCOME' ? 'Pemasukan' : 'Pengeluaran'
      const isIncome = row.original.type === 'INCOME'
      return (
        <div className="flex items-center justify-end gap-x-2">
          <div className="flex flex-col gap-y-1">
            <span className={`text-right w-full font-bold ${isIncome ? 'text-gray-400' : 'text-main-100'}`}>
              {isIncome ? '' : '-'}
              {Intl.NumberFormat('id-ID', {
                style: 'currency',
                currency: 'IDR',
                maximumFractionDigits: 0,
                minimumFractionDigits: 0,
              }).format(Number(amount))}
            </span>
            <AppBigCaption className="text-gray-200">{transactionType}</AppBigCaption>
          </div>
        </div>
      )
    },
  },
]
