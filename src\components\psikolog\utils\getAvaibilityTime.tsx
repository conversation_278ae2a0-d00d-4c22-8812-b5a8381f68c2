import { addHours, format } from 'date-fns'
import moment from 'moment'

type TimeListProps = {
  startTime: string
  endTime: string
}

export const getStartTime = (timeList: TimeListProps[]) => {
  if (timeList.length > 0) {
    return timeList[timeList.length - 1].endTime
  }
  return '09:00'
}

export const getEndTime = (startTime: string) => {
  if (startTime) {
    return format(addHours(new Date(`01/01/1000 ${startTime}`), 1), 'kk:mm')
  }
  return '10:00'
}

export const isAfterValidTime = (time: string) => {
  if (time) {
    return moment(`01/01/1000 ${time}`).isAfter(moment(`01/01/1000 21:00`))
  }
  return false
}
