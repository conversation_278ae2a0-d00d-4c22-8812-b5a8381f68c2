import { IIcons, SVGIcons } from '@/components/_common/icon'

export default function TestimoniDetailHeader({ client, stars }: { client: string; stars: number }) {
  return (
    <div className="flex flex-col gap-2">
      <div className="flex items-center gap-1">
        <SVGIcons className="" name={IIcons.Star} />
        <span className="text-[#222222] font-bold text-[16px]">{stars}/5</span>
        <span className="text-[#222222] font-bold text-[16px]">
          Dari <span className="text-[#039EE9] font-bold text-[16px]">{client}</span>
        </span>
      </div>
    </div>
  )
}
