'use client'

import { ColumnDef } from '@tanstack/react-table'
import { UserPhoto } from '../../UserPhoto/UserPhoto'
import { InfoDataDisplay } from '@/components/datagrid/InfoDataDisplay'
import Link from 'next/link'

export type ClientReportType = {
  id: string
  psikolog: string
  klien: string
  status: string
  metode: string
  biaya: string
  imgUrl: string
  jadwal: string
}

export const columns: ColumnDef<ClientReportType>[] = [
  {
    accessorKey: 'id',
    header: 'Klien Report ID',
  },
  {
    accessorKey: 'klien',
    header: 'Klien',
    cell: ({ cell, row }) => {
      return (
        <div className="font-bold hover:underline hover:text-main-100">
          <UserPhoto photo={cell.row.original['imgUrl']} title={row.getValue('klien')} />
        </div>
      )
    },
  },
  {
    accessorKey: 'psikolog',
    header: 'Psikolog',
    cell: ({ cell, row }) => {
      return (
        <div className="font-bold hover:text-main-100">
          <UserPhoto
            photo={cell.row.original['imgUrl']}
            title={row.getValue('psikolog')}
            subTitle="Psikolog Klinis"
          />
        </div>
      )
    },
  },
  {
    accessorKey: 'jadwal',
    header: 'Jadwal',
    cell: ({ cell, row }) => {
      const date = new Intl.DateTimeFormat('id-ID', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      }).format(new Date(row.getValue('jadwal')))
      const time = `13:00 - 14:00`
      return <InfoDataDisplay title={date} subTitle={time} />
    },
  },
]
