'use client'
import Title from '../Title'
import AppInput from '@/components/_common/input/Input'
import ButtonPrimary from '@/components/_common/ButtonPrimary'

import { yupResolver } from '@hookform/resolvers/yup'
import { useForm } from 'react-hook-form'
import * as yup from 'yup'
import { useRouter, useSearchParams } from 'next/navigation'
import Translation from '@/constans/Translation'
import { authService } from '@/services/auth.service'
import { useState } from 'react'
import { useToast } from '@/components/ui/use-toast'
import { Routes } from '@/constans/routes'

const validationSchema = yup.object().shape({
  password: yup
    .string()
    .min(6, Translation.PasswordMinChar)
    .matches(/[A-Z]/, Translation.RequiredUppercase)
    .matches(/[a-z]/, Translation.RequiredLowercase)
    .matches(/\d/, Translation.RequiredNumber)
    .required(Translation.PasswordRequired),
  rePassword: yup.string().oneOf([yup.ref('password')], Translation.RepasswordMustMatch),
})

const ResetPasswordComponent = () => {
  const router = useRouter()
  const { toast } = useToast()
  const searchparam = useSearchParams()
  const tokenFromUrl = searchparam.get('token') ?? ''
  const [isLoading, setIsLoading] = useState<boolean>(false)

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(validationSchema),
  })

  async function onSubmit(data: { password: string }) {
    setIsLoading(true)
    try {
      await authService
        .resetPassword(data.password, tokenFromUrl)
        .then((res) => {
          toast({
            variant: 'success',
            title:
              res?.message || 'Kata sandi anda berhadil diatur ulang. Silahkan masuk dengan kata sandi baru.',
          })
          setTimeout(() => {
            setIsLoading(false)
            router.replace(Routes.Login)
          }, 1000)
        })
        .catch((err) => {
          setTimeout(() => {
            toast({
              variant: 'danger',
              title: 'Kata sandi anda gagal diatur ulang. Silahkan hubungi admin.',
            })
            setIsLoading(false)
          }, 1000)
        })
    } catch (error) {
      setTimeout(() => {
        toast({
          variant: 'danger',
          title: 'Terjadi masalah teknis. Silahkan hubungi admin.',
        })
        setIsLoading(false)
      }, 1000)
    } finally {
      setTimeout(() => {
        setIsLoading(false)
      }, 1000)
    }
  }

  return (
    <div className="grid gap-y-4">
      <Title title="Buat Password Baru" subTitle="Pastikan buat password yang mudah Anda ingat." />
      <AppInput
        className="pt-0"
        label="Buat Password"
        type="password"
        {...register('password')}
        name="password"
        errorMsg={!!errors.password ? errors.password.message : undefined}
      />
      <AppInput
        {...register('rePassword')}
        className="pt-0"
        label="Ulangi Password"
        type="password"
        name="rePassword"
        errorMsg={!!errors.rePassword ? errors.rePassword.message : undefined}
      />
      <ButtonPrimary
        variant="contained"
        size="xs"
        className="w-full p-3 min-h-[48px] space-x-2"
        onClick={() => handleSubmit(onSubmit)()}
        isLoading={isLoading}
      >
        Simpan
      </ButtonPrimary>
    </div>
  )
}

export default ResetPasswordComponent
