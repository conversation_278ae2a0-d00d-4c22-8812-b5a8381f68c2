import React from 'react'
import { twMerge } from 'tailwind-merge'

type GeneralUIProps = {
  children: React.ReactNode
  className?: string
}

type BoxProps = GeneralUIProps

type SizingProps = {
  xs?: number
  sm?: number
  md?: number
  lg?: number
  xl?: number
  xxl?: number
}

type FlexProps = GeneralUIProps & SizingProps

type TypographyProps = GeneralUIProps & {
  as?: 'span' | 'h1' | 'h2' | 'h3' | 'h4'
}

type HeadingProps = GeneralUIProps

type AppText = GeneralUIProps & {
  bold?: boolean
}

export const H1 = ({ children, className }: HeadingProps) => (
  <h1 className={`text-heading-lg font-bold ${className ?? ''}`}>{children}</h1> // fontSize 32, lineHeight 42
)
export const H2 = ({ children, className }: HeadingProps) => (
  <h2 className={`text-inherit text-heading-md font-bold ${className ?? ''}`}>{children}</h2> // fontSize 28, lineHeight 36
)
export const H3 = ({ children, className }: HeadingProps) => (
  <h3 className={`text-heading-sm font-semibold ${className ?? ''}`}>{children}</h3> // fontSize 26, lineHeight 32
)
export const H4 = ({ children, className, bold }: HeadingProps & { bold?: boolean }) => (
  <h4 className={`text-subheading-md ${bold ? 'font-bold' : 'font-medium'} ${className ?? ''}`}>
    {children}
  </h4> // fontSize 20, lineHeight 26
)

export const AppBigText = ({ children, className, bold }: AppText) => (
  <span className={`text-body-lg ${bold ? 'font-bold' : 'font-medium'} ${className ?? ''}`}>{children}</span> // fontSize 16, lineHeight 22
)

export const AppMediumText = ({ children, className, bold }: AppText) => (
  <span className={`${bold ? 'text-body-md font-bold' : 'text-body-sm font-medium'} ${className ?? ''}`}>
    {children}
  </span> // default medium; bold = (fontSize 14, lineHeight 20); medium (fontSize 14, lineHeight 18)
)

export const AppBigCaption = ({ children, className }: AppText) => (
  <span className={`text-caption-md font-medium ${className ?? ''}`}>{children}</span> // fontSize 12, lineHeight 16
)

export const AppMediumCaption = ({ children, className, bold }: AppText) => (
  <span className={`text-captiom-sm ${bold ? 'font-bold' : 'font-medium'} ${className ?? ''}`}>
    {children}
  </span> // default medium; bold = (fontSize 11, lineHeight 14)
)

export const Typography = ({ children, className, as = 'span' }: TypographyProps) => {
  let TypohgraphyUI = null
  switch (as) {
    case 'span':
      TypohgraphyUI = className ? <span className={className}>{children}</span> : <span>{children}</span>
      break
    case 'h1':
      TypohgraphyUI = <H1 className={className}>{children}</H1>
      break
    case 'h2':
      TypohgraphyUI = <H2 className={className}>{children}</H2>
      break
    case 'h3':
      TypohgraphyUI = <H3 className={className}>{children}</H3>
      break
    case 'h4':
      TypohgraphyUI = <H4 className={className}>{children}</H4>
      break
    default:
      TypohgraphyUI = className ? <span className={className}>{children}</span> : <span>{children}</span>
      break
  }
  return TypohgraphyUI
}

export const BigTitleTypography = ({ children, className }: TypographyProps) => (
  <Typography as="h1" className={twMerge(`text-gray-400`, className)}>
    {children}
  </Typography>
)

export const MediumTitleTypography = ({ children, className }: TypographyProps) => (
  <Typography as="h2" className={twMerge(`text-gray-400`, className)}>
    {children}
  </Typography>
)

export const SmallTitleTypography = ({ children, className }: TypographyProps) => (
  <Typography as="h3" className={twMerge(`text-gray-400`, className)}>
    {children}
  </Typography>
)

// LAYOUT UI
export const Container = ({ children, className, ...props }: BoxProps) => {
  return (
    <section className={`mx-auto ${className ?? ''}`} {...props}>
      {children}
    </section>
  )
}

export const Flex = ({
  children,
  className,
  xs = undefined,
  sm = undefined,
  md = undefined,
  lg = undefined,
  xl = undefined,
  xxl = undefined,
  ...props
}: FlexProps) => (
  <div
    className={twMerge(
      `flex xs:w-${xs ? xs + '/12' : 'full'} sm:w-${sm ? sm + '/12' : 'full'} md:w-${md ? md + '/12' : 'full'} lg:w-${lg ? lg + '/12' : 'full'} xl:w-${xl ? xl + '/12' : 'full'} xxl:w-${xxl ? xxl + '/12' : 'full'}`,
      className
    )}
    {...props}
  >
    {children}
  </div>
)

export const Card = ({ children, className }: BoxProps) => (
  <div className={twMerge(`border border-line-200 rounded-card xs:p-1.5 sm:p-2 md:p-6`, className)}>
    {children}
  </div>
)
// END LAYOUT UI
