import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogOverlay,
} from '@/components/ui/dialog'
import React, { forwardRef } from 'react'
import { cn } from '@/lib/utils'
import { DialogClose } from '@radix-ui/react-dialog'
import { IIcons, SVGIcons } from '../icon'

export type AppModalProps = {
  open: boolean
  showOverlay?: boolean
  onClose: (arg?: any) => void
  title?: string | React.ReactNode
  children: React.ReactNode
  footer?: React.ReactNode
  className?: string
  [key: string]: any
}

export const AppModal = forwardRef<any, any>(function AppModal(
  { title, open, onClose, className, children, showOverlay, ...props },
  ref
) {
  return (
    <Dialog open={open}>
      <DialogContent
        showOverlay={showOverlay}
        overlay={<DialogOverlay ref={ref} />}
        onClose={onClose}
        className={cn(
          'w-[calc(100%-32px)] h-auto flex flex-col flex-start max-h-screen overflow-y-auto',
          className
        )}
        {...props}
      >
        <DialogHeader className={`sm:px-0 py-6 flex relative ${title ? 'visible' : 'hidden'}`}>
          <DialogTitle className="text-left mr-6">
            <span className="text-subheading-md font-bold">{title}</span>
          </DialogTitle>
          <DialogClose
            onClick={onClose && onClose}
            style={{ marginTop: '0 !important' }}
            className="absolute top-1/2 -translate-y-1/2 right-0 mt-0"
          >
            <SVGIcons name={IIcons.Close} />
          </DialogClose>
          <DialogDescription className="hidden"></DialogDescription>
        </DialogHeader>
        {children}
      </DialogContent>
    </Dialog>
  )
})
