'use client'
import { IIcons, SVGIcons } from '@/components/_common/icon'

export default function ListTanggal() {
  return (
    <>
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-[12px]">
          {/* img profile */}
          <div className="flex flex-col items-center justify-center bg-[#E7F7FF] w-[60px] h-[56px] rounded-[15px] gap-1 p-2">
            <span className="text-[#222222] text-[14px] font-bold">16</span>
            <span className="text-[#222222] text-[14px]">SEL</span>
          </div>
          {/* title */}
          <div className="flex flex-col gap-1">
            <p className="font-bold text-[#222222] text-[14px]">
              Konseling dengan <span className="text-[#039EE9]"><PERSON><PERSON><PERSON></span>
            </p>
            <div className="flex items-center gap-[12px]">
              <p className="text-[#535353] text-[14px]">13:00 - 14.00 WIB</p>
              <div className="w-[6px] h-[6px] bg-[#EBEBEB] rounded-full"></div>
              <p className="text-[#535353] text-[14px]">Call</p>
            </div>
          </div>
        </div>
        <SVGIcons className="ml-2" name={IIcons.ArrowRight} />
      </div>
    </>
  )
}
