'use client'
import { AppSelect } from '@/components/_common/Select/AppSelect'
import { Switch } from '@/components/ui/switch'
import { useEffect, useState } from 'react'
import moment from 'moment-timezone'
import ButtonPrimary from '@/components/_common/ButtonPrimary'
import { profileService } from '@/services/profile.service'
import { useToast } from '@/components/ui/use-toast'
import { useDispatch, useSelector } from '@/store'
import { setTimezone } from '@/store/psikolog/profile.reducer'

export const TimezoneForm = ({ onClose }: { onClose: () => void }) => {
  const { toast } = useToast()
  const dispatch = useDispatch()
  const [isAutoChecked, setIsAutoChecked] = useState<boolean>(false)
  const [selectedTimezone, setSelectedTimezone] = useState<string>('')
  const { userIdentity } = useSelector((state) => state.PsychologistProfile)
  const timezone = userIdentity?.userConfig?.TIMEZONE

  useEffect(() => {
    if (timezone) {
      setSelectedTimezone(timezone)
    }
  }, [timezone])

  async function updateTimezone(timezone: string) {
    try {
      await profileService.updateTimezone(timezone)
      toast({
        variant: 'success',
        description: 'Perubahan zona waktu berhasil diterapkan',
      })
    } catch (error) {
      toast({
        variant: 'danger',
        description: 'Terjadi kesalahan saat merubah zona waktu',
      })
    }
  }

  const handleChecked = async (checked: string | boolean) => {
    if (checked) {
      const userTimezone = moment.tz.guess()
      setSelectedTimezone(userTimezone)
    }
    setIsAutoChecked(!!checked)
  }

  const handleSubmitTimezone = async () => {
    if (selectedTimezone) {
      await updateTimezone(selectedTimezone)
      dispatch(setTimezone(selectedTimezone))
      onClose()
    } else {
      toast({
        variant: 'danger',
        description: 'Silahkan pilih zona waktu terlebih dahulu.',
      })
    }
  }

  const timezoneOptions: { label: string; value: string }[] = []
  moment.tz.zonesForCountry('ID').map((item) => {
    timezoneOptions.push({
      label: `${item} (GMT${moment.tz(item).format('Z')})`,
      value: item,
    })
  })
  return (
    <div className="grid gap-3 -mt-3">
      <span>Waktu akan disesuaikan dengan lokasi Anda.</span>
      <div className="flex flex-row gap-3 justify-between items-center">
        <span className="font-bold text-gray-400 text-[16px]">Deteksi lokasi otomatis</span>
        <Switch
          checked={isAutoChecked}
          onCheckedChange={(checked) => handleChecked(checked)}
          id="switch-timezone"
        />
      </div>
      <div className="flex">
        <AppSelect
          useFilterIcon
          name="timezone-options"
          options={timezoneOptions || []}
          className="md:min-w-[200px] text-gray-300 text-body-md"
          placeholder={'Pilih zona waktu'}
          onChange={(val) => {
            setSelectedTimezone(val)
            handleChecked(false)
          }}
          value={selectedTimezone}
          errorMsg={''}
        />
      </div>
      <div className="flex justify-end mt-4">
        <ButtonPrimary
          className="min-w-[143px]"
          variant={'contained'}
          size="sm"
          onClick={() => handleSubmitTimezone()}
        >
          Simpan
        </ButtonPrimary>
      </div>
    </div>
  )
}
