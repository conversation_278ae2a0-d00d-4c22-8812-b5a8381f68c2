import { config } from '@/constans/config'
import { httpRequest } from '@/utils/network'

export type PayloadAvaibility = {
  timezone: string
  startTime: string
  endTime: string
  day: number
}
export type PayloadSepcificAvaibility = {
  id?: string | null
  timezone: string
  startTime: string
  endTime: string
  date: string
}
export type PayloadAvaibilityUpdate = {
  startTime: string
  endTime: string
}

export class AvaibilityService {
  async getAvaibility() {
    return await httpRequest({
      method: 'get',
      url: `${config?.apiBaseUrl}api/psychologists/availability`,
    })
  }
  async createUpdateAvaibility(payload: PayloadAvaibility) {
    return await httpRequest({
      method: 'post',
      url: `${config?.apiBaseUrl}api/psychologists/availability`,
      data: payload,
    })
  }
  async updateAvaibilityById(payload: PayloadAvaibilityUpdate, id: string) {
    return await httpRequest({
      method: 'put',
      url: `${config?.apiBaseUrl}api/psychologists/availability/${id}`,
      data: payload,
    })
  }
  async removeAvaibilityById(id: string) {
    return await httpRequest({
      method: 'delete',
      url: `${config?.apiBaseUrl}api/psychologists/availability/delete-by-id`,
      data: { id },
    })
  }
  async removeAvaibilityByDay(day: number) {
    return await httpRequest({
      method: 'delete',
      url: `${config?.apiBaseUrl}api/psychologists/availability/delete-by-day`,
      data: { day },
    })
  }
  async removeAvaibilityByDate(date: string) {
    return await httpRequest({
      method: 'delete',
      url: `${config?.apiBaseUrl}api/psychologists/availability/delete-by-date`,
      data: { date },
    })
  }
  async cloneAvaibility(payload: { copy: number; paste: number[] }) {
    return await httpRequest({
      method: 'post',
      url: `${config?.apiBaseUrl}api/psychologists/availability/copy-paste`,
      data: payload,
    })
  }
  async createBatchAvaibility(payload: PayloadSepcificAvaibility[]) {
    return await httpRequest({
      method: 'post',
      url: `${config?.apiBaseUrl}api/psychologists/availability/batch/create`,
      data: payload,
    })
  }
  async updateBatchAvaibility(payload: PayloadSepcificAvaibility[], previousDate: string) {
    return await httpRequest({
      method: 'put',
      url: `${config?.apiBaseUrl}api/psychologists/availability/batch/update/${previousDate}`,
      data: payload,
    })
  }
}

export const avaibilityService = new AvaibilityService()
