import { useToast } from '@/components/ui/use-toast'
import { counsellingService } from '@/services/counselling.service'
import { useQuery } from '@tanstack/react-query'

export const useGetScheduleByDate = (date: string, duration?: number) => {
  const { toast } = useToast()
  return useQuery({
    queryKey: ['PsychologistSchedule', { date, duration }],
    queryFn: () =>
      counsellingService
        .getScheduleBydate(date)
        .then((response) => {
          if (duration) {
            const timebyDuration = response?.find((val: any) => val?.durationInMinute === duration)
            return timebyDuration
          }
          return response
        })
        .catch((error) => {
          toast({
            title: 'Gagal',
            description: '<PERSON><PERSON><PERSON><PERSON> masalah dengan server, Silahkan hubungi Admin',
            variant: 'danger',
          })
        }),
  })
}
