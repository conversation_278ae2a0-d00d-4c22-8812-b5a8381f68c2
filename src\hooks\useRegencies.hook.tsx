import { useToast } from '@/components/ui/use-toast'
import { regionService } from '@/services/region.service'
import { capitalizeEachWord } from '@/utils/stringUtils'
import { useQuery } from '@tanstack/react-query'

export const useRegencies = (id: string) => {
  const { toast } = useToast()
  return useQuery({
    queryKey: ['Regencies', { id }],
    queryFn: () => {
      if (id) {
        return regionService
          .getRegencies(id)
          .then((response) => {
            const payload = response.map((val: any) => ({
              ...val,
              label: capitalizeEachWord(val.name),
              value: val.id,
            }))
            return payload
          })
          .catch((error) => {
            toast({
              title: 'Gagal',
              description: 'Terjadi masalah dengan server, Silahkan hubungi Admin',
              variant: 'danger',
            })
          })
      } else {
        return []
      }
    },
  })
}
