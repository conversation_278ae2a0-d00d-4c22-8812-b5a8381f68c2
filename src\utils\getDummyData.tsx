// ESM
import { faker } from '@faker-js/faker'

type FakerProps = {
  pengguna: string
  email: string
  saldo: string
  konseling: number
  journaling: number
  status: string
  imgUrl: string
  id: string
  psikolog: string
  klien: string
  metode: string
  biaya: string
  jadwal: string
}

export function createRandom(): FakerProps {
  return {
    imgUrl: faker.image.urlLoremFlickr(),
    pengguna: faker.person.fullName(),
    email: faker.internet.email(),
    status: faker.commerce.product(),
    konseling: faker.number.int({ max: 20 }),
    journaling: faker.number.int({ max: 20 }),
    saldo: faker.commerce.price({ min: 1000, max: 100000 }),
    id: String('K' + faker.number.bigInt({ min: 20000000, max: 99999999 })),
    psikolog: faker.person.fullName(),
    klien: faker.person.fullName(),
    metode: faker.company.name(),
    biaya: faker.commerce.price({ min: 1000, max: 100000 }),
    jadwal: faker.date
      .between({ from: '2020-01-01T00:00:00.000Z', to: '2030-01-01T23:30:30.000Z' })
      .toDateString(),
  }
}

export const getDummy = (count: number): FakerProps[] =>
  faker.helpers.multiple(createRandom, {
    count: count ?? 10,
  })
