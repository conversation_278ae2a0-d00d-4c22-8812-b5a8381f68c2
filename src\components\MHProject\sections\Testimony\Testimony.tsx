'use client'
import { TestimonyCardItem } from './TestimonyCardItem'
import { useEffect, useState } from 'react'
import { testimonyService } from '@/services/testimony.service'
import { CategoryProps } from '@/services/faq.service'
import { MobileTestimony } from './Mobiletestimony'

type TestimonyProps = {
  category: CategoryProps
  title: string
  subtitle: string
}

export type TestimonyResponse = {
  id: string
  testimony: string
  clientName: string
}

export const Testimony = ({ title, subtitle, category }: TestimonyProps) => {
  const [testimonyList, setTestimonyList] = useState([])
  const [testimonyListGroup, setTestimonyListGroup] = useState<any>([])

  useEffect(() => {
    testimonyService
      .getTestimonyByCategory(category)
      .then((response) => {
        if (response.length) {
          setTestimonyList(response)
        }
      })
      .catch((error) => {
        console.log(error)
      })
  }, [category])

  useEffect(() => {
    if (testimonyList.length) {
      const amount = Math.ceil(testimonyList.length / 3)
      let groupByN = (n: number, arr: any[]) => {
        let result = []
        for (let i = 0; i < arr.length; i += n) {
          result.push(arr.slice(i, i + n))
        }
        return result
      }
      const newData = groupByN(amount, testimonyList)
      setTestimonyListGroup(newData)
    }
  }, [testimonyList])

  return (
    <div className="flex flex-col items-center gap-y-10">
      <div className="flex flex-col items-center gap-y-4 px-4 md:px-0">
        <span className="text-subheading-md md:text-[38px] md:leading-[42px] text-gray-400 font-bold text-center">
          {title}
        </span>
        <span className="text-body-md md:text-body-lg text-gray-300 font-medium text-center">{subtitle}</span>
      </div>
      <div className="hidden md:grid grid-cols-3 grid-flow-dense w-full gap-6">
        {testimonyListGroup.length
          ? testimonyListGroup.map((group: any, id: number) => {
              return (
                <div key={id} className="flex flex-col gap-y-6">
                  {group.map((item: TestimonyResponse) => {
                    return <TestimonyCardItem key={item.id} {...item} />
                  })}
                </div>
              )
            })
          : null}
      </div>
      <div className="flex md:hidden">
        <MobileTestimony testimonyList={testimonyList} />
      </div>
    </div>
  )
}
