import ButtonPrimary from '@/components/_common/ButtonPrimary'
import ContentClientReportDetails from './ContentClientReportDetail'
import InputfileClientReport from './inputFile'
import NextAppointmentClientReport from './nextAppointment'
import { formatStringToDateOutput, formatStringToStartEndTimeOutput } from '@/utils/displayDate'
import { defaultTimezone } from '@/constans/StaticOptions'

export default function ClientReportDetails({
  report,
  onDelete,
  onUpdate,
}: {
  report: any
  onDelete: (val: any) => void
  onUpdate: (val: any) => void
}) {
  return (
    <div className="flex flex-col gap-4 py-4 border-t border-[#EBEBEB]">
      <ContentClientReportDetails title={'Anamnesa'} content={report.anamnesis ?? '-'} />
      <ContentClientReportDetails title={'Intervensi'} content={report.intervention ?? '-'} />
      <ContentClientReportDetails title={'Tugas'} content={report.task ?? '-'} />
      <ContentClientReportDetails title={'Catatan untuk Klien'} content={report.notesForClient ?? '-'} />
      <ContentClientReportDetails title={'Assessment File'} content={report.assessmentFile ?? '-'} />
      {/* <InputfileClientReport /> */}
      {/* jadwal berikutnya */}
      <NextAppointmentClientReport
        date={report?.counseling?.startTime ? formatStringToDateOutput(report?.counseling?.startTime) : ''}
        time={
          report?.counseling?.startTime
            ? formatStringToStartEndTimeOutput({
                date: report?.counseling?.startTime,
                duration: report?.counseling?.duration,
                timezone: defaultTimezone,
                isUTC: true,
              })
            : ''
        }
        via={report?.counseling?.method === 'Call' ? 'Call' : 'Video Call'}
      />
      {/* dibuat pada */}
      <span className="text-[14px] text-[#535353]">
        Klien report dibuat: {formatStringToDateOutput(report?.createdAt)}
      </span>
      {/* button */}
      <div className="flex items-center gap-3">
        <ButtonPrimary
          className="rounded-[15px]"
          variant="outlined"
          size="sm"
          onClick={() => onUpdate(report)}
        >
          Ubah Klien Report
        </ButtonPrimary>
        <ButtonPrimary
          className="rounded-[15px] w-[143px]"
          variant="outlined"
          color="gray"
          size="sm"
          onClick={() => onDelete(report)}
        >
          Hapus
        </ButtonPrimary>
      </div>
    </div>
  )
}
