import { Swiper, SwiperSlide } from 'swiper/react'
import { FreeMode, Navigation } from 'swiper/modules'
import 'swiper/css'
import 'swiper/css/pagination'
import { FeatureCard } from './FeatureCard'

export const MHFeatureMobile = ({ content }: { content: any[] }) => {
  return (
    <div className="w-full max-w-screen xl:max-w-[1120px] relative flex md:hidden flex-col gap-y-10 -mt-[60px] md:-mt-[200px] z-1">
      <div className="relative w-full">
        <Swiper
          slidesPerView={'auto'}
          navigation
          spaceBetween={24}
          freeMode={true}
          pagination={{
            clickable: true,
          }}
          modules={[FreeMode, Navigation]}
          className="mySwiperpsychologist"
        >
          {content.map((slide: any) => (
            <SwiperSlide className="first:pl-4 last:pr-4" key={slide.id} style={{ width: 'fit-content' }}>
              <div className="w-[328px] lg:w-[400px] h-full">
                <FeatureCard {...slide} handleClick={() => slide.handleClick && slide.handleClick()} />
              </div>
            </SwiperSlide>
          ))}
        </Swiper>
      </div>
    </div>
  )
}
