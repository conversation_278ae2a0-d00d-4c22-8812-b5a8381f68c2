'use client'

import { useForm } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import * as yup from 'yup'
import { useToast } from '@/components/ui/use-toast'
import { useEffect, useRef, useState } from 'react'
import { AdminProfile } from '@/interfaces/profile-service'
import { profileService } from '@/services/profile.service'
import { FormSetting } from './FormSetting'
import AppInput from '@/components/_common/input/Input'
import { EthnicityoptionsList, Otheroptions, ReligionOptionsList } from '@/constans/onboardOptions'
import { InputImage } from '@/components/_common/InputImage'
import { PhoneNumberRegExp } from '@/constans/StaticOptions'

const validationSchema = yup.object().shape({
  nickname: yup.string().nullable(),
  fullName: yup.string().nullable(),
  profilePhoto: yup.string().nullable(),
  phoneNumber: yup
    .string()
    .matches(PhoneNumberRegExp, 'Nomor handphone harus valid dengan kode negara, e.g +6212345678'),
})

type InferTypeProfile = yup.InferType<typeof validationSchema>
type KeysType = keyof InferTypeProfile

const PersonalDataPsikolog = ({ nickname, fullName, profilePhoto, phoneNumber }: AdminProfile) => {
  const { toast } = useToast()
  const ref = useRef<any>(null)
  const [isLoadingForm, setIsLoadingForm] = useState<boolean>(false)
  const [fieldEdit, setFieldEdit] = useState<string | null>(null)
  const EthnicityoptionsListWithoutOther = [...EthnicityoptionsList]
    .filter((val) => val.value !== Otheroptions)
    .map((item) => item.value)
  const ReligionOptionsListWithoutOther = [...ReligionOptionsList]
    .filter((val) => val.value !== Otheroptions)
    .map((item) => item.value)

  const {
    register,
    handleSubmit,
    trigger,
    getValues,
    setValue,
    reset,
    resetField,
    formState: { errors, isLoading, isSubmitting },
  } = useForm({
    resolver: yupResolver(validationSchema),
    defaultValues: {
      nickname: '',
      fullName: '',
      profilePhoto: '',
      phoneNumber: '',
    },
  })

  useEffect(() => {
    reset({
      nickname: nickname ?? '',
      fullName: fullName ?? '',
      profilePhoto: profilePhoto ?? '',
      phoneNumber: phoneNumber ?? '',
    })
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fullName, nickname, phoneNumber, reset])

  const handleOnSubmit = async (key: string) => {
    setIsLoadingForm(true)
    try {
      const formData = new FormData()
      formData.append(key, getValues(key as any))
      const result = await profileService.updateAdminProfile(formData)
      resetField(key as KeysType, { defaultValue: result?.[key] })
      toast({
        variant: 'success',
        title: 'Perbaharui data profil berhasil',
      })
      setIsLoadingForm(false)
      setFieldEdit(null)
    } catch (error) {
      toast({
        variant: 'danger',
        title: 'Perbaharui data profil gagal',
      })
      setFieldEdit(null)
      setIsLoadingForm(false)
    }
  }

  const uploadImage = async (imageUpload: any) => {
    if (imageUpload) {
      const formData = new FormData()
      formData.append('profilePhoto', imageUpload)
      await profileService.updateAdminProfile(formData)
      toast({
        variant: 'success',
        title: 'Perbaharui photo profil berhasil',
      })
    }
  }

  return (
    <div className="grid gap-y-6">
      <div className="flex flex-row gap-4 mt-4">
        <InputImage
          {...register('profilePhoto')}
          classinfo="w-full text-caption-md font-medium text-gray-300"
          objectPosition="center"
          height={120}
          width={120}
          name="profilePhoto"
          label="Foto"
          accept={'image/jpeg,image/png'}
          maxFileSizeMb={2}
          onChange={(file) => {
            if (file) {
              uploadImage(file)
            }
          }}
          preview={getValues('profilePhoto') || ''}
          inputRef={ref}
        />
      </div>
      <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
        <div className="col-span-2">
          <FormSetting
            viewComponent={getValues('fullName') ?? ''}
            editComponent={
              <AppInput
                {...register('fullName')}
                className="pt-0"
                type="text"
                value={getValues('fullName')!}
                onChange={(val) => {
                  setValue('fullName', val.target.value, { shouldValidate: true })
                }}
                name="fullName"
                label="Nama Lengkap"
                errorMsg={!!errors.fullName ? String(errors.fullName.message) : undefined}
                placeholder="fullName"
              />
            }
            label="Nama Lengkap"
            isEdit={fieldEdit === 'fullName'}
            isLoading={isLoadingForm}
            onEditButton={() => setFieldEdit('fullName')}
            onCancel={() => {
              setFieldEdit(null)
              resetField('fullName')
            }}
            onSubmit={() => handleOnSubmit('fullName')}
          />
          <FormSetting
            viewComponent={getValues('nickname') ?? ''}
            editComponent={
              <AppInput
                {...register('nickname')}
                className="pt-0"
                type="text"
                value={getValues('nickname')!}
                onChange={(val) => {
                  setValue('nickname', val.target.value, { shouldValidate: true })
                }}
                name="nickname"
                label="Nama Panggilan"
                errorMsg={!!errors.nickname ? String(errors.nickname.message) : undefined}
                placeholder="nickname"
              />
            }
            label="Nama Panggilan"
            isEdit={fieldEdit === 'nickname'}
            isLoading={isLoadingForm}
            onEditButton={() => setFieldEdit('nickname')}
            onCancel={() => {
              setFieldEdit(null)
              resetField('nickname')
            }}
            onSubmit={() => handleOnSubmit('nickname')}
          />
          <FormSetting
            viewComponent={getValues('phoneNumber') ?? ''}
            editComponent={
              <AppInput
                {...register('phoneNumber')}
                className="pt-0"
                type="text"
                onChange={(val) => {
                  setValue('phoneNumber', val.target.value, { shouldValidate: true })
                }}
                name="phoneNumber"
                label="No. Handphone"
                errorMsg={!!errors.phoneNumber ? String(errors.phoneNumber.message) : undefined}
                placeholder="+628xxxxxxxxx"
                note="Pastikan nomor terdaftar di aplikasi WhatsApp."
              />
            }
            label="No. Handphone (Whatsapp)"
            isEdit={fieldEdit === 'phoneNumber'}
            isLoading={isLoadingForm}
            onEditButton={() => setFieldEdit('phoneNumber')}
            onCancel={() => {
              setFieldEdit(null)
              resetField('phoneNumber')
            }}
            onSubmit={() => handleOnSubmit('phoneNumber')}
          />
        </div>
      </div>
    </div>
  )
}

export default PersonalDataPsikolog
