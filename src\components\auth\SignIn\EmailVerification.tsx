'use client'
import Title from '../Title'

import { useRouter, useSearchParams } from 'next/navigation'
import { Routes } from '@/constans/routes'
import { Separator } from '@/components/ui/separator'
import Link from 'next/link'
import CountdownTimer from './CountdownTimer'
import { useState } from 'react'
import { authService } from '@/services/auth.service'
import Lottie from 'lottie-react'
import EmailLottie from '../../../../public/lotties/mail.json'
import SparkEmailLottie from '../../../../public/lotties/Sparkling.json'

export const EmailVerification = () => {
  const router = useRouter()
  const searchparam = useSearchParams()
  const emailDeepLink = searchparam.get('email') ?? ''
  const [isAvailableResend, setIsAvailableResend] = useState<boolean>(false)
  const [time, setTime] = useState<number>(1)

  async function handleResend() {
    try {
      authService
        .signinWithEmail(emailDeepLink)
        .then((res) => {
          setIsAvailableResend(false)
          setTime(1)
        })
        .catch((err) => {
          console.log(err)
        })
    } catch (error) {
      console.log(error)
    }
  }

  const handleOnExpiredTimer = () => {
    setIsAvailableResend(true)
    console.log('Expired Timer')
  }

  return (
    <div className="grid gap-y-0">
      <div className="relative max-h-[120px] bg-slate1 max-w-[120px] mx-auto text-center">
        <div className="absolute top-1 left-4 w-[70%]">
          <Lottie animationData={SparkEmailLottie} loop={true} autoplay={true} className="w-full h-full" />
          <div className="w-full h-2 bg-white absolute bottom-0 right-0"></div>
        </div>
        <Lottie animationData={EmailLottie} loop={false} autoplay={true} className="w-full h-full" />
        <div className="absolute bottom-0 right-0 bg-white w-full h-3"></div>
      </div>
      <div className="grid gap-y-4">
        <Title
          center
          title="Verifikasi Email Anda"
          subTitle="Untuk menjaga keamanan platform, Kami memastikan semua akun terdaftar telah terverifikasi. "
        />
        <Separator />
        <div className="flex flex-col gap-y-[8px] text-center">
          <span className="text-body-sm font-medium text-gray-400 text-center">
            Email verifikasi terlah terkirim ke:
          </span>
          <span className="text-subheading-md font-bold text-gray-400">{emailDeepLink}</span>
        </div>
        <span className="text-body-sm font-medium text-gray-400 text-center">
          Salah masukan email?{' '}
          <Link className="text-body-sm font-bold text-main-100" href={Routes.Login}>
            Ganti Email
          </Link>
        </span>
        <Separator />
        <span className="text-body-sm font-medium text-gray-400 text-center">
          Tidak dapat email verifikasi?{' '}
          <span
            className={`text-body-sm font-bold ${isAvailableResend ? 'text-[#039EE9] cursor-pointer' : 'text-gray-100 cursor-not-allowed'}`}
            onClick={() => isAvailableResend && handleResend()}
          >
            Kirim Ulang
          </span>{' '}
          {!isAvailableResend ? (
            <span className="`text-body-sm font-bold text-gray-300">
              {'('}
              <CountdownTimer
                onComplete={() => handleOnExpiredTimer()}
                className={`text-body-sm font-bold text-[#039EE9]`}
                time={time}
              />
              {')'}
            </span>
          ) : null}
        </span>
      </div>
    </div>
  )
}

export default EmailVerification
