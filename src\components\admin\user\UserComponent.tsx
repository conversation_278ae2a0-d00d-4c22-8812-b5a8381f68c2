'use client'
import { columns } from './columns'
import { DataGrid } from '@/components/datagrid/DataTable'
import { Card } from '@/components/_common/ui'
import { getDummy } from '@/utils/getDummyData'
import { HeaderContent } from '../HeaderContent'
import { FilterHeader } from '../FilterHeader'
import { AdminStatus } from '@/constans/StaticOptions'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { ADMIN_ADMIN_API } from '@/constans/API_PATH'
import { useRouter } from 'next/navigation'
import { useToast } from '@/components/ui/use-toast'
import { adminService } from '@/services/admin.service'

const fetchData = async ({ pageIndex, pageSize }: { pageIndex: number; pageSize: number }) => {
  const limit = pageSize
  return new Promise((resolve, reject) => {
    const data = {
      data: getDummy(limit),
      page: pageIndex + 1 || 1,
      limit: limit,
      totalPage: Math.ceil(500 / limit) ?? 1,
      total: 500,
    }
    resolve(data)
  })
}

export const UserComponent = () => {
  const { toast } = useToast()
  const router = useRouter()
  const refRefetch = useRef<any>()
  const debounceRef = useRef<NodeJS.Timeout | null>(null)

  const filterOptions = [
    { value: AdminStatus.ALL, label: 'Semua Status' },
    { value: AdminStatus.ACTIVE, label: 'Aktif' },
    { value: AdminStatus.INACTIVE, label: 'Nonaktif' },
  ]

  const [toggle, setToggle] = useState<boolean>(false)
  const [userAdmin, setUserAdmin] = useState<any>(null)
  const actions = ['activate', 'deactivate']
  const [status, setStatus] = useState<string>('All')
  const [searchName, setSearchName] = useState<string>('')

  const fetchData = useCallback(async () => {
    try {
      const statusQuery =
        status === AdminStatus.ALL ? AdminStatus.ALL : status === AdminStatus.ACTIVE ? 'true' : 'false'
      const response = await adminService.getAdminList(statusQuery, searchName)
      console.log('response', response)

      return {
        data: response.data || [],
        meta: response.meta || { total: 0, perPage: 10, currentPage: 1, lastPage: 1 },
      }
    } catch (error) {
      console.error('Error fetching Admin:', error)
      return { data: [], meta: { total: 0, perPage: 10, currentPage: 1, lastPage: 1 } }
    }
  }, [status, searchName])

  useEffect(() => {
    if (debounceRef.current) {
      clearTimeout(debounceRef.current)
    }

    debounceRef.current = setTimeout(() => {
      refRefetch.current()
    }, 500)

    return () => {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current)
      }
    }
  }, [searchName])

  useEffect(() => {
    refRefetch.current()
  }, [status])

  useEffect(() => {
    if (!toggle) {
      setUserAdmin(null)
    }
  }, [toggle])

  const handleActivate = useCallback(async (id?: string) => {
    try {
      await adminService.postAdminEnable(id ?? '')
      toast({ variant: 'success', title: 'Admin berhasil diaktifkan' })
      setTimeout(() => {
        setToggle(false)
        refRefetch.current()
      }, 500)
    } catch (error) {
      toast({ variant: 'danger', title: 'Admin gagal diaktifkan' })
    }
  }, [])

  const handleDeactivate = useCallback(async (id?: string) => {
    try {
      await adminService.postAdminDisable(id ?? '')
      toast({ variant: 'success', title: 'Admin berhasil dinonaktifkan' })
      setTimeout(() => {
        setToggle(false)
        refRefetch.current()
      }, 500)
    } catch (error) {
      toast({ variant: 'danger', title: 'Admin gagal dinonaktifkan' })
    }
  }, [])

  const Activate = useMemo(
    () => ({
      name: 'activate',
      icon: null,
      label: 'Aktifkan Akun',
      onClick: (item: any) => {
        setUserAdmin(item?.original)
        handleActivate(item?.original?.userIdentity?.id)
      },
    }),
    [handleActivate]
  )

  const Deactivate = useMemo(
    () => ({
      name: 'deactivate',
      icon: null,
      label: 'Nonaktifkan Akun',
      onClick: (item: any) => {
        setUserAdmin(item?.original)
        handleDeactivate(item?.original?.userIdentity?.id)
      },
    }),
    [handleDeactivate]
  )

  const actionsMenu = (row: any) => {
    let isActive = row?.original?.userIdentity?.isActive
    console.log('isActive', isActive)
    let actionsMenus: any[] = []
    if (!isActive && actions.includes('activate')) {
      actionsMenus = [...actionsMenus, { ...Activate, key: `activate-${row.id}` }]
    }
    if (isActive && actions.includes('deactivate')) {
      actionsMenus = [...actionsMenus, { ...Deactivate, key: `deactivate-${row.id}` }]
    }
    return actionsMenus
  }

  return (
    <>
      <HeaderContent
        title="Admin"
        handleDatePicker={() => undefined}
        handleAdd={() => router.push('/admin/user/add')}
      />
      <Card className="border-0 p-0 xs:p-0 sm:border sm:p-6">
        <div className="flex flex-col">
          <div className="flex gap-x-5 mb-4">
            <FilterHeader
              showSecondFilter={false}
              firstOptions={filterOptions}
              onChangeSearch={(val) => setSearchName(val)}
              onChangeFilter={(val) => setStatus(val)}
              labelFirstFilter="Status"
            />
          </div>
          <DataGrid
            fetchData={fetchData}
            columns={columns}
            actionMenuList={actionsMenu}
            refetchRef={refRefetch}
          />
        </div>
      </Card>
    </>
  )
}
