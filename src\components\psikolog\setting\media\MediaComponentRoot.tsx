'use client'
import ButtonPrimary from '@/components/_common/ButtonPrimary'
import InputFile from '@/components/_common/InputFile/Index'
import { AppBigCaption, H4 } from '@/components/_common/ui'
import { useEffect, useRef, useState } from 'react'
import { InputYoutubeLink } from './InputYoutubeLink'
import { PsychologistProfile } from '@/interfaces/profile-service'
import { profileService } from '@/services/profile.service'
import { useToast } from '@/components/ui/use-toast'

type LinkVideoProps = {
  id: number
  link: string | undefined
  isActive: boolean
  isEdit: boolean
}

const initialLink = [
  { id: 1, link: undefined, isActive: false, isEdit: true },
  { id: 2, link: undefined, isActive: false, isEdit: true },
  { id: 3, link: undefined, isActive: false, isEdit: true },
]

const MediaComponentRoot = ({
  video,
  youtubeVideo,
  refetch,
}: PsychologistProfile & { refetch: () => void }) => {
  const { toast } = useToast()
  const videoRef = useRef(null)
  const [linkVideo, setLinkVideo] = useState<LinkVideoProps[]>(initialLink)
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [isLoadFirst, setIsLoadFirst] = useState<boolean>(false)

  useEffect(() => {
    const generateLink = initialLink.map((val, id) => {
      return {
        ...val,
        id: id + 1,
        link: youtubeVideo?.[id]?.link ?? '',
        isActive: youtubeVideo?.[id]?.link ? true : false,
        isEdit: youtubeVideo?.[id]?.link ? false : true,
      }
    })
    if (youtubeVideo?.length) {
      setIsLoadFirst(true)
    }
    setLinkVideo(generateLink)
  }, [youtubeVideo])

  const validateLink = (): boolean => {
    if (linkVideo.length > 3) {
      toast({
        variant: 'danger',
        title: 'Maksimal 3 video',
      })
      return false
    }
    return true
  }

  const handleSubmit = async (newLink: LinkVideoProps['link'], index: number) => {
    setIsLoading(true)
    if (!newLink) {
      toast({
        variant: 'danger',
        title: 'Lengkapi link video dengan data yang valid',
      })
      setIsLoading(false)
      return false
    }
    const isValid = validateLink()
    if (isValid) {
      try {
        const formData = new FormData()
        const linkVideoUpdate = [...linkVideo].map((linkItem, id) => {
          if (id === index) {
            return { ...linkItem, link: newLink, isEdit: false }
          }
          return linkItem
        })
        const link = linkVideoUpdate.filter((val) => !!val.link)
        const payload =
          link.length > 0
            ? link.map((val) => {
                return {
                  link: val.link,
                  title: val.link,
                  description: val.link,
                }
              })
            : []
        formData.append('youtubeVideos', JSON.stringify(payload))
        await profileService.updatePsychologistProfile(formData)
        toast({
          variant: 'success',
          title: 'Perbaharui data youtube berhasil',
        })
        refetch()
        setIsLoading(false)
      } catch (error) {
        setIsLoading(false)
        toast({
          variant: 'danger',
          title: 'Perbaharui data youtube gagal',
        })
      }
    } else {
      setIsLoading(false)
    }
  }

  const handleAdd = (index: number) => {
    const isValid = validateLink()
    isValid &&
      setLinkVideo((prevLinkVideo) =>
        [...prevLinkVideo].map((linkItem, id) => {
          if (id === index) {
            return { ...linkItem, isActive: true }
          }
          return linkItem
        })
      )
  }

  const handleEdit = (index: number) => {
    setLinkVideo((prevLinkVideo) =>
      [...prevLinkVideo].map((linkItem, id) => {
        if (id === index) {
          return { ...linkItem, isEdit: true }
        }
        return linkItem
      })
    )
  }

  const handleRemove = async (index: number) => {
    setIsLoading(true)
    const isValid = validateLink()
    if (isValid) {
      try {
        const formData = new FormData()
        const linkVideoRemove = [...linkVideo].map((linkItem, id) => {
          if (id === index) {
            return { ...linkItem, link: '', isActive: false, isEdit: true }
          }
          return linkItem
        })
        const link = linkVideoRemove.filter((val) => !!val.link)
        const payload =
          link.length > 0
            ? link.map((val) => {
                return {
                  link: val.link,
                  title: val.link,
                  description: val.link,
                }
              })
            : []
        formData.append('youtubeVideos', JSON.stringify(payload))
        await profileService.updatePsychologistProfile(formData)
        toast({
          variant: 'success',
          title: 'Perbaharui data youtube berhasil',
        })
        refetch()
        setIsLoading(false)
      } catch (error) {
        setIsLoading(false)
        toast({
          variant: 'danger',
          title: 'Perbaharui data youtube gagal',
        })
      }
    } else {
      setIsLoading(false)
    }
  }

  const handleUpdateVideo = async (file: any) => {
    try {
      const formData = new FormData()
      formData.append('video', file)
      await profileService.updatePsychologistProfile(formData)
      toast({
        variant: 'success',
        title: 'Perbaharui video berhasil',
      })
    } catch (error) {
      toast({
        variant: 'danger',
        title: 'Perbaharui video gagal',
      })
    }
  }

  const activeLink = [...linkVideo].filter((res) => res.isActive).length

  return (
    <div className="grid grid-cols-2 xs:grid-cols-2 md:grid-cols-3 gap-4">
      <div className="col-span-2">
        <InputFile
          accept={'video/mp4'}
          onChange={(file) => handleUpdateVideo(file)}
          previewVideo={video}
          inputRef={videoRef}
          maxFileSizeMb={10}
        />
      </div>
      <div className="col-span-2">
        <div className="flex justify-between items-center">
          <div>
            <H4 bold>Lampiran Video</H4>
            <AppBigCaption>
              Anda dapat memasukan 3 video youtube yang akan ditampilkan di halman profil.
            </AppBigCaption>
          </div>
          <ButtonPrimary
            disabled={activeLink >= 3}
            variant="outlined"
            size="xs"
            onClick={() => handleAdd(activeLink)}
          >
            Tambah
          </ButtonPrimary>
        </div>
      </div>
      <div className="grid col-span-2 items-center gap-y-4">
        {linkVideo.map((link, index) => {
          if (link.isActive) {
            return (
              <div key={link.id} className="col-span-2">
                <InputYoutubeLink
                  isEdit={link.isEdit}
                  value={link.link}
                  onEdit={() => handleEdit(index)}
                  onRemove={() => handleRemove(index)}
                  onSubmit={(val) => handleSubmit(val, index)}
                  btnShow={link.isActive}
                  isLoading={isLoading}
                />
              </div>
            )
          }
        })}
      </div>
    </div>
  )
}

export default MediaComponentRoot
