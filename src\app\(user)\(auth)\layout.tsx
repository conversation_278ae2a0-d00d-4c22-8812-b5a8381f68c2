'use client'
import Image from 'next/image'

export default function UserLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <>
      <Image
        src={'/ilustration/footer.svg'}
        fill
        alt="footer"
        className="hidden md:block absolute top-0 left-0 object-cover"
      />
      <Image
        src={'/ilustration/footer-mobile.png'}
        fill
        alt="footer"
        className="md:hidden block absolute top-0 left-0 object-cover"
      />

      <div className="pb-4 xs:p-4 md:pt-14 2xl:pt-8  md:mt-[33px] z-1">{children}</div>
      {/* <div className="fixed bottom-bottomUserMenu left-1/2 transform -translate-x-1/2 md:transform-none md:left-auto md:bottom-0 md:right-1 lg:right-10"> */}
    </>
  )
}
