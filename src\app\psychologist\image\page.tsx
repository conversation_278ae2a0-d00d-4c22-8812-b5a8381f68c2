'use client'

import { useGetProfile } from '@/hooks/useGetProfile.hook'
import { toPng } from 'html-to-image'
import Image from 'next/image'
import { useEffect, useRef, useState } from 'react'

const AdminPage = () => {
  const exportRef = useRef<any>()
  const [photo, setPhoto] = useState('')
  const [isDownload, setIsDownload] = useState(false)

  const { data, isFetched } = useGetProfile()

  useEffect(() => {
    if (isFetched) {
      setPhoto(data?.profilePhoto ?? '/mascot.svg')
      setIsDownload(true)
    }
  }, [isFetched])

  useEffect(() => {
    if (isDownload && isFetched) {
      htmlToImageConvert(
        exportRef.current,
        `${data?.fullName ? data?.fullName?.replace(/\s/g, '') : 'profile'}.png`
      )
      setIsDownload(false)
      setTimeout(() => {
        window && window.close()
      }, 4000)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [photo])

  const htmlToImageConvert = (ref: any, filename: string) => {
    toPng(ref, { cacheBust: false })
      .then((dataUrl) => {
        const link = document.createElement('a')
        link.download = filename
        link.href = dataUrl
        link.click()

        link.remove()
      })
      .catch((err) => {
        console.log(err)
      })
  }

  return (
    <div
      ref={exportRef}
      style={{
        display: 'flex',
        flexDirection: 'column',
        rowGap: '4px',
        columnGap: '4px',
        width: '1080px',
        height: '1080px',
        backgroundColor: '#F0FAFF',
        position: 'relative',
        borderRadius: '15px',
        overflow: 'hidden',
      }}
    >
      {/* Logo */}
      <Image
        style={{
          position: 'absolute',
          top: '44px',
          left: '80px',
        }}
        width={328}
        height={54}
        src={'/ilustration/share-logo.png'}
        alt="Logo"
        priority
      />

      {/* Greet */}
      <div
        style={{
          display: 'flex',
          flexDirection: 'column',
          gap: '1px',
          position: 'absolute',
          top: '162px',
          left: '90px',
          rowGap: '12px',
          columnGap: '12px',
        }}
      >
        <p
          style={{
            color: '#004262',
            fontWeight: 'bold',
            fontSize: '40px',
            textTransform: 'uppercase',
          }}
        >
          HALO TEMAN HEALING!
        </p>
        <p
          style={{
            color: '#019EE9',
            fontWeight: 'bold',
            fontSize: '56px',
            textTransform: 'uppercase',
          }}
        >
          KONSELING DENGAN SAYA DI MENTALHEALING.ID
        </p>
      </div>

      {/* Avatar and Details */}
      <div
        style={{
          position: 'absolute',
          top: '420px',
          left: '90px',
          width: '424px',
          height: '466px',
          borderRadius: '15px',
        }}
      >
        <Image
          style={{
            objectFit: 'cover',
            objectPosition: 'bottom',
            borderRadius: '15px',
          }}
          width={424}
          height={466}
          src={photo ?? ''}
          onError={() => setPhoto('/mascot.svg')}
          alt={'photo-' + (data?.nickname ?? '')}
        />
      </div>
      <div
        style={{
          display: 'flex',
          flexDirection: 'column',
          position: 'absolute',
          top: '405px',
          left: '540px',
          rowGap: '12px',
          columnGap: '12px',
          width: '483px',
          height: '466px',
          textTransform: 'uppercase',
          justifyContent: 'center',
        }}
      >
        <p
          style={{
            color: '#004262',
            fontWeight: 'bold',
            fontSize: '36px',
          }}
        >
          {data?.fullName ?? ''}
        </p>
        <span
          style={{
            color: '#019EE9',
            fontWeight: 'bold',
            fontSize: '24px',
          }}
        >
          {data && data?.field?.length > 0 ? data?.field?.map((val: any) => val.name).join(', ') : ''}
        </span>
      </div>

      {/* Background Wave */}
      <div
        style={{
          position: 'absolute',
          bottom: '0',
          left: '0',
          width: '1080px',
          height: '259px',
          zIndex: 10,
        }}
      >
        <Image
          style={{
            position: 'absolute',
            bottom: '0',
            left: '0',
            width: '1080px',
            height: '259px',
            zIndex: 10,
          }}
          width={1080}
          height={259}
          src="/ilustration/posterWave.svg"
          alt="Wave Illustration"
          className="w-full"
        />
        {/* App Download Links */}
        <div
          style={{
            display: 'flex',
            rowGap: '16px',
            columnGap: '16px',
            position: 'absolute',
            width: '822px',
            height: '64px',
            left: '129px',
            top: '140px',
            zIndex: 30,
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <div
            style={{
              position: 'relative',
              width: '822px',
              height: '64px',
            }}
          >
            <span
              style={{
                color: 'white',
                fontWeight: 'bold',
                fontSize: '24px',
                position: 'absolute',
                top: '12px',
              }}
            >
              Download aplikasinya sekarang!
            </span>
            <Image
              style={{
                left: '440px',
                position: 'absolute',
                top: '3px',
              }}
              width={193}
              height={63}
              src="/images/play-store.svg"
              alt="Play Store"
            />
            {/* hdie for a while until app uploaded to the appstore */}
            {/* <Image
              style={{
                left: '650px',
                position: 'absolute',
                top: '0',
              }}
              width={196}
              height={64}
              src="/images/app-store.svg"
              alt="App Store"
            /> */}
          </div>
        </div>
      </div>
    </div>
  )
}

export default AdminPage
