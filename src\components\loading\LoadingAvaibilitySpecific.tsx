export default function LoadingAvaibilitySpecific() {
  return (
    <>
      <div className="flex flex-col gap-4">
        {/* header mobile */}
        <div className="animate-pulse h-5 bg-slate-200 rounded w-[55%] block md:hidden"></div>
        {/* header */}
        <div className="hidden md:flex justify-between items-center">
          <div className="animate-pulse h-5 bg-slate-200 rounded w-[55%]"></div>
          <div className="animate-pulse h-5 bg-slate-200 rounded w-[20%]"></div>
        </div>
        {/* content */}
        <div className="flex flex-col gap-1">
          <div className="animate-pulse h-2 bg-slate-200 rounded w-[75%]"></div>
          <div className="animate-pulse h-2 bg-slate-200 rounded w-[75%]"></div>
          <div className="animate-pulse h-2 bg-slate-200 rounded w-[75%]"></div>
        </div>
        {/* btn mobile */}
        <div className="animate-pulse h-5 bg-slate-200 rounded w-full block md:hidden"></div>
      </div>
    </>
  )
}
