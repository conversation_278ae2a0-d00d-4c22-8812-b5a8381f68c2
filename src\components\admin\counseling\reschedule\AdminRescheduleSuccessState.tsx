import { AvatarWithInfo } from '@/components/_common/CardInfo/AvatarWithInfo'
import { IIcons } from '@/components/_common/icon'
import { ListInformation } from '@/components/_common/ListInformation'
import { AppBigText } from '@/components/_common/ui'
import { dispatch, useSelector } from '@/store'
import { resetRescheduleState } from '@/store/admin/counseling.reducer'
import { format } from 'date-fns'
import { useEffect, useState } from 'react'

export const AdminRescheduleSuccessState = ({ clientName }: { clientName: string }) => {
  const { rescheduleDetail } = useSelector((state) => state.AdminCounseling)
  const { date, time } = rescheduleDetail
  const [detail, setDetail] = useState({ date, time })

  useEffect(() => {
    if (rescheduleDetail) {
      setDetail({ date: rescheduleDetail.date, time: rescheduleDetail.time })
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  return (
    <>
      <div className="grid gap-2 border-b border-line-200 pb-4">
        <AppBigText>
          Kami telah mengirimkan jadwal baru kepada Klien. Setelah Klien mengkonfirmasi jadwal, kami akan
          memberikan notifikasi kepada Anda.
        </AppBigText>
      </div>
      <AvatarWithInfo
        wrapClassName="items-center"
        orientation="column"
        className="w-[60px] h-[60px]"
        image="/images/avatar.jpg"
        name="Hilmi Salim"
      >
        <>
          <AppBigText bold>Konseling untuk {clientName}</AppBigText>
          <ListInformation
            className="py-0 pb-2 border-b-0"
            listItem={[
              { label: detail.date ? format(detail.date, 'PPPP') : '', icon: IIcons.Calendar },
              { label: detail.time ? detail.time : '', icon: IIcons.Time },
            ]}
          />
        </>
      </AvatarWithInfo>
    </>
  )
}
