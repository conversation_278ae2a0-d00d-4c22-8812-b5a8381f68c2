const sanitizeForId = (label: string) => {
  return label
    .toLowerCase()
    .replace(/[^\w\s]|(\s+)/g, (_match: string, group1: string) => (group1 ? '-' : ''))
}

export const getInitialName = (name: string) => {
  if (name.length) {
    let StringName = name.split(' '),
      initials = StringName[0].substring(0, 1).toUpperCase()

    if (StringName.length > 1) {
      initials += StringName[StringName.length - 1].substring(0, 1).toUpperCase()
    }
    return initials
  }
}

export const capitalizeEachWord = (word: string = '') => {
  const finalSentence = word.toLowerCase().replace(/(^\w{1})|(\s+\w{1})/g, (letter) => letter.toUpperCase())
  return finalSentence
}

export { sanitizeForId }
