'use client'

import { CardActionInfo } from '@/components/_common/CardInfo/CardActionInfo'
import { CardUSerInfo } from '@/components/_common/CardInfo/CardUserInfo'
import { ListInfo } from '@/components/_common/CardInfo/ListInfo'
import TabList from '@/components/_common/tabs/TabList'
import { BigTitleTypography } from '@/components/_common/ui'
import TabItem from '@/components/_common/tabs/TabItem'
import { ProfilePsikolog } from './ProfilePsikolog'
import { CounsellingComponent } from '../counseling/CounsellingComponent'
import BalanceHistoryComponent from './BalanceHistory/BalanceHistoryComponent'
import PenaltyComponent from './Penalty/PenaltyComponent'
import { useDetailPsychologistById } from '@/hooks/useDetailPsychologistById.hook'
import { LoadingCardWithAvatar } from '@/components/loading/LoadingCard'
import { LoadingDetailPsychologist } from '@/components/loading/LoadingDetailPsychologist'

const DetailPsikologComponent = ({ idPsychologist }: { idPsychologist: string }) => {
  const { data, isLoading, isPending } = useDetailPsychologistById(idPsychologist)
  const isLoadingApp = isLoading || isPending
  const psychologistName = data?.fullName ?? ''
  const psychologistPhoto = data?.profilePhoto ?? ''
  const field = data?.field?.length ? data?.field.map((val: any) => val.name) : []
  const experience = `${data?.calculatedExperience ?? 0} Tahun pengalaman`
  const phoneNumber = data?.phoneNumber ?? ''
  const listInfo = [field.join(' ,'), experience, phoneNumber]
  console.log(data)
  const detailPsychologist = data ?? {}
  const CounsellingData = [
    {
      label: 'Konseling',
      content: <CounsellingComponent id={idPsychologist} />,
    },
    {
      label: 'Riwayat Saldo',
      content: <BalanceHistoryComponent id={idPsychologist} />,
    },
    {
      label: 'Penalty',
      content: <PenaltyComponent />,
    },
    {
      label: 'Profil Psikolog',
      content: <ProfilePsikolog {...detailPsychologist} />,
    },
  ]
  if (isLoadingApp) return <LoadingDetailPsychologist />
  return (
    <>
      <div className="flex sm:flex sm:items-center pb-6">
        <BigTitleTypography>Detail Psikolog</BigTitleTypography>
      </div>
      <div className="grid grid-flow-row-dense grid-cols-6 grid-rows-1 gap-4">
        {isLoadingApp ? (
          <div className="col-span-6 xs:col-span-6 sm:col-span-6 xl:col-span-4 flex items-center gap-x-6 p-2 xs:p-3 sm:p-4 xl:p-6">
            <LoadingCardWithAvatar />
          </div>
        ) : (
          <CardUSerInfo
            className="col-span-6 xs:col-span-6 sm:col-span-6 xl:col-span-4 flex items-center gap-x-6 p-2 xs:p-3 sm:p-4 xl:p-6"
            image={psychologistPhoto}
            heading={psychologistName}
            subHeading={<ListInfo list={listInfo} />}
          />
        )}
        <CardActionInfo
          className="w-full items-center p-2 col-span-6 xs:col-span-6 sm:col-span-3 xl:col-span-1 xs:p-3 sm:p-4"
          title={'Saldo'}
          value={'Rp.300.000'}
          label={'Lihat Siwayat Saldo'}
          handleAction={() => {}}
        />
        <CardActionInfo
          className="w-full items-center p-2 col-span-6 xs:col-span-6 sm:col-span-3 xl:col-span-1 xs:p-3 sm:p-4"
          title={'Penalty'}
          value={data?.penalty ?? 0}
          label={'Lihat Penalty'}
          handleAction={() => {}}
        />
      </div>
      <div className="gap-4 mt-4">
        <TabList className="sticky top-navbar z-30 bg-white" activeTabIndex={0}>
          {CounsellingData.map((counselling, index) => {
            return (
              <TabItem className="bg-main-100" key={index} label={counselling.label}>
                {counselling.content}
              </TabItem>
            )
          })}
        </TabList>
      </div>
    </>
  )
}

export default DetailPsikologComponent
