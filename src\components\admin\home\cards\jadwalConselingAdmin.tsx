'use client'
import { IIcons, SVGIcons } from '@/components/_common/icon'
import { useGetCounselingCounter } from '@/components/psikolog/home/<USER>/useGetCounselingCounter.hook'
import { Routes } from '@/constans/routes'
import { DividerVerticalIcon } from '@radix-ui/react-icons'
import { useRouter } from 'next/navigation'

export default function JadwalConselingAdmin() {
  // home jadwal conseling
  const router = useRouter()
  const { data } = useGetCounselingCounter()

  const jadwalKonselings = [
    {
      id: 1,
      jumlah: data?.upcoming ?? 0,
      title: 'Akan Datang',
      style: 'py-[12px] px-0 md:px-2 lg:px-4 flex items-center justify-center',
    },
    {
      id: 2,
      jumlah: data?.waiting ?? 0,
      title: 'Menunggu Konfirmasi',
      style: 'py-[12px] px-0 md:px-2 lg:px-4 flex items-center justify-center',
    },
    {
      id: 4,
      jumlah: data?.waitingPayment ?? 0,
      title: 'Menung<PERSON> Pembayaran',
      style: 'py-[12px] px-0 md:px-2 lg:px-4 flex items-center justify-center',
    },
    {
      id: 3,
      jumlah: data?.cancelled ?? 0,
      title: 'Batal',
      style: 'hidden',
    },
  ]
  return (
    <>
      {/* tab ~ dekstop */}
      <div className="hidden md:flex justify-between items-center  py-[34px] px-[24px] bg-white border border-[#EBEBEB] rounded-xl gap-[1px]">
        <div
          className="flex items-center justify-normal md:justify-between gap-0 cursor-pointer"
          onClick={() => router.push(Routes.PsychologistCounseling)}
        >
          <p className="font-bold text-[16px] text-[#242424]">Jadwal Konseling</p>
          <SVGIcons className="" name={IIcons.ArrowRight} />
        </div>
        {jadwalKonselings.map((jadwalKonseling, id) => (
          <>
            <div key={jadwalKonseling.id} className="flex justify-center">
              <div className="flex flex-col px-[10px] md:px-0">
                <p
                  className={`text-[14px] md:text-[16px] font-bold text-center ${jadwalKonseling.id === 2 ? 'text-[#E42B3B]' : 'text-[#222222]'}`}
                >
                  {jadwalKonseling.jumlah}
                </p>
                <p className="text-[12px] md:text-[14px] text-center">{jadwalKonseling.title}</p>
              </div>
            </div>
            {id !== jadwalKonselings?.length - 1 && <DividerVerticalIcon className="text-gray-100" />}
          </>
        ))}
      </div>
      {/* mobile */}
      <div className="md:hidden flex flex-col py-[34px] px-[24px] bg-white border border-[#EBEBEB] rounded-xl gap-10">
        <div className="flex justify-between gap-0 md:gap-4">
          <p className="font-bold text-[16px] md:text-[18px] text-[#242424]">Jadwal Konseling</p>
          <SVGIcons className="" name={IIcons.ArrowRight} />
        </div>
        <div className="flex items-center justify-center gap-4">
          {jadwalKonselings.map((jadwalKonseling, id) => (
            <>
              <div key={jadwalKonseling.id} className="flex items-center gap-[14px]">
                <div className="flex flex-col items-center justify-center">
                  <p
                    className={`text-[14px] md:text-[16px] font-bold text-center ${jadwalKonseling.id === 2 ? 'text-[#E42B3B]' : 'text-[#222222]'}`}
                  >
                    {jadwalKonseling.jumlah}
                  </p>
                  <p className="text-[12px] md:text-[14px] text-center">{jadwalKonseling.title}</p>
                </div>
              </div>
              {id !== jadwalKonselings?.length - 1 && <DividerVerticalIcon className="text-gray-100" />}
            </>
          ))}
        </div>
      </div>
    </>
  )
}
