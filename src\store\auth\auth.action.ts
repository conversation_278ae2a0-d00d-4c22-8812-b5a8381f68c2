import { SignInGoogleResponse } from '@/components/auth/SignIn/SignInComponent'
import { refreshToken } from '@/lib/firebase/auth'
import { firebaseAuth } from '@/lib/firebase/config'
import { authService, OnboardClient } from '@/services/auth.service'
import { createAsyncThunk } from '@reduxjs/toolkit'
import { UserCredential } from 'firebase/auth'

export enum AuthRole {
  SUPERADMIN = 'SuperAdmin',
  CLIENT = 'Client',
  PSIKOLOG = 'Psychologist',
  ADMIN = 'Admin',
}

const types = {
  LOGIN: 'login',
  LOGOUT: 'logout',
  VERIFY: 'verify',
  ONBOARDING: 'onboarding',
}

export type UserAuth = {
  isCompleteOnboard: boolean
  token?: string
  role?: string
  id?: string
}

export const verifyAuth = createAsyncThunk(types.VERIFY, async (data: SignInGoogleResponse, thunkAPI) => {
  try {
    let userAuth: UserAuth = {
      token: undefined,
      role: undefined,
      id: undefined,
      isCompleteOnboard: false,
    }
    const getIdTokenResult = await data.user?.getIdTokenResult()
    if (getIdTokenResult?.claims?.role) {
      userAuth = {
        isCompleteOnboard: true,
        role: String(getIdTokenResult?.claims?.role ? getIdTokenResult?.claims?.role : '') ?? undefined,
        id: getIdTokenResult?.claims?.id ? String(getIdTokenResult?.claims?.id) : undefined,
      }
    }

    const userFirebase = {
      firebaseId: data?.user?.uid,
      email: data?.user?.email,
    }

    return {
      user: { ...userAuth, ...userFirebase },
      firebaseCurrentUser: {
        uid: data?.user?.uid,
        displayName: data?.user?.displayName,
        email: data?.user?.email,
        photoURL: data?.user?.photoURL,
      },
      role: userAuth.role,
      token: getIdTokenResult?.token,
    }
  } catch (error) {
    console.log(error)
    throw thunkAPI.rejectWithValue(error)
  }
})

export const verifyAuthSignInWithEmail = createAsyncThunk(
  types.VERIFY,
  async (data: UserCredential, thunkAPI) => {
    try {
      let userAuth: UserAuth = {
        token: undefined,
        role: undefined,
        id: undefined,
        isCompleteOnboard: false,
      }
      const getIdTokenResult = await data.user?.getIdTokenResult()
      if (getIdTokenResult?.claims?.role) {
        userAuth = {
          isCompleteOnboard: true,
          role: String(getIdTokenResult?.claims?.role ? getIdTokenResult?.claims?.role : '') ?? undefined,
          id: getIdTokenResult?.claims?.id ? String(getIdTokenResult?.claims?.id) : undefined,
        }
      }

      const userFirebase = {
        firebaseId: data?.user?.uid,
        email: data?.user?.email,
      }

      return {
        user: { ...userAuth, ...userFirebase },
        firebaseCurrentUser: {
          uid: data?.user?.uid,
          displayName: data?.user?.displayName,
          email: data?.user?.email,
          photoURL: data?.user?.photoURL,
        },
        role: userAuth.role,
        token: getIdTokenResult?.token,
      }
    } catch (error) {
      console.log(error)
      throw thunkAPI.rejectWithValue(error)
    }
  }
)

export const onboardingAuth = createAsyncThunk(types.ONBOARDING, async (data: OnboardClient, thunkAPI) => {
  try {
    let userAuth: UserAuth = {
      token: undefined,
      role: undefined,
      id: undefined,
      isCompleteOnboard: false,
    }

    const userAuthOnboard = await authService.onboardingAuthPost(data).then((response) => {
      return response.data
    })
    const refreshTokenResult = await refreshToken(userAuthOnboard?.token ?? '')
    const getIdTokenResult = await firebaseAuth.currentUser?.getIdTokenResult()
    userAuth.token = refreshTokenResult?.token
    userAuth.role = String(getIdTokenResult?.claims?.role ? getIdTokenResult?.claims?.role : '') ?? undefined
    userAuth.isCompleteOnboard = getIdTokenResult?.claims?.role ? true : false

    return {
      user: { ...userAuth },
      role: userAuth.role,
      token: userAuth.token,
    }
  } catch (error) {
    console.log(error)
    throw thunkAPI.rejectWithValue(error)
  }
})
