'use client'
import { IIcons, SVGIcons } from '@/components/_common/icon'
import { ListInformation } from '@/components/_common/ListInformation'
import { AppBigText, Card } from '@/components/_common/ui'
import React from 'react'
import { twMerge } from 'tailwind-merge'

type CardCounsellingProps = {
  variant: 'base' | 'success' | 'danger' | 'info'
  status: string
  psikologName: string
  clientName: string
  date: string
  time: string
  method: string
  children: React.ReactNode
}

export const CardCounsellingStatus = ({
  variant = 'base',
  status,
  psikologName,
  clientName,
  date,
  time,
  method,
  children,
}: CardCounsellingProps) => {
  const variantStatus =
    variant === 'success'
      ? 'bg-success-100/10'
      : variant === 'danger'
        ? 'bg-danger-100/10'
        : variant === 'info'
          ? 'bg-main-200/10'
          : 'bg-line-100'

  return (
    <div
      className={`grid grid-flow-cols-dense grid-cols-1 xs:grid-cols-1 lg:grid-cols-3 grid-rows-1 gap-x-4 items-start`}
    >
      <div
        className={twMerge(
          `col-span-3 xs:col-span-3 sm:col-span-3 xl:col-span-2 flex items-center gap-x-6 p-2 pb-4 xs:p-3 sm:p-4 md:p-6 xl:p-6 xs:pb-8 sm:pb-8 md:pb-8 lg:pb-8 xl:pb-8 bg-line-200 rounded-t-card ${variantStatus}`
        )}
      >
        Status: {status}
      </div>
      <Card className="col-span-3 xs:col-span-3 sm:col-span-3 xl:col-span-2 items-center gap-x-6 p-2 xs:p-3 sm:p-4 xl:p-6 relative bg-white -top-4 z-index-10 grid">
        <AppBigText bold>
          Konseling <span className="text-main-100">{clientName}</span> dengan{' '}
          <span className="text-main-100">{psikologName}</span>
        </AppBigText>
        <ListInformation
          listItem={[
            { label: date, icon: IIcons.Calendar },
            { label: time, icon: IIcons.Time },
            { label: method, icon: method === 'Call' ? IIcons.Call : IIcons.VideoCall },
          ]}
        />
        <div className="grid grid-cols-1 grid-rows-1 gap-y-4 pt-4">{children}</div>
      </Card>
    </div>
  )
}
