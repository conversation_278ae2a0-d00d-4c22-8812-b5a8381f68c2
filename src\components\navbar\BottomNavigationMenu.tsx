import Link from 'next/link'
import { IIcons, SVGIcons } from '../_common/icon'
import { Routes } from '@/constans/routes'
import { usePathname } from 'next/navigation'
import { isActiveLink } from '@/utils/linkActiveChecker'
import { MenuItemProps } from '@/constans/SidebarMenu'

export const BottomNavigationMenu = ({ menuList }: { menuList?: MenuItemProps[] }) => {
  const pathname = usePathname()

  const isActive = (item: any) => {
    const active = isActiveLink(pathname, item.route)
    if (active) return true
    if (item.children) {
      return item.children.some((child: any) => isActive(child))
    }
    return false
  }

  return (
    <div className="h-[66px] flex justify-around items-center w-screen bg-white fixed bottom-0 z-30 border-t border-line-200">
      {(menuList?.length
        ? menuList
        : [
            { route: Routes.UserHome, label: 'Home', icon: IIcons.Home },
            { route: Routes.UserSearchPsikolog, label: 'Psikolog', icon: IIcons.Search },
            { route: Routes.Login, label: 'Masuk', icon: IIcons.User },
          ]
      ).map((item) => {
        const isItemActive = isActive(item)
        if (!item.showInMobile) return null
        return (
          <Link key={item.route} href={item.route}>
            <div
              className={`flex flex-col gap-y-1 items-center min-w-16 text-gray-300 hover:text-main-100 ${isItemActive ? 'text-main-100' : ''}`}
            >
              <SVGIcons name={item.icon} />
              <span className="text-caption-md font-medium text-center text-nowrap">
                {item.labelMobile ?? item.label}
              </span>
            </div>
          </Link>
        )
      })}
    </div>
  )
}
