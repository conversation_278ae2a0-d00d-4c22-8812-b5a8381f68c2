import ButtonPrimary from '@/components/_common/ButtonPrimary'
import { SVGIcons } from '@/components/_common/icon'
import { AppModal } from '@/components/_common/Modal/AppModal'

interface NotesData {
  categories: string[]
  expectations: string
  feelings: string
  description: string
}

interface CategoryOption {
  id: string
  name: string
  icon: string
}

interface NotesModalProps {
  isOpen: boolean
  onClose: () => void
  notesData: NotesData
  categoryOptions: CategoryOption[]
  loadingCategories: boolean
  onInputChange: (field: keyof NotesData, value: string | string[]) => void
  toggleCategory: (categoryName: string) => void
  onSave: () => void
  onClear: () => void
}

const NotesModal = ({
  isOpen,
  onClose,
  notesData,
  categoryOptions,
  loadingCategories,
  onInputChange,
  toggleCategory,
  onSave,
  onClear,
}: NotesModalProps) => {
  // Function to get the background color based on category ID
  const getCategoryBgColor = (categoryId: string) => {
    // For now, just returning a default color
    return 'bg-main-50'
  }

  return (
    <AppModal open={isOpen} onClose={onClose} title="Ubah Catatan Konseling" size="lg">
      <div className="p-4">
        <h3 className="font-semibold mb-3">Kategori Masalah</h3>
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-2 mb-4">
          {loadingCategories ? (
            <div className="col-span-full text-center py-4">
              <p>Loading categories...</p>
            </div>
          ) : (
            categoryOptions.map((category) => (
              <button
                key={category.id}
                type="button"
                className={`flex flex-col items-center p-3 rounded-lg transition-colors ${
                  notesData.categories.includes(category.name)
                    ? 'bg-main-50 border-2 border-main-100'
                    : 'border border-gray-200'
                }`}
                onClick={(e) => {
                  e.preventDefault()
                  e.stopPropagation()
                  toggleCategory(category.name)
                }}
              >
                <div className="h-8 w-8 rounded-full bg-white flex items-center justify-center mb-2">
                  {category.icon.startsWith('http') ? (
                    <img src={category.icon} alt={category.name} className="h-5 w-5" />
                  ) : (
                    <SVGIcons name={category.icon as any} className="text-main-100" />
                  )}
                </div>
                <span className="text-xs text-center">{category.name}</span>
              </button>
            ))
          )}
        </div>

        <div className="mb-4">
          <h3 className="font-semibold mb-2">Harapan setelah konseling</h3>
          <textarea
            className="w-full border border-gray-200 rounded-lg p-3 min-h-24"
            placeholder="Apa yang kamu harapkan setelah melakukan konseling ini?"
            value={notesData.expectations}
            onChange={(e) => onInputChange('expectations', e.target.value)}
          />
        </div>

        <div className="mb-4">
          <h3 className="font-semibold mb-2">Detail Keluhan</h3>
          <textarea
            className="w-full border border-gray-200 rounded-lg p-3 min-h-24"
            placeholder="Ceritakan perasaan atau masalah yang ingin kamu konsultasikan..."
            value={notesData.feelings}
            onChange={(e) => onInputChange('feelings', e.target.value)}
          />
        </div>

        <div className="flex gap-3 justify-end">
          <ButtonPrimary
            variant="outlined"
            onClick={(e) => {
              e.preventDefault()
              e.stopPropagation()
              onClear()
            }}
          >
            Hapus Catatan
          </ButtonPrimary>
          <ButtonPrimary
            variant="contained"
            onClick={(e) => {
              e.preventDefault()
              e.stopPropagation()
              onSave()
            }}
          >
            Simpan Perubahan
          </ButtonPrimary>
        </div>
      </div>
    </AppModal>
  )
}

export default NotesModal
