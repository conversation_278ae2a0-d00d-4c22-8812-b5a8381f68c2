import { useToast } from '@/components/ui/use-toast'
import { counsellingService } from '@/services/counselling.service'
import { useQuery } from '@tanstack/react-query'

export const useGetAvailability = (psychologistId: string, dateSelected: string) => {
  const { toast } = useToast()
  return useQuery({
    queryKey: ['CounselingAvailabilityPsychologistList', { psychologistId, dateSelected }],
    queryFn: () =>
      counsellingService
        .getPsychologistAvailability(psychologistId)
        .then((response) => {
          console.log(response)
          return response
        })
        .catch((error) => {
          toast({
            title: 'Gagal',
            description: '<PERSON><PERSON><PERSON><PERSON> masalah dengan server, Silahkan hubungi Admin',
            variant: 'danger',
          })
        }),
  })
}
