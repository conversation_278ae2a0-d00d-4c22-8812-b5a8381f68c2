import { useToast } from '@/components/ui/use-toast'
import { regionService } from '@/services/region.service'
import { capitalizeEachWord } from '@/utils/stringUtils'
import { useQuery } from '@tanstack/react-query'

export const useDistricts = (id: string) => {
  const { toast } = useToast()
  return useQuery({
    queryKey: ['Districts', { id }],
    queryFn: () => {
      if (id) {
        return regionService
          .getDistricts(id)
          .then((response) => {
            const payload = response.map((val: any) => ({
              ...val,
              label: capitalizeEachWord(val.name),
              value: val.id,
            }))
            return payload
          })
          .catch((error) => {
            toast({
              title: 'Gagal',
              description: 'Ter<PERSON>di masalah dengan server, Silahkan hubungi Admin',
              variant: 'danger',
            })
          })
      } else {
        return []
      }
    },
  })
}
