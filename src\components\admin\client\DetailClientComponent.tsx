'use client'

import { CardActionInfo } from '@/components/_common/CardInfo/CardActionInfo'
import { CardUSerInfo } from '@/components/_common/CardInfo/CardUserInfo'
import { ListInfo } from '@/components/_common/CardInfo/ListInfo'
import { useSearchParams } from 'next/navigation'
import { CounsellingComponent } from '../counseling/CounsellingComponent'
import { DetailData } from '../DetailData'
import { ProfileClient } from './ProfileClient'

const DetailClientComponent = ({ clientId }: { clientId: string }) => {
  const searchparam = useSearchParams()
  const TabListData = [
    {
      label: 'Konseling',
      content: <CounsellingComponent />,
    },
    {
      label: 'Riwayat Saldo',
      content: 'Riwayat Saldo',
    },
    {
      label: 'Penalty',
      content: 'Penalty',
    },
    {
      label: 'Profil Klien',
      content: <ProfileClient />,
    },
  ]

  const Header = () => {
    return (
      <div className="grid grid-flow-row-dense grid-cols-6 grid-rows-1 gap-4">
        <CardUSerInfo
          className="col-span-6 xs:col-span-6 sm:col-span-6 xl:col-span-4 flex items-center gap-x-6 p-2 xs:p-3 sm:p-4 xl:p-6"
          image={'https://i.pravatar.cc/150?img=1'}
          heading={searchparam.get('name') || ''}
          subHeading={<ListInfo list={['K0001', '+6281293375028']} />}
        />
        <CardActionInfo
          className="w-full items-center p-2 col-span-6 xs:col-span-6 sm:col-span-3 xl:col-span-1 xs:p-3 sm:p-4"
          title={'Saldo'}
          value={'Rp.300.000'}
          label={'Lihat Siwayat Saldo'}
          handleAction={() => {}}
        />
      </div>
    )
  }

  return <DetailData title={'Klien'} Header={Header} tabList={TabListData} />
}

export default DetailClientComponent
