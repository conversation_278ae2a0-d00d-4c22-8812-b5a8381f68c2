'use client'

import React, { useRef, useState, useEffect } from 'react'
import ButtonPrimary from '@/components/_common/ButtonPrimary'
import AppInput from '@/components/_common/input/Input'
import Avatar from '@/components/navbar/Avatar'
import { UseFormGetValues, UseFormRegister, UseFormReturn, UseFormSetValue } from 'react-hook-form'
import { FormSetting } from '@/components/psikolog/setting/FormSetting'
import { InputImage } from '@/components/_common/InputImage'
import { AppSelect } from '@/components/_common/Select/AppSelect'
import {
  EducationOptionsList,
  EthnicityoptionsList,
  MaritalStatusOptions,
  OccupationOptionsList,
  Otheroptions,
  ReligionOptionsList,
} from '@/constans/onboardOptions'
import { DomiciliesOptions } from '@/components/auth/Onboard/DomiciliesOptions'
import { RadioInput } from '@/components/_common/RadioInput/RadioInput'
import { DatePicker } from '@/components/ui/DatePicker'
import { usePsychologist<PERSON>ield } from './hook/usePsychologistField.hook'
import { usePsychologistProblemcategory } from './hook/usePsychologistProblemcategory.hook'
import { MultiSelect } from '@/components/_common/Select/AppMultiSelect'
import { EducationHistory } from '@/interfaces/profile-service'

interface AddPsikologStepTwoProps {
  pageNumberStep: number
  register: UseFormRegister<any>
  getValues: UseFormGetValues<any>
  setValue: UseFormSetValue<any>
  errors: {
    educationHistory?: {
      [key: number]: {
        level?: { message: string }
        major?: { message: string }
        university?: { message: string }
        entryYear?: { message: string }
        graduationYear?: { message: string }
      }
    }
    [key: string]: any
  }
  onSubmit: () => void
}

const MAX_FILE_SIZE = 2 * 1024 * 1024 // 2MB

export default function AddPsikologStepTwo({
  pageNumberStep,
  register,
  getValues,
  setValue,
  errors,
  onSubmit,
}: AddPsikologStepTwoProps) {
  const [educationData, setEducationData] = useState<Partial<EducationHistory>[]>([
    {
      level: '',
      university: '',
      graduationYear: null,
      major: '',
      entryYear: null,
    },
  ])
  const [previewImage, setPreviewImage] = useState<string | null>(null)
  const ref = useRef<any>(null)
  const { data: psychologistField } = usePsychologistField()
  const { data: psychologistProblemCategory } = usePsychologistProblemcategory()
  const psychologistProblemCategorySelected = getValues('problemCategory')
    ? psychologistProblemCategory?.filter((val: any) => getValues('problemCategory')?.includes(val.id))
    : []

  useEffect(() => {
    const existingEducation = getValues('educationHistory')
    if (existingEducation?.length) {
      setEducationData(existingEducation)
    }
  }, [getValues])

  const handleFileChange = (file: File | null) => {
    if (file) {
      if (!['image/jpeg', 'image/png', 'image/webp'].includes(file.type)) {
        alert('Format file harus JPEG, PNG, atau WEBP')
        return
      }

      if (file.size > MAX_FILE_SIZE) {
        alert('Ukuran file maksimal 2MB')
        return
      }

      setPreviewImage(URL.createObjectURL(file))
      setValue('profilePhoto', file, { shouldValidate: true })
    }
  }

  const handleAddEducation = () => {
    if (educationData.length < 3) {
      const newEducation = {
        level: '',
        university: '',
        graduationYear: null,
        major: '',
        entryYear: null,
      }

      setEducationData([...educationData, newEducation])
      const currentLength = getValues('educationHistory')?.length || 0
      setValue(`educationHistory.${currentLength}`, newEducation)
    }
  }

  const handleDeleteEducation = (index: number) => {
    const newEducationData = educationData.filter((_, i) => i !== index)
    setEducationData(newEducationData)

    // Update form values by removing the deleted education
    const currentEducationHistory = getValues('educationHistory')
    const updatedEducationHistory = currentEducationHistory.filter((_: any, i: number) => i !== index)
    setValue('educationHistory', updatedEducationHistory, { shouldValidate: true })
  }

  return (
    <>
      <div className="flex flex-col gap-[6px] mb-4">
        <h2 className="text-[26px] font-bold">Informasi Pribadi</h2>
        <span className="text-[12px] text-[#737373]">Step {pageNumberStep} dari 4</span>
      </div>

      {/* foto psikolog */}
      <InputImage
        {...register('profilePhoto')}
        name="profilePhoto"
        label="Foto"
        accept="image/jpeg,image/png"
        onChange={handleFileChange}
        preview={previewImage || ''}
        objectPosition="center"
        inputRef={ref}
        maxFileSizeMb={2}
      />

      {/* Bio */}
      <AppInput
        {...register('bio')}
        name="bio"
        label="Bio"
        rows={3}
        type="textarea"
        value={getValues('bio') || ''}
        onChange={(e) => setValue('bio', e.target.value, { shouldValidate: true })}
        errorMsg={!!errors.bio ? String(errors.bio.message) : undefined}
      />
      {/* No. SIPP */}
      <AppInput
        {...register('sipp')}
        label="No. SIPP"
        type="number"
        name="sipp"
        placeholder=""
        value={getValues('sipp') || ''}
        onChange={(e) => setValue('sipp', e.target.value, { shouldValidate: true })}
        errorMsg={!!errors.sipp ? String(errors.sipp.message) : undefined}
      />
      {/* No. STR */}
      <AppInput
        {...register('str')}
        label="No. STR"
        type="number"
        name="str"
        value={getValues('str') || ''}
        onChange={(e) => setValue('str', e.target.value, { shouldValidate: true })}
        errorMsg={!!errors.str ? String(errors.str.message) : undefined}
        placeholder=""
      />

      {/* bidang psikolog / field */}
      <AppSelect
        {...register('field')}
        options={psychologistField || []}
        onChange={(val) => setValue('field', val, { shouldValidate: true })}
        value={getValues('field') ? String(getValues('field')) : ''}
        className="h-[50px]"
        label="Bidang Psikolog"
        name="field"
        placeholder="Pilih salah satu"
        errorMsg={!!errors.field ? String(errors.field.message) : undefined}
      />

      {/* Spesialisasi / problemCategory */}

      <MultiSelect
        options={psychologistProblemCategory ?? []}
        defaultValue={
          psychologistProblemCategorySelected
            ? ([...psychologistProblemCategorySelected]?.map((val: any) => val.id) as string[])
            : []
        }
        onValueChange={(value: string[]) => {
          setValue('problemCategory', Array.isArray(value) ? value : [value], {
            shouldValidate: true,
          })
        }}
        label="Spesialisasi"
        name="problemCategory"
        placeholder="Pilih Spesialisasi"
        errorMsg={!!errors.problemCategory ? String(errors.problemCategory.message) : undefined}
      />

      {/* Pendidikan */}
      {educationData.map((education, index) => (
        <div key={index} className="flex flex-col gap-4">
          <div className="flex justify-between items-center">
            <h3 className="text-[18px] font-semibold">Pendidikan {index + 1}</h3>
            {educationData.length > 1 && (
              <button
                type="button"
                onClick={() => handleDeleteEducation(index)}
                className="text-red-500 hover:text-red-700"
              >
                Hapus
              </button>
            )}
          </div>

          <AppSelect
            options={EducationOptionsList || []}
            onChange={(val) => {
              const newEducationData = [...educationData]
              newEducationData[index].level = val
              setEducationData(newEducationData)
              setValue(`educationHistory.${index}.level`, val, { shouldValidate: true })
            }}
            value={getValues(`educationHistory.${index}.level`) || education.level}
            className="h-[50px]"
            label="Tingkat Pendidikan"
            placeholder="Pilih Pendidikan"
            errorMsg={errors.educationHistory?.[index]?.level?.message}
          />

          <AppInput
            label="Program Studi"
            placeholder="Masukkan Program Studi"
            type="text"
            value={getValues(`educationHistory.${index}.major`) || education.major}
            onChange={(e) => {
              const newEducationData = [...educationData]
              newEducationData[index].major = e.target.value
              setEducationData(newEducationData)
              setValue(`educationHistory.${index}.major`, e.target.value, { shouldValidate: true })
            }}
            errorMsg={errors.educationHistory?.[index]?.major?.message}
          />

          <AppInput
            label="Nama Universitas"
            placeholder="Nama Universitas"
            type="text"
            value={getValues(`educationHistory.${index}.university`) || education.university}
            onChange={(e) => {
              const newEducationData = [...educationData]
              newEducationData[index].university = e.target.value
              setEducationData(newEducationData)
              setValue(`educationHistory.${index}.university`, e.target.value, { shouldValidate: true })
            }}
            errorMsg={errors.educationHistory?.[index]?.university?.message}
          />

          <DatePicker
            placeholder="Tahun Masuk"
            className="py-3 h-[50px] w-full"
            date={getValues(`educationHistory.${index}.entryYear`) || education.entryYear}
            label="Tahun Masuk"
            errorMsg={errors.educationHistory?.[index]?.entryYear?.message}
            onSelect={(date) => {
              const newEducationData = [...educationData]
              newEducationData[index].entryYear = date
              setEducationData(newEducationData)
              setValue(`educationHistory.${index}.entryYear`, date, { shouldValidate: true })
            }}
            captionLayout="dropdown"
            fromYear={1950}
            toYear={new Date().getFullYear()}
          />

          <DatePicker
            placeholder="Tahun Lulus"
            className="py-3 h-[50px] w-full"
            date={getValues(`educationHistory.${index}.graduationYear`) || education.graduationYear}
            label="Tahun Lulus"
            errorMsg={errors.educationHistory?.[index]?.graduationYear?.message}
            onSelect={(date) => {
              const newEducationData = [...educationData]
              newEducationData[index].graduationYear = date
              setEducationData(newEducationData)
              setValue(`educationHistory.${index}.graduationYear`, date, { shouldValidate: true })
            }}
            captionLayout="dropdown"
            fromYear={1950}
            toYear={new Date().getFullYear()}
          />
        </div>
      ))}

      {educationData.length < 3 && (
        <div className="py-4 border-t border-[#EBEBEB] cursor-pointer" onClick={handleAddEducation}>
          <span className="text-[#039EE9]">+ Tambah Data Pendidikan</span>
        </div>
      )}

      {/* Tanggal Lahir */}
      <DatePicker
        placeholder=""
        className="py-3 h-[50px] w-full"
        date={getValues('birthDate') ? new Date(getValues('birthDate')!) : undefined}
        label="Tanggal Lahir"
        errorMsg={!!errors.birthDate ? String(errors.birthDate.message) : undefined}
        onSelect={(date) => setValue('birthDate', date, { shouldValidate: true })}
        captionLayout="dropdown"
        fromYear={new Date().getFullYear() - 100}
        toYear={new Date().getFullYear()}
      />

      {/* Pekerjaan */}
      <AppSelect
        {...register('occupation')}
        options={OccupationOptionsList || []}
        onChange={(val) => setValue('occupation', val, { shouldValidate: true })}
        value={getValues('occupation') ? String(getValues('occupation')) : ''}
        className="h-[50px]"
        label="Pekerjaan"
        name="occupation"
        placeholder="Pilih Pekerjaan"
        errorMsg={!!errors.occupation ? String(errors.occupation.message) : undefined}
      />
      {getValues('occupation') === Otheroptions && (
        <AppInput
          {...register('occupationOther')}
          className="pt-0"
          type="text"
          name="occupationOther"
          placeholder="Silahkan masukan pekerjaan lainnya"
          errorMsg={!!errors.occupationOther ? String(errors.occupationOther.message) : undefined}
        />
      )}
      {/* Status Pernikahan */}
      <RadioInput
        options={MaritalStatusOptions}
        name={'maritalStatus'}
        value={getValues('maritalStatus')!}
        label="Status Pernikahan"
        errorMsg={!!errors.maritalStatus ? String(errors.maritalStatus.message) : undefined}
        onChange={(val) => {
          setValue('maritalStatus', val, { shouldValidate: true })
        }}
      />

      {/* Urutan Bersaudara */}
      <div className="grid gap-2">
        <label className="text-body-md font-bold text-gray-400">Urutan Bersaudara*</label>
        <div className="flex gap-4 items-center">
          <AppInput
            {...register('childTo')}
            className="pt-0 max-w-[105px]"
            type="number"
            name="childTo"
            onChange={(val) => {
              setValue('childTo', val.target.value, { shouldValidate: true })
              const currentBirthOrder = getValues('birthOrder') ?? '0/0'
              const splitBirthOrder = currentBirthOrder.split('/')
              setValue('birthOrder', `${val.target.value}/${splitBirthOrder[1]}`, {
                shouldValidate: true,
              })
            }}
            placeholder="Anak ke"
          />
          <span>Dari</span>
          <AppInput
            {...register('totalSibling')}
            className="pt-0 grow"
            type="number"
            name="totalSibling"
            onChange={(val) => {
              setValue('totalSibling', val.target.value, { shouldValidate: true })
              const currentBirthOrder = getValues('birthOrder') ?? '0/0'
              const splitBirthOrder = currentBirthOrder.split('/')
              setValue('birthOrder', `${splitBirthOrder[0]}/${val.target.value}`, {
                shouldValidate: true,
              })
            }}
            placeholder="Jumlah Saudara"
          />
        </div>
        <label className="text-red-400 text-body-sm">
          {errors.childTo || errors.totalSibling ? 'Urutan Bersaudara harus diisi dengan benar' : undefined}
        </label>
      </div>

      {/* Domisili */}
      <DomiciliesOptions
        register={register}
        getValues={getValues}
        setValue={setValue}
        errors={errors}
        onSubmit={onSubmit}
        onFinalSetValue={(val: string) => setValue('domicile', val, { shouldValidate: true })}
      />

      {/* Suku Bangsa */}
      <AppSelect
        {...register('ethnicity')}
        options={EthnicityoptionsList || []}
        onChange={(val) => setValue('ethnicity', val, { shouldValidate: true })}
        value={getValues('ethnicity') ? String(getValues('ethnicity')) : ''}
        className="h-[50px]"
        label="Suku Bangsa"
        name="ethnicity"
        placeholder="Pilih Suku Bangsa"
        errorMsg={!!errors.ethnicity ? String(errors.ethnicity.message) : undefined}
      />
      {getValues('ethnicity') === Otheroptions && (
        <AppInput
          {...register('ethnicityOther')}
          className="pt-0"
          type="text"
          name="ethnicityOther"
          placeholder="Silahkan masukan suku bangsa lainnya"
          errorMsg={!!errors.ethnicityOther ? String(errors.ethnicityOther.message) : undefined}
        />
      )}

      {/* Agama */}
      <AppSelect
        {...register('religion')}
        options={ReligionOptionsList || []}
        onChange={(val) => setValue('religion', val, { shouldValidate: true })}
        value={getValues('religion') ? String(getValues('religion')) : ''}
        className="h-[50px]"
        label="Agama"
        name="religion"
        placeholder="Pilih Agama"
        errorMsg={!!errors.religion ? String(errors.religion.message) : undefined}
      />
      {getValues('religion') === Otheroptions && (
        <AppInput
          {...register('religionOther')}
          className="pt-0"
          type="text"
          name="religionOther"
          placeholder="Silahkan masukan agama lainnya"
          errorMsg={!!errors.religionOther ? String(errors.religionOther.message) : undefined}
        />
      )}
    </>
  )
}
