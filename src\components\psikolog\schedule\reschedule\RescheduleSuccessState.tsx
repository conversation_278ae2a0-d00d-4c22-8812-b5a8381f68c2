import { AvatarWithInfo } from '@/components/_common/CardInfo/AvatarWithInfo'
import { IIcons } from '@/components/_common/icon'
import { ListInformation } from '@/components/_common/ListInformation'
import { AppBigText } from '@/components/_common/ui'
import useGetPsychologistTimezone from '@/hooks/useGetPsychologistTimezone.hook'
import useGetTimezoneLabel from '@/hooks/useGetTimezone.hook'
import { useSelector } from '@/store'
import { formatStringToFulldateOutput, formatStringToStartEndTimeOutput } from '@/utils/displayDate'
import { format } from 'date-fns'

export const RescheduleSuccessState = ({
  clientName,
  profilePhoto,
  counselingDuration,
}: {
  counselingDuration: number
  clientName: string
  profilePhoto: string
}) => {
  const { rescheduleDetail } = useSelector((state) => state.psikologSchedule)
  const { date, time } = rescheduleDetail

  const psychologistTimezone = useGetPsychologistTimezone()
  const timeZoneLabel = useGetTimezoneLabel()
  const dateLabel = formatStringToFulldateOutput(date ?? '')
  const timeLabel = formatStringToStartEndTimeOutput({
    date: date && time ? `${date} ${time}` : '',
    duration: counselingDuration,
    timezone: psychologistTimezone,
    timeLabel: timeZoneLabel,
    isUTC: true,
  })

  return (
    <>
      <div className="grid gap-2 border-b border-line-200 pb-4">
        <AppBigText>
          Kami telah mengirimkan jadwal baru kepada Klien. Setelah Klien mengkonfirmasi jadwal, kami akan
          memberikan notifikasi kepada Anda.
        </AppBigText>
      </div>
      <AvatarWithInfo
        wrapClassName="items-left md:items-center flex-row md:flex-col"
        orientation="column"
        className="w-[60px] h-[60px]"
        image={profilePhoto}
        name={clientName}
        heading={
          <AppBigText bold className="text-left md:text-center">
            Konseling untuk {clientName}
          </AppBigText>
        }
        subHeading={
          <ListInformation
            className="py-0 pb-2 border-b-0 text-left md:text-center"
            listItem={[
              { label: dateLabel, icon: IIcons.Calendar },
              { label: timeLabel, icon: IIcons.Time },
            ]}
          />
        }
      >
        {/* <>
          <AppBigText bold>Konseling untuk {clientName}</AppBigText>
          <ListInformation
            className="py-0 pb-2 border-b-0"
            listItem={[
              { label: dateLabel, icon: IIcons.Calendar },
              { label: timeLabel, icon: IIcons.Time },
            ]}
          />
        </> */}
      </AvatarWithInfo>
    </>
  )
}
