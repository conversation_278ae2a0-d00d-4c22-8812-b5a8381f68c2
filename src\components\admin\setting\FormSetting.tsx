'use client'
import ButtonPrimary from '@/components/_common/ButtonPrimary'
import { Separator } from '@/components/ui/separator'

type FormInputSettingProps = {
  viewComponent: React.ReactNode
  editComponent: React.ReactNode
  label: string
  isEdit?: boolean
  withMargin?: boolean
  isLoading?: boolean
  disableEdit?: boolean
  onSubmit?: () => void
  onCancel?: () => void
  onEditButton?: () => void
}

export const FormSetting = ({
  label,
  isEdit,
  withMargin,
  onSubmit,
  viewComponent,
  editComponent,
  onCancel,
  onEditButton,
  isLoading,
  disableEdit,
}: FormInputSettingProps) => {
  const toggleIsEdit = () => {
    if (isEdit) {
      onCancel && onCancel()
    } else {
      onEditButton && onEditButton()
    }
  }
  return (
    <>
      <div className="flex flex-col py-4">
        <div className="flex justify-between w-full">
          <span className="text-gray-200 text-body-sm">{label}</span>
          {disableEdit ? null : (
            <span
              className="text-body-md font-bold text-main-100 hover:text-main-200 cursor-pointer w-[40px] text-right"
              onClick={toggleIsEdit}
            >
              {isEdit ? 'Batal' : 'Atur'}
            </span>
          )}
        </div>
        <div className={isEdit ? 'py-6' : ''}>
          <div className={`${withMargin ? 'mr-[40px]' : ''}`}>{isEdit ? editComponent : viewComponent}</div>
          {isEdit && (
            <div className="mt-2">
              <ButtonPrimary
                disabled={isLoading}
                isLoading={isLoading}
                variant="contained"
                size="xs"
                onClick={() => onSubmit && onSubmit()}
              >
                Simpan
              </ButtonPrimary>
            </div>
          )}
        </div>
      </div>
      {!isEdit && <Separator orientation="horizontal" />}
    </>
  )
}
