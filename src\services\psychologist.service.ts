import { config } from '@/constans/config'
import { PsychologistStatus } from '@/constans/StaticOptions'
import { httpRequest } from '@/utils/network'

export class PsychologistService {
  async getPsychologistForUserById(id: string) {
    const url = `${config?.apiBaseUrl}api/psychologists/${id}/details`
    return await httpRequest({
      method: 'get',
      url,
    })
  }

  async getPsychologistScheduleByDate(id: string, date: string) {
    const url = `${config?.apiBaseUrl}api/psychologists/${id}/details?date=${date}`
    return await httpRequest({
      method: 'get',
      url,
    })
  }
  async getPsychologistAvailabilityDateById(id: string) {
    const url = `${config?.apiBaseUrl}api/psychologists/${id}/date-availability`
    return await httpRequest({
      method: 'get',
      url,
    })
  }
  // async getPsychologistSceduleById(id: string) {
  //   const url = `${config?.apiBaseUrl}api/psychologists/${id}/schedule`
  //   return await httpRequest({
  //     method: 'get',
  //     url,
  //   })
  // }

  async getPsychologistById(id: string) {
    return await httpRequest({
      method: 'get',
      url: `${config?.apiBaseUrl}api/psychologists/${id}`,
    })
  }
  async getPsychologist() {
    return await httpRequest({
      method: 'get',
      url: `${config?.apiBaseUrl}api/psychologists?orderBy={ "createdAt": "asc" }&perPage=100`,
    })
  }
  async getPsychologistField() {
    return await httpRequest({
      method: 'get',
      url: `${config?.apiBaseUrl}api/psychologist-field`,
    })
  }
  async getPsychologistProblemCategory() {
    return await httpRequest({
      method: 'get',
      url: `${config?.apiBaseUrl}api/problem-category`,
    })
  }
  async getPsychologistByCategory(category: string) {
    return await httpRequest({
      method: 'get',
      url: `${config?.apiBaseUrl}api/psychologists?orderBy={ "createdAt": "asc" }&where={ "problemCategory": { "some" : { "problemCategory": { "in": ["${encodeURIComponent(category)}"] } } } }&perPage=100`,
    })
  }
  async adminGetPsychologistDetails(id: string) {
    return await httpRequest({
      method: 'get',
      url: `${config?.apiBaseUrl}api/psychologists-admin/${id}`,
    })
  }
  async adminGetPsychologistDateAvailability(id: string) {
    return await httpRequest({
      method: 'get',
      url: `${config?.apiBaseUrl}api/psychologists/${id}/date-availability`,
    })
  }
  async patchPsychologist(id: string, payload: FormData) {
    return await httpRequest({
      method: 'patch',
      url: `${config?.apiBaseUrl}api/psychologists/${id}`,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      data: payload,
    })
  }
  async postPsychologist(payload: FormData) {
    return await httpRequest({
      method: 'post',
      url: `${config?.apiBaseUrl}api/psychologists/`,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      data: payload,
    })
  }

  async postPsychologistWithdrawal(payload: FormData, id: string) {
    return await httpRequest({
      method: 'post',
      url: `${config?.apiBaseUrl}api/psychologists/${id}/withdraw/`,
      headers: {
        // 'Content-Type': 'multipart/form-data',
      },
      data: payload,
    })
  }

  async postPsychologistDisable(id: string) {
    return await httpRequest({
      method: 'post',
      url: `${config?.apiBaseUrl}api/user-identity/${id}/disable`,
    })
  }
  async postPsychologistEnable(id: string) {
    return await httpRequest({
      method: 'post',
      url: `${config?.apiBaseUrl}api/user-identity/${id}/enable`,
    })
  }
  async adminGetPsychologistList(status: string, searchName: string) {
    const statusQuery = status === PsychologistStatus.ALL ? '' : `"userIdentity":{"isActive": ${status}},`
    const searchNameQuery = searchName !== '' ? `"${searchName}"` : '""'
    const urlEndpoint = `${config?.apiBaseUrl}api/psychologists-admin?where={${statusQuery} "fullName":{"contains":${searchNameQuery}}}&page=1&perPage=100`
    return await httpRequest({
      method: 'get',
      url: urlEndpoint,
    })
  }

  // Fixed getPsychologistFiltered function
  async getPsychologistFiltered(
    specializations: string[],
    experience: string | string[],
    date: string | string[],
    times: string | string[],
    searchPsikolog: string = ''
  ) {
    const whereClause: Record<string, any> = {}

    // Filter by search name (optional)
    if (searchPsikolog) {
      whereClause.fullName = {
        contains: searchPsikolog,
      }
    }

    // Filter by specializations
    if (specializations && specializations.length > 0) {
      whereClause.problemCategory = {
        some: {
          problemCategory: {
            in: specializations,
          },
        },
      }
    }

    // Format parameters
    const params = new URLSearchParams({
      where: JSON.stringify(whereClause),
      page: '1',
      perPage: '100',
    })

    if (date) {
      if (Array.isArray(date)) {
        params.append('date', date.join(','))
      } else {
        params.append('date', date)
      }
    }

    if (times) {
      if (Array.isArray(times)) {
        params.append('times', times.join(','))
      } else {
        params.append('times', times)
      }
    }

    if (experience) {
      if (Array.isArray(experience)) {
        params.append('experience', experience.join(','))
      } else {
        params.append('experience', experience)
      }
    }

    const url = `${config?.apiBaseUrl}/api/psychologists?${params.toString()}`

    console.log('URL:', url)

    return await httpRequest({
      method: 'get',
      url,
    })
  }
}

export const psychologistService = new PsychologistService()
