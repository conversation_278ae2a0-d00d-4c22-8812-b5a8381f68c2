import { useState } from 'react'
import { profileService } from '@/services/profile.service'

interface NotificationError {
  message: string
  error: string
  statusCode: number
}

export const useNotificationSettings = () => {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<NotificationError | null>(null)
  const [success, setSuccess] = useState(false)

  const updateNotificationStatus = async (type: 'counseling' | 'news', status: boolean) => {
    try {
      setLoading(true)
      setError(null)
      setSuccess(false)

      const payload =
        type === 'counseling'
          ? { NOTIFICATION_COUNSELING_STATUS: status }
          : { NOTIFICATION_NEWS_UPDATE: status }

      await profileService.clientUpdateNotificationStatus(payload)
      setSuccess(true)
    } catch (err: any) {
      if (err.response?.data) {
        setError(err.response.data)
      } else {
        setError({
          message: 'Gagal memperbarui status notifikasi',
          error: 'Error',
          statusCode: 500,
        })
      }
    } finally {
      setLoading(false)
    }
  }

  return {
    updateNotificationStatus,
    loading,
    error,
    success,
  }
}
