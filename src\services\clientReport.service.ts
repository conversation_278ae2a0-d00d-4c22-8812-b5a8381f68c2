import { config } from '@/constans/config'
import { httpRequest } from '@/utils/network'

export type ClientReportProps = {
  psychologistId: string
  clientId: string
  counselingId: string
  activityId: string
  data: string
  anamnesis: string
  intervention: string
  task: string
  assessmentFile: string
  notesForClient: string
  nextCounselingAnswer: string
  nextCounselingSchedule?: string
  nextCounselingDate?: string
  nextCounselingDuration?: string
  createdAt?: string
  createdBy?: string
  modifiedAt?: string
  modifiedBy?: string
}

export class ClientReportService {
  async getAllClientReport() {
    return await httpRequest({
      method: 'get',
      url: `${config?.apiBaseUrl}api/psychologists/client-reports?page=1&perPage=1000`,
    })
  }
  async getIncomleteClientReport() {
    return await httpRequest({
      method: 'get',
      url: `${config?.apiBaseUrl}api/psychologists/client-reports?where={ "clientReport": null }&page=1&perPage=1000`,
    })
  }
  async createClientReport(payload: FormData, counselingId: string) {
    return await httpRequest({
      method: 'post',
      url: `${config?.apiBaseUrl}api/psychologists/counseling/${counselingId}/client-report`,
      data: payload,
    })
  }
  async updateClientReport(payload: FormData, counselingId: string) {
    return await httpRequest({
      method: 'put',
      url: `${config?.apiBaseUrl}api/psychologists/counseling/${counselingId}/client-report`,
      data: payload,
    })
  }
  async adminGetClientReport(id: string) {
    return await httpRequest({
      method: 'get',
      url: `${config?.apiBaseUrl}api/client-reports/${id}`,
    })
  }
  async adminRemindPsychologistClientReport(id: string) {
    return await httpRequest({
      method: 'post',
      url: `${config?.apiBaseUrl}api/client-reports/${id}/remind`,
    })
  }
  async adminUpdateClientReport(payload: FormData, id: string) {
    return await httpRequest({
      method: 'patch',
      url: `${config?.apiBaseUrl}api/client-reports/${id}`,
      data: payload,
    })
  }
  async adminDeleteClientReport(id: string) {
    return await httpRequest({
      method: 'delete',
      url: `${config?.apiBaseUrl}api/client-reports/${id}`,
    })
  }
}

export const clientReportService = new ClientReportService()
