import Avatar from '@/components/navbar/Avatar'

export default function Banner({ name, email, noHp }: { name: string; email: string; noHp: number }) {
  return (
    <>
      {/* banner */}
      <figure className="w-full h-full relative">
        <img
          src="/images/banner/bannerUserProfile.svg"
          alt=""
          className="z-0 h-[142px] md:h-[110px] md:max-h-[110px] w-full hidden md:block object-cover object-right rounded-none md:rounded-[15px]"
        />
        <img
          src="/images/banner/bannerUserProfileMobile.svg"
          alt=""
          className="z-0 h-[142px] md:h-[110px] md:max-h-[110px] w-full block md:hidden object-cover object-top rounded-none md:rounded-[15px]"
        />
        <div className="absolute left-4 md:left-10 top-6 z-10 flex items-center gap-[14px]">
          <figure className="border-2 border-white rounded-full w-[64px] h-[64px] flex justify-center items-center">
            <Avatar height={60} width={60} image={'/images/avatar.jpg'} />
          </figure>
          <figcaption className="flex flex-col gap-[2px]">
            <span className="font-bold text-white text-[16px]">{name}</span>
            <span className="text-white text-[14px]">{email}</span>
            <span className="text-white text-[14px]">+{noHp}</span>
          </figcaption>
        </div>
      </figure>
    </>
  )
}
