import { IIcons, SVGIcons } from '@/components/_common/icon'

export default function HeaderUbahClient({
  client,
  date,
  time,
  via,
}: {
  client: string
  date: string
  time: string
  via: string
}) {
  return (
    <div className="flex flex-col gap-1">
      <h1 className="text-[36px] font-bold text-[#222222]">Ubah Klien Report untuk {client}</h1>
      {/* jadwal */}
      <div className="flex items-center gap-3 md:gap-6 flex-wrap">
        <span className="text-[16px] text-[#222222]">Sesi konseling:</span>
        <div className="flex items-center gap-1">
          <SVGIcons className="mr-2" name={IIcons.Calendar} />
          <span className="text-[16px] text-[#222222]">{date}</span>
        </div>
        <div className="flex items-center gap-1">
          <SVGIcons className="mr-2" name={IIcons.Clock} />
          <span className="text-[16px] text-[#222222]">{time}</span>
        </div>
        <div className="flex items-center gap-1">
          <SVGIcons className="mr-2" name={IIcons.Call} />
          <span className="text-[16px] text-[#222222]">{via}</span>
        </div>
      </div>
    </div>
  )
}
