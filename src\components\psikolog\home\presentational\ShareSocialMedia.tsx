'use client'

export default function ShareSocialMedia({
  handleClickAccountInformation,
}: {
  handleClickAccountInformation: () => void
}) {
  return (
    <>
      <div className="flex flex-col gap-[16px] bg-white rounded-[15px] border border-[#EBEBEB] p-[12px] md:p-[24px] w-[260px] md:w-auto h-[107px] md:h-auto">
        <div className="flex flex-col gap-[6px]">
          <div className="flex justify-between items-center">
            <p className="font-bold text-[14px] md:text-[16px] text-[#222222]">Share akun ke sosial media</p>
            {/* <SVGIcons className="ml-2" name={IIcons.ArrowRight} /> */}
          </div>
          <p className="text-[#535353] text-[12px] md:text-[14px]">
            <span className="hidden md:inline-block">
              <PERSON><PERSON><PERSON> followers dan circle Anda tahu kalau Anda menerima konseling online di Mentalhealing.id.
            </span>
          </p>
        </div>
        <button
          onClick={handleClickAccountInformation}
          className="w-full group font-bold border border-[#039EE9] text-[#039EE9] p-1 md:py-[10px] md:px-[16px] rounded-[15px] relative overflow-hidden"
        >
          <span className="relative z-10 group-hover:text-white delay-200">Share</span>
          <div className="w-full h-full absolute group-hover:bottom-0 -bottom-20 left-0 bg-[#039EE9] transition-all ease-in-out duration-700"></div>
        </button>
      </div>
    </>
  )
}
