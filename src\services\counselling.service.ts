import { config } from '@/constans/config'
import { PaymentStatus } from '@/constans/StaticOptions'
import { httpRequest } from '@/utils/network'
import { content } from 'html2canvas/dist/types/css/property-descriptors/content'
interface CounselingForecastPayload {
  psychologistId: string
  problemCategory: string[]
  method: 'Call' | 'VideoCall'
  location: 'Online' | 'Offline'
  schedule: string
  complaint: string
  expectation: string
  description: string
  duration: number
  voucherId?: string
}
export class CounsellingService {
  async clientGetDetailCounsellingById(idCounseling: string) {
    try {
      const response = await httpRequest({
        method: 'get',
        url: `${config?.apiBaseUrl}api/clients/counseling/${idCounseling}`,
      })

      console.log('Service Response:', response)
      return response
    } catch (error) {
      console.error('Service Error:', error)
      throw error
    }
  }

  async clientUpdateNoteCounselling(
    payload: { problemCategory: string[]; complaint: string; expectation: string },
    idCounseling: string
  ) {
    try {
      const response = await httpRequest({
        method: 'patch',
        url: `${config?.apiBaseUrl}api/counselings/${idCounseling}/update-note`,
        data: payload,
      })
      console.log('Service Response:', response)
      return response
    } catch (error) {
      console.error('Service Error:', error)
      throw error
    }
  }

  async clientGetListCounselling(categories: PaymentStatus[], page: number, perPage: number) {
    const statusQuery = JSON.stringify({ status: { in: categories } })

    return await httpRequest({
      method: 'get',
      url: `${config?.apiBaseUrl}api/clients/counseling?where=${statusQuery}&page=${page}&perPage=${perPage}`,
    })
  }

  async getCounsellingByCategory(category: PaymentStatus[]) {
    return await httpRequest({
      method: 'get',
      url: `${config?.apiBaseUrl}api/psychologists/counseling?page=1&perPage=500&where={ "status": { "in": ${JSON.stringify(category)} } }`,
    })
  }
  async approveCounselling(id: string) {
    return await httpRequest({
      method: 'post',
      url: `${config?.apiBaseUrl}api/psychologists/counseling/${id}/approve`,
    })
  }
  async startCounselling(id: string) {
    return await httpRequest({
      method: 'post',
      url: `${config?.apiBaseUrl}api/psychologists/counseling/${id}/inprogress`,
    })
  }
  async completeCounselling(id: string) {
    return await httpRequest({
      method: 'post',
      url: `${config?.apiBaseUrl}api/psychologists/counseling/${id}/complete`,
    })
  }
  async rescheduleCounselling(id: string, payload: { schedule: string; message: string }) {
    return await httpRequest({
      method: 'post',
      url: `${config?.apiBaseUrl}api/psychologists/counseling/${id}/reschedule`,
      data: payload,
    })
  }
  async rejectCounselling(payload: { message: string }, id: string) {
    return await httpRequest({
      method: 'post',
      url: `${config?.apiBaseUrl}api/psychologists/counseling/${id}/reject`,
      data: payload,
    })
  }
  async cancelCounselling(payload: { message: string }, id: string) {
    return await httpRequest({
      method: 'post',
      url: `${config?.apiBaseUrl}api/psychologists/counseling/${id}/cancel`,
      data: payload,
    })
  }
  async getCounselingbyId(counselingId: string) {
    return await httpRequest({
      method: 'get',
      url: `${config?.apiBaseUrl}api/psychologists/counseling/${counselingId}`,
    })
  }
  async getPsychologistAvailability(psychologistId: string) {
    return await httpRequest({
      method: 'get',
      url: `${config?.apiBaseUrl}api/psychologists/${psychologistId}/details`,
    })
  }
  async getScheduleBydate(date: string) {
    return await httpRequest({
      method: 'get',
      url: `${config?.apiBaseUrl}api/psychologists/schedule?date=${date}`,
    })
  }
  async getPsychologistScheduleBydate(id: string, date: string) {
    return await httpRequest({
      method: 'get',
      url: `${config?.apiBaseUrl}api/psychologists/${id}/schedule?date=${date}`,
    })
  }
  async getCounselingByPsychologist(id: string) {
    return await httpRequest({
      method: 'get',
      url: `${config?.apiBaseUrl}api/psychologists/${id}/counseling`,
    })
  }

  async adminGetCounselingById(id: string) {
    return await httpRequest({
      method: 'get',
      url: `${config?.apiBaseUrl}api/counselings/${id}`,
    })
  }

  async adminRescheduleCounselling(
    id: string,
    payload: { imitate: string; schedule: string; message: string }
  ) {
    return await httpRequest({
      method: 'post',
      url: `${config?.apiBaseUrl}api/counselings/${id}/reschedule`,
      data: payload,
    })
  }

  async adminCancelCounselling(payload: { message: string }, id: string) {
    return await httpRequest({
      method: 'post',
      url: `${config?.apiBaseUrl}api/counselings/${id}/cancel`,
      data: payload,
    })
  }
  async adminApproveCounselling(id: string) {
    return await httpRequest({
      method: 'post',
      url: `${config?.apiBaseUrl}api/counselings/${id}/approve`,
    })
  }
  async adminRejectCounselling(payload: { message: string }, id: string) {
    return await httpRequest({
      method: 'post',
      url: `${config?.apiBaseUrl}api/counselings/${id}/reject`,
      data: payload,
    })
  }
  async adminRemindClientReport(id: string) {
    return await httpRequest({
      method: 'post',
      url: `${config?.apiBaseUrl}api/counselings/${id}/remind-client-report`,
    })
  }

  async postCounselingForecast(payload: CounselingForecastPayload) {
    const url = `${config?.apiBaseUrl}api/counselings/forecast`
    console.log('payload', payload)
    console.log('url', url)
    return await httpRequest({
      method: 'post',
      url,
      data: payload,
    })
  }

  async counselingControllerCreateByForecastId(payload: any) {
    const url = `${config?.apiBaseUrl}api/counselings`
    console.log('url', url)
    return await httpRequest({
      method: 'post',
      url,
      data: payload,
    })
  }

  async getListVoucherAvailable() {
    return await httpRequest({
      method: 'get',
      url: `${config?.apiBaseUrl}api/vouchers/available`,
    })
  }

  async getCounselingVoucherByIdUser(idVoucher: string) {
    return await httpRequest({
      method: 'get',
      url: `${config?.apiBaseUrl}api/vouchers/check/${idVoucher}`,
    })
  }
}

export const counsellingService = new CounsellingService()
