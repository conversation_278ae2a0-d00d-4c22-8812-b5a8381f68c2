import FormToggle from '../formToggle'
import { useState } from 'react'
import { useNotificationSettings } from '../hook/useClientSettingNotification'
import { useProfileInformation } from '../hook/useClientInformationProfile'

export default function NotifSetting() {
  const { profileData } = useProfileInformation()
  const { updateNotificationStatus, loading, error, success } = useNotificationSettings()
  const [counselingStatus, setCounselingStatus] = useState(false)
  const [newsStatus, setNewsStatus] = useState(false)

  const handleCounselingStatusChange = async (status: boolean) => {
    await updateNotificationStatus('counseling', status)
    if (!error) {
      setCounselingStatus(status)
    }
  }

  const handleNewsStatusChange = async (status: boolean) => {
    await updateNotificationStatus('news', status)
    if (!error) {
      setNewsStatus(status)
    }
  }

  return (
    <div className="space-y-4">
      <FormToggle
        title="Status konseling"
        contentType=""
        content="Setiap adanya perubahan status konselingmu."
        titleModal="Atur Status konseling"
        showModal={false}
        isActive={counselingStatus}
        onToggle={handleCounselingStatusChange}
      />

      {error && <div className="text-red-500 text-sm mt-2">{error.message}</div>}
      {success && (
        <div className="text-green-500 text-sm mt-2">Notification settings updated successfully!</div>
      )}
      {loading && <div className="text-gray-500 text-sm mt-2">Updating notification settings...</div>}

      <FormToggle
        title="Berita & Pemberitahuan"
        contentType=""
        content="Dapatkan informasi terbaru tentang layanan dan berita."
        titleModal="Atur Berita & Pemberitahuan"
        showModal={false}
        isActive={newsStatus}
        onToggle={handleNewsStatusChange}
      />
    </div>
  )
}
