'use client'
import ButtonPrimary from '@/components/_common/ButtonPrimary'
import AppInput from '@/components/_common/input/Input'
import { Separator } from '@/components/ui/separator'
import AppInputInterface from '@/interfaces/components/_common/input'
import { ChangeEvent, useRef, useState } from 'react'

type FormInputSettingProps = {
  label: string
  type: any
  value?: any
  children: React.ReactNode
  isEdit?: boolean
  onChange?: (value: string) => void
  withMargin?: boolean
  onSubmit?: () => void
  onCancel?: () => void
  renderViewMode: React.ReactNode
  option?: any
}

export const FormInputSetting = ({
  label,
  isEdit,
  type,
  onChange,
  value,
  children,
  withMargin,
  onSubmit,
  renderViewMode,
  onCancel,
  option,
}: FormInputSettingProps) => {
  // const inputRef = useRef<HTMLInputElement>(null)
  // const [inputValue, setInputValue] = useState<any>(value || '')
  // const [newPassword, setNewPassword] = useState<string>('')
  // const [rePassword, setRePassword] = useState<string>('')
  // const [error, setError] = useState<any>({})
  const [toggleEdit, setToggleEdit] = useState<boolean>(isEdit || false)

  // const handleInputChange = (event: any) => {
  //   setInputValue(event.target.value)
  // }

  const toggleIsEdit = () => {
    if (toggleEdit) onCancel && onCancel()
    setToggleEdit((prevToggleEdit) => !prevToggleEdit)
  }

  // const handleSubmit = () => {
  //   const validate = validateForm()
  //   validate && onSubmit && onSubmit(rest.value)
  // }

  // const handleSetError = (field: string, message: string) => {
  //   setError((prevError: any) => ({ ...prevError, [field]: message }))
  // }

  // const validateForm = () => {
  //   if (rest.type === 'password') {
  //     if (newPassword && rePassword && rePassword != newPassword) {
  //       handleSetError('rePassword', 'Password tidak sama')
  //       return false
  //     }
  //   }
  //   handleSetError('rePassword', '')
  //   return true
  // }

  return (
    <>
      <div className="flex flex-col py-4">
        <div className="flex justify-between w-full">
          <span className="text-gray-200 text-body-sm">{label}</span>
          <span
            className="text-body-md font-bold text-main-100 hover:text-main-200 cursor-pointer w-[40px] text-right"
            onClick={toggleIsEdit}
          >
            {toggleEdit ? 'Batal' : 'Atur'}
          </span>
        </div>
        <div className={toggleEdit ? 'py-6' : ''}>
          <div className={`${withMargin ? 'mr-[40px]' : ''}`}>{toggleEdit ? children : renderViewMode}</div>
          {/* {rest.type === 'password' && toggleEdit ? (
          <>
            <div className={`${withMargin ? 'mr-[40px]' : ''}`}>
              <AppInput
                label={'Password Baru'}
                className="mt-2"
                type={type}
                value={newPassword}
                onChange={(e: ChangeEvent<HTMLInputElement>) => {
                  setNewPassword(e.target.value)
                  }}
                  />
                  </div>
                  <div className={`${withMargin ? 'mr-[40px]' : ''}`}>
                  <AppInput
                  ref={inputRef}
                  label={'Ulangi Password Baru'}
                  className="mt-2"
                  type={type}
                  value={rePassword}
                  onChange={(e: ChangeEvent<HTMLInputElement>) => setRePassword(e.target.value)}
                errorMsg={error?.rePassword ?? ''}
              />
              </div>
          </>
        ) : null} */}
          {toggleEdit && (
            <div className="mt-2">
              <ButtonPrimary variant="contained" size="xs" onClick={() => onSubmit && onSubmit()}>
                Simpan
              </ButtonPrimary>
            </div>
          )}
        </div>
      </div>
      {!toggleEdit && <Separator orientation="horizontal" />}
    </>
  )
}
