import { useSelector } from '@/store'
import { useState, useEffect } from 'react'

const useGetTimezoneLabel = (zone?: string) => {
  const { userIdentity } = useSelector((state) => state.PsychologistProfile)
  const timezone = zone ? zone : userIdentity?.userConfig?.TIMEZONE
  const [timezoneLabel, setTimezoneLabel] = useState<string>('')

  useEffect(() => {
    if (timezone) {
      let localTimezone = ''
      switch (timezone) {
        case 'Asia/Jakarta':
        case 'Asia/Pontianak':
          localTimezone = 'WIB'
          break
        case 'Asia/Makassar':
          localTimezone = 'WITA'
          break
        case 'Asia/Jayapura':
          localTimezone = 'WIT'
          break
        default:
          localTimezone = 'Unset Timezone'
          break
      }
      setTimezoneLabel(localTimezone)
    }
  }, [timezone])

  return timezoneLabel
}

export default useGetTimezoneLabel
