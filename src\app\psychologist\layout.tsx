'use client'
import ClickOutside from '@/components/_common/ClickOutside'
import { BottomNavigationMenu } from '@/components/navbar/BottomNavigationMenu'
import Navbar from '@/components/navbar/Navbar'
import Sidebar from '@/components/sidebar/Sidebar'
import { Routes } from '@/constans/routes'
import { PsikologMenuItems } from '@/constans/SidebarMenu'
import { useState } from 'react'

export default function AdminLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  const [sidebarOpen, setSidebarOpen] = useState<boolean>(false)

  return (
    <>
      <ClickOutside className="sticky top-0 z-30" onClick={() => setSidebarOpen(false)}>
        <Navbar
          sidebarOpen={sidebarOpen}
          setSidebarOpen={setSidebarOpen}
          onShowAll={Routes.PsychologistNotification}
        />
        <Sidebar menuList={PsikologMenuItems} sidebarOpen={sidebarOpen} setSidebarOpen={setSidebarOpen} />
      </ClickOutside>
      <div className="relative flex flex-1 flex-col lg:ml-sidebar">
        <div className="pb-4 p-4 md:p-6 2xl:p-8">{children}</div>
      </div>
      <div className="mt-bottomUserMenu flex md:hidden">
        <BottomNavigationMenu menuList={PsikologMenuItems} />
      </div>
    </>
  )
}
