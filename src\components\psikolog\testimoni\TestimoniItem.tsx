'use client'
import { IIcons, SVGIcons } from '@/components/_common/icon'
import { AppBigText } from '@/components/_common/ui'
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion'
import React from 'react'
import { TestimoniItemContent } from './TestimoniContent'
import { LabelValue } from '@/components/_common/CardInfo/LabelValue'
import { TestimonyListProps } from './TestimoniList'
import LoadingTestimoni from '@/components/loading/loadingTestimoni'

export const TestimoniItem = ({ itemList, isLoading }: TestimonyListProps) => {
  const listOfContent = itemList && itemList.length > 0 ? itemList : []
  const [value, setValue] = React.useState(listOfContent[0]?.id)
  const isLoadingApp = isLoading
  return (
    <Accordion type="single" collapsible className="w-full" value={value} onValueChange={setValue}>
      {isLoadingApp ? (
        <LoadingTestimoni />
      ) : (
        listOfContent.map((list, listIndex) => {
          const rating = list.rating ? `${list.rating}/5` : 'Unrate'
          return (
            <AccordionItem
              key={listIndex}
              value={list.id}
              className="p-6 my-4 gap-y-4 border border-line-200 rounded-card"
            >
              <AccordionTrigger
                label={value === list.id ? 'Tutup' : 'Lihat Detail'}
                className={`py-0 hover:no-underline items-baseline ${value === list.id ? 'border-b border-line-200' : ''}`}
              >
                <div className="flex flex-col justify-items-start">
                  <span className="flex items-center">
                    <SVGIcons className="mr-2" name={IIcons.Star} />
                    <AppBigText bold>
                      {rating} dari{' '}
                      <span className="text-main-100">{list.clientName ?? list?.client?.fullName}</span>
                    </AppBigText>
                  </span>

                  <LabelValue
                    displayRows
                    labelClass="text-body-sm font-medium text-gray-300 pt-4 text-start"
                    valueClass="font-medium text-gray-400 pb-4 text-start"
                    label="Catatan untuk psikolog"
                    value={list.messageForPsychologist}
                  />
                </div>
              </AccordionTrigger>
              <AccordionContent className="py-4">
                <TestimoniItemContent {...list} />
              </AccordionContent>
            </AccordionItem>
          )
        })
      )}
    </Accordion>
  )
}
