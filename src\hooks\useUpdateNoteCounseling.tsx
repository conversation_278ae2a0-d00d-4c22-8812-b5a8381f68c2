import { useState } from 'react'
import { useToast } from '@/components/ui/use-toast'
import { counsellingService } from '@/services/counselling.service'
import { log } from 'console'

interface UpdateNotePayload {
  problemCategory: string[]
  complaint: string
  expectation: string
}

export const useUpdateCounselingNote = (idCounseling: string) => {
  const [isLoading, setIsLoading] = useState(false)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const { toast } = useToast() // Fix: Properly destructure toast function

  const initialNoteData = {
    problemCategory: [],
    complaint: '',
    expectation: '',
  }

  const [noteData, setNoteData] = useState<UpdateNotePayload>(initialNoteData)

  const openModal = (currentData: {
    problemCategory: Array<{ problemCategory: string }> | string[]
    complaint: string
    expectation: string
  }) => {
    // Transform the data to always use category names
    const formattedCategories = Array.isArray(currentData.problemCategory)
      ? currentData.problemCategory.map((cat) => {
          if (typeof cat === 'string') {
            return cat
          }
          return cat.problemCategory
        })
      : []

    setNoteData({
      problemCategory: formattedCategories,
      complaint: currentData.complaint || '',
      expectation: currentData.expectation || '',
    })
    setIsModalOpen(true)
  }

  const closeModal = () => {
    setIsModalOpen(false)
  }

  const updateNoteData = (field: keyof UpdateNotePayload, value: string | string[]) => {
    setNoteData((prev) => ({ ...prev, [field]: value }))
  }

  const toggleCategory = (categoryName: string) => {
    setNoteData((prev) => {
      const currentCategories = [...prev.problemCategory]
      if (currentCategories.includes(categoryName)) {
        return {
          ...prev,
          problemCategory: currentCategories.filter((name) => name !== categoryName),
        }
      } else {
        return {
          ...prev,
          problemCategory: [...currentCategories, categoryName],
        }
      }
    })
  }

  const clearNoteData = () => {
    setNoteData(initialNoteData)
  }

  const updateCounselingNote = async () => {
    if (!idCounseling) return

    setIsLoading(true)
    try {
      const payload: UpdateNotePayload = {
        problemCategory: noteData.problemCategory,
        complaint: noteData.complaint,
        expectation: noteData.expectation,
      }
      console.log('Payload:', payload)

      await clientUpdateNoteCounselling(payload, idCounseling)
      toast({
        title: 'Berhasil',
        description: 'Catatan konseling berhasil diperbarui',
        variant: 'success',
      })
      closeModal()
      window.location.reload()
    } catch (error) {
      console.error('Error updating counseling note:', error)
      toast({
        title: 'Gagal',
        description: 'Gagal memperbarui catatan konseling',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  const clientUpdateNoteCounselling = async (payload: UpdateNotePayload, idCounseling: string) => {
    try {
      const formattedPayload = {
        problemCategory: payload.problemCategory, // Now sending array of category names
        complaint: payload.complaint,
        expectation: payload.expectation,
      }

      const response = await counsellingService.clientUpdateNoteCounselling(formattedPayload, idCounseling)
      console.log('Service Response:', response)
      return response
    } catch (error) {
      console.error('Service Error:', error)
      throw error
    }
  }

  return {
    isLoading,
    isModalOpen,
    noteData,
    openModal,
    closeModal,
    updateNoteData,
    toggleCategory,
    clearNoteData,
    updateCounselingNote,
  }
}
