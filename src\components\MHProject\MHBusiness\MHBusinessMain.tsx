import { STATIC_DATA } from '@/constans/STATIC_DATA'
import ColaborationsLogo from '../sections/ColaborationsLogo'
import { Correlation } from '../sections/Correlation'
import { Services } from '../sections/Services'
import { Testimony } from '../sections/Testimony/Testimony'
import { OurAdvantages } from '../sections/OurAdvantages'
import { OurApproaches } from '../sections/OurApproaches'
import { AssessmentForm } from '../sections/AssessmentForm'
import { Faq } from '../sections/Faq'
import { Container } from '../sections/Container'

export const MHBusinessMain = () => {
  return (
    <div className="gap-y-[100px] w-full flex flex-col items-center">
      <div className="mt-10 w-full relative">
        <ColaborationsLogo {...STATIC_DATA.MHBusiness.colaboration} />
      </div>
      <div className="min-h-[500px] flex flex-col justify-center max-w-MHmax md:px-10 lg:px-40 relative w-full gap-y-[100px]">
        <Correlation {...STATIC_DATA.MHBusiness.section1} />
      </div>

      <Container className="bg-[#E6F5FD] py-15">
        <Services {...STATIC_DATA.MHBusiness.section2} />
      </Container>

      <div className="min-h-[500px] flex flex-col justify-center max-w-MHmax md:px-10 lg:px-40 relative w-full gap-y-[100px]">
        <Testimony category="MHBusiness" {...STATIC_DATA.MHBusiness.section3} />
        <OurAdvantages {...STATIC_DATA.MHBusiness.section4} />
      </div>

      <Container className="bg-gradient-to-t from-[#E6F5FD] to-white">
        <OurApproaches {...STATIC_DATA.MHBusiness.section5} />
      </Container>

      <div
        id="faq"
        className="min-h-[500px] flex flex-col justify-center max-w-MHmax md:px-10 lg:px-40 relative w-full gap-y-[100px]"
      >
        <div className="px-4">
          <Faq category="MHBusiness" />
        </div>
        <AssessmentForm isCompany />
      </div>
    </div>
  )
}

export default MHBusinessMain
