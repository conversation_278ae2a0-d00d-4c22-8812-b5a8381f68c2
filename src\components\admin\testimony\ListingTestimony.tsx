'use client'

import { DataGrid } from '@/components/datagrid/DataTable'
import { getDummy } from '@/utils/getDummyData'
import { ColumnDef } from '@tanstack/react-table'
import { useRouter } from 'next/navigation'
import { DateRange } from 'react-day-picker'
import { FilterHeader } from '../FilterHeader'
import { useCallback, useEffect, useMemo, useState } from 'react'
import { AppSheet } from '@/components/_common/Sheet/AppSheet'
import TestimoniDetailHeader from './details/testimonyDetailHeader'
import NotesForPsikolog from './details/notesForPsikolog'
import ContentDetailTestimony from './details/contentDetailTestimony'
import moment from 'moment'
import { testimonyService } from '@/services/testimony.service'
import { useToast } from '@/components/ui/use-toast'
import { useDispatch } from '@/store'
import { setIsFetched, setMetaTestimony } from '@/store/admin/meta.reducer'

type ListingTestimonyType = {
  columns: ColumnDef<any, any>
  rangeDate: DateRange | undefined
  actions: string[]
  showSecondFilter?: boolean
  fetchPath: string
  pageFilter?: any[]
  refRefetch: any
}

export const ListingTestimony = ({
  columns,
  rangeDate,
  actions = [],
  fetchPath,
  pageFilter,
  refRefetch,
}: ListingTestimonyType) => {
  const dispatch = useDispatch()
  const { toast } = useToast()
  const [toggle, setToggle] = useState<boolean>(false)
  const [testimonyData, setTestimonyData] = useState<any>(null)

  const toggleSheet = () => {
    setToggle((prev) => !prev)
  }

  useEffect(() => {
    if (toggle === false) {
      setTestimonyData(null)
    }
  }, [toggle])

  const router = useRouter()
  const handleRemoveTestimony = useCallback(
    async (id?: string) => {
      try {
        await testimonyService.adminRemoveTestimony(id ? id : testimonyData?.id)
        toast({
          variant: 'success',
          title: 'Berhasil Menghapus Testimoni',
        })
        setTimeout(() => {
          setToggle(false)
          refRefetch.current()
        }, 700)
      } catch (error) {
        toast({
          variant: 'danger',
          title: 'Gagal Menghapus Testimoni',
        })
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [testimonyData?.id]
  )
  const handleRequestTestimony = useCallback(
    async (id?: string) => {
      try {
        await testimonyService.adminRequestTestimony(id ? id : testimonyData?.id)
        toast({
          variant: 'success',
          title: 'Email remider untuk isi testimoni berhasil terkirim',
        })
        setTimeout(() => {
          setToggle(false)
          refRefetch.current()
        }, 700)
      } catch (error) {
        toast({
          variant: 'danger',
          title: 'Email remider untuk isi testimoni gagal terkirim',
        })
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [testimonyData?.id]
  )

  const Delete = useMemo(
    () => ({
      name: 'delete',
      icon: null,
      label: 'Hapus Testimoni',
      onClick: (item: any) => {
        handleRemoveTestimony(item?.original?.id)
      },
    }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [handleRemoveTestimony]
  )
  const RequestTestimony = useMemo(
    () => ({
      name: 'requestTestimony',
      icon: null,
      label: 'Minta Testimoni',
      onClick: (item: any) => {
        handleRequestTestimony(item?.original?.id)
      },
    }),
    [handleRequestTestimony]
  )

  const actionsMenu = (row: any) => {
    let actionsMenus: any[] = []
    if (actions.includes('delete')) {
      actionsMenus = [...actionsMenus, Delete]
    }
    if (actions.includes('requestTestimony')) {
      actionsMenus = [...actionsMenus, RequestTestimony]
    }
    return actionsMenus
  }

  return (
    <>
      <div className="flex flex-col">
        <div className="flex gap-x-5 mb-4 justify-between md:justify-start">
          <FilterHeader onChangeSearch={(val) => console.log(val)} />
        </div>
        <DataGrid
          fetchPath={fetchPath}
          actionMenuList={actionsMenu}
          columns={columns as unknown as ColumnDef<any, any>[]}
          onClickRow={(val) => {
            toggleSheet()
            setTestimonyData(val)
          }}
          refetchRef={refRefetch}
          pageFilter={pageFilter}
          setMeta={(meta) => {
            dispatch(setIsFetched(meta ? true : false))
            dispatch(setMetaTestimony(meta ? meta : null))
          }}
        />
      </div>

      <AppSheet
        open={toggle}
        side="right"
        onClose={toggleSheet}
        title="Testimoni Detail"
        className="!max-w-screen md:!max-w-[50vw] px-4 pt-[22px] pb-6"
      >
        <div className="mt-6 flex flex-col gap-4 p-6 rounded-[15px] border border-[#EBEBEB]">
          <TestimoniDetailHeader
            client={testimonyData?.client?.name ?? ''}
            stars={testimonyData?.rating ?? 0}
          />
          <NotesForPsikolog note={testimonyData?.messageForPsychologist} />
          <ContentDetailTestimony
            onRemovetestimony={() => handleRemoveTestimony()}
            date={
              testimonyData?.counseling?.startTime
                ? moment(testimonyData?.counseling?.startTime).locale('id').format('dddd, D MMMM YYYY')
                : '-'
            }
            time={
              testimonyData?.counseling?.startTime
                ? `${moment(testimonyData?.counseling?.startTime).locale('id').format('HH:mm')} - ${moment(
                    testimonyData?.counseling?.startTime
                  )
                    .add(testimonyData?.counseling?.duration === 120 ? 2 : 1, 'hours')
                    .locale('id')
                    .format('HH:mm')}`
                : '-'
            }
            via={testimonyData?.counseling?.method === 'Call' ? 'Call' : 'Video Call'}
            testimoni={testimonyData?.testimony ?? '-'}
            create={
              testimonyData?.createdAt
                ? moment(testimonyData?.createdAt).locale('id').format('D MMMM YYYY')
                : '-'
            }
          />
        </div>
      </AppSheet>
    </>
  )
}
