'use client'
import { FeatureCard } from '@/components/home/<USER>/MentalHealingFeature/FeatureCard'
import { MobileTypeCounselling } from './MobileTypeCounselling'
import { STATIC_DATA } from '@/constans/STATIC_DATA'
import { useRouter } from 'next/navigation'

export const TypeCounselling = () => {
  const router = useRouter()
  const FeatureList = [
    {
      id: 1,
      image: '/ilustration/home/<USER>',
      service: STATIC_DATA.Home.section3.content[0].topic,
      title: STATIC_DATA.Home.section3.content[0].title,
      subTitle: STATIC_DATA.Home.section3.content[0].subTitle,
      label: STATIC_DATA.Home.section3.content[0].label,
      handleClick: () => router.push('/search-psikolog'),
    },
    {
      id: 2,
      image: '/ilustration/home/<USER>',
      service: STATIC_DATA.Home.section3.content[1].topic,
      title: STATIC_DATA.Home.section3.content[1].title,
      subTitle: STATIC_DATA.Home.section3.content[1].subTitle,
      label: STATIC_DATA.Home.section3.content[1].label,
      handleClick: () => router.push('/search-psikolog'),
    },
    {
      id: 3,
      image: '/ilustration/home/<USER>',
      service: STATIC_DATA.Home.section3.content[2].topic,
      title: STATIC_DATA.Home.section3.content[2].title,
      subTitle: STATIC_DATA.Home.section3.content[2].subTitle,
      label: STATIC_DATA.Home.section3.content[2].label,
      handleClick: () => router.push('/search-psikolog'),
    },
  ]
  return (
    <>
      <div className="flex flex-col justify-center items-center gap-y-2 mb-3">
        <span className="text-subheading-md md:text-[38px] font-bold text-center text-[#222222]">
          {STATIC_DATA.Home.section3.heading}
        </span>
        <span className="text-body-md md:text-body-lg font-medium text-center text-[#535353]">
          {STATIC_DATA.Home.section3.subHeading}
        </span>
      </div>
      <div className="hidden md:grid grid-cols-3 gap-x-6 max-w-screen xl:max-w-[1120px] w-full z-1 px-4 2lg:px-4 xl:px-0">
        {FeatureList.map((item) => (
          <FeatureCard isTypeCounselling key={item.id} {...item} />
        ))}
      </div>

      {/* MOBILE VIEW */}
      <MobileTypeCounselling content={FeatureList} />
    </>
  )
}
