'use client'
import { HeaderContent } from '@/components/admin/HeaderContent'
import TestimoniList from './TestimoniList'
import { useGetTestimonyList } from './hook/useGetTestimonyList.hook'
import { FilterHeader } from '@/components/admin/FilterHeader'
import { GlobalAllStatus, RatingOptions } from '@/constans/StaticOptions'
import { useState } from 'react'

export const TestimoniComponent = () => {
  const [search, setSearch] = useState('')
  const [filter, setFilter] = useState<number | string>(GlobalAllStatus)
  const { data, isLoading, isPending } = useGetTestimonyList(search, filter)

  return (
    <>
      <HeaderContent title="Testimoni" />
      <div className="flex flex-col">
        <div className="flex gap-x-5 mb-4 justify-between md:justify-start">
          <FilterHeader
            firstOptions={RatingOptions}
            onChangeFilter={(val) => (val ? setFilter(val) : setFilter(GlobalAllStatus))}
            showSecondFilter={false}
            labelFirstFilter="Status"
          />
        </div>
        <TestimoniList isLoading={isLoading || isPending} itemList={data || []} />
      </div>
    </>
  )
}
