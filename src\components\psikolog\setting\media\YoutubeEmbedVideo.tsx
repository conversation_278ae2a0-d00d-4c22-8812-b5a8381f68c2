const YoutubeEmbed = ({
  src,
  className,
  width = '240',
  height = '135',
}: {
  src: string
  className?: string
  width?: string
  height?: string
}) => {
  return (
    <div className={`relative ${className ?? ''}`}>
      <iframe
        className="rounded-2xl"
        width={width}
        height={height}
        src={src}
        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
        allowFullScreen
        title="Embedded youtube"
      />
    </div>
  )
}

export default YoutubeEmbed
