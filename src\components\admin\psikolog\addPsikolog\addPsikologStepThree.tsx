'use client'
import InputFile from '@/components/_common/InputFile/Index'
import { useRef, useState } from 'react'
import { toast } from '@/components/ui/use-toast'
import { InputYoutubeLink } from '@/components/psikolog/setting/media/InputYoutubeLink'
import { UseFormGetValues, UseFormRegister, UseFormReturn, UseFormSetValue } from 'react-hook-form'

type LinkVideoProps = {
  id: number
  link: string | undefined
  isActive: boolean
  isEdit: boolean
}

interface AddPsikologStepThreeProps {
  pageNumberStep: number
  register: UseFormRegister<any>
  getValues: UseFormGetValues<any>
  setValue: UseFormSetValue<any>
  errors: UseFormReturn['formState']['errors']
  onSubmit: () => void
}

const initialLink = [
  { id: 1, link: undefined, isActive: false, isEdit: true },
  { id: 2, link: undefined, isActive: false, isEdit: true },
  { id: 3, link: undefined, isActive: false, isEdit: true },
]

const AddPsikologStepThree = ({
  pageNumberStep,
  register,
  getValues,
  setValue,
  errors,
}: AddPsikologStepThreeProps) => {
  const videoRef = useRef(null)
  const [linkVideo, setLinkVideo] = useState<LinkVideoProps[]>(initialLink)

  const validateLink = (): boolean => {
    if (linkVideo.length > 3) {
      toast({
        variant: 'danger',
        title: 'Maksimal 3 video',
      })
      return false
    }
    return true
  }

  const handleSubmit = (newLink: LinkVideoProps['link'], index: number) => {
    if (!newLink) {
      toast({
        variant: 'danger',
        title: 'Lengkapi link video dengan data yang valid',
      })
      return false
    }
    const isValid = validateLink()
    isValid &&
      setLinkVideo((prevLinkVideo) =>
        [...prevLinkVideo].map((linkItem, id) => {
          if (id === index) {
            return { ...linkItem, link: newLink, isEdit: false }
          }
          return linkItem
        })
      )
  }

  const handleAdd = (index: number) => {
    const isValid = validateLink()
    isValid &&
      setLinkVideo((prevLinkVideo) =>
        [...prevLinkVideo].map((linkItem, id) => {
          if (id === index) {
            return { ...linkItem, isActive: true }
          }
          return linkItem
        })
      )
  }

  const handleEdit = (index: number) => {
    setLinkVideo((prevLinkVideo) =>
      [...prevLinkVideo].map((linkItem, id) => {
        if (id === index) {
          return { ...linkItem, isEdit: true }
        }
        return linkItem
      })
    )
  }

  const handleUpdateVideo = async (file: any) => {
    setValue('video', file)
  }

  const activeLink = [...linkVideo].filter((res) => res.isActive).length

  return (
    <>
      <div className="flex flex-col gap-[6px] mb-4">
        <h2 className="text-[26px] font-bold">Media</h2>
        <span className="text-[12px] text-[#737373]">Step {pageNumberStep} dari 4</span>
      </div>

      <div className="flex flex-col gap-4">
        <div className="col-span-2">
          <InputFile
            {...register('video')}
            accept={'video/mp4'}
            onChange={(file) => handleUpdateVideo(file)}
            // previewVideo={video}
            inputRef={videoRef}
            maxFileSizeMb={10}
          />
        </div>
        <div>
          <div>
            <InputYoutubeLink
              {...register('youtubeVideos')}
              btnShow={false}
              isEdit={true}
              onSubmit={(val) => handleSubmit(val, 0)}
              value={linkVideo[0].link}
              onEdit={() => {}}
              onRemove={() => {}}
            />
          </div>
        </div>
        <div className="grid col-span-2 items-center gap-y-4">
          {linkVideo.map((link, index) => {
            if (link.isActive) {
              return (
                <div key={link.id} className="col-span-2">
                  <InputYoutubeLink
                    isEdit={link.isEdit}
                    value={link.link}
                    onEdit={() => handleEdit(index)}
                    onSubmit={(val) => handleSubmit(val, index)}
                    onRemove={() => {}}
                  />
                </div>
              )
            }
          })}
        </div>
      </div>

      {/* btn add youtube link */}
      <button disabled={activeLink >= 2} onClick={() => handleAdd(activeLink)} className="flex items-start">
        <span
          className={`${activeLink >= 2 ? 'text-red-500 cursor-not-allowed' : 'text-[#039EE9] cursor-pointer'}`}
        >
          + Tambah Youtube Link
        </span>
      </button>
    </>
  )
}

export default AddPsikologStepThree
