import ButtonPrimary from '@/components/_common/ButtonPrimary'
import { Card } from '@/components/_common/ui'
import Image from 'next/image'

type FeatureProps = {
  image: string
  service: string[]
  title: string
  subTitle: string
  label: string
  isTypeCounselling?: boolean
  handleClick: () => void
}

export const LogoMeaning = ({ note, content }: { note: string; content: any[] }) => {
  return (
    <div className="flex flex-col gap-y-8 items-center z-1 px-4 md:px-0">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-16">
        {content.length &&
          content.map((item) => {
            return (
              <div
                key={item.id}
                className="flex flex-col gap-y-4 items-center bg-white shadow-card rounded-lg p-6"
              >
                <Image src={item.image} alt={'logo-' + item.id} height={100} width={100} />
                <span className="text-heading-sm font-bold text-[#019EE9] text-center">{item.title}</span>
                <span
                  className="text-body-lg font-medium text-gray-300 text-center"
                  dangerouslySetInnerHTML={{ __html: item.content }}
                ></span>
              </div>
            )
          })}
      </div>
      <span
        className="text-body-lg md:text-subheading-md font-medium text-gray-300 text-center"
        dangerouslySetInnerHTML={{ __html: note }}
      ></span>
    </div>
  )
}
