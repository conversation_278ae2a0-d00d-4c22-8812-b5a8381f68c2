import { FormInputSetting } from '@/components/psikolog/setting/FormInputSetting'

import { useState } from 'react'
import AppInput from '@/components/_common/input/Input'
import { useUpdatePassword } from '../hook/useClientUpdatePassword'
import { useProfileInformation } from '../hook/useClientInformationProfile'

export default function LoginInfo() {
  const { profileData } = useProfileInformation()
  const { updatePassword, loading, error, success } = useUpdatePassword()
  const [currentPassword, setCurrentPassword] = useState('')
  const [newPassword, setNewPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [validationError, setValidationError] = useState('')

  const handlePasswordUpdate = async () => {
    // Reset validation error
    setValidationError('')

    // Validate passwords
    if (!currentPassword || !newPassword || !confirmPassword) {
      setValidationError('Semua kolom harus diisi')
      return
    }

    if (newPassword !== confirmPassword) {
      setValidationError('Password baru tidak cocok')
      return
    }

    if (newPassword.length < 6) {
      setValidationError('Password baru harus memiliki minimal 6 karakter')
      return
    }

    // Submit password update
    await updatePassword(currentPassword, newPassword)

    // Clear fields on success
    if (success) {
      setCurrentPassword('')
      setNewPassword('')
      setConfirmPassword('')
    }
  }

  return (
    <>
      <FormInputSetting
        renderViewMode={<div className="mt-2"> {profileData?.phoneNumber}</div>}
        value={profileData?.phoneNumber}
        type={'number'}
        option={''}
        label="No Handphone"
      >
        {profileData?.phoneNumber}
      </FormInputSetting>

      <FormInputSetting
        renderViewMode={<div className="mt-2">{profileData?.userIdentity?.email}</div>}
        value={profileData?.userIdentity?.email}
        type={'email'}
        option={''}
        label="Email"
      >
        {profileData?.userIdentity?.email}
      </FormInputSetting>

      <FormInputSetting
        renderViewMode={<div className="mt-2">••••••</div>}
        value={''}
        type={'password'}
        option={''}
        label="Password"
        onSubmit={handlePasswordUpdate}
      >
        <div className="space-y-4">
          <AppInput
            type="password"
            label="Current Password"
            value={currentPassword}
            onChange={(e) => setCurrentPassword(e.target.value)}
            placeholder="Enter current password"
          />
          <AppInput
            type="password"
            label="New Password"
            value={newPassword}
            onChange={(e) => setNewPassword(e.target.value)}
            placeholder="Enter new password"
          />
          <AppInput
            type="password"
            label="Confirm New Password"
            value={confirmPassword}
            onChange={(e) => setConfirmPassword(e.target.value)}
            placeholder="Confirm new password"
          />

          {validationError && <div className="text-red-500 text-sm mt-2">{validationError}</div>}
          {error && <div className="text-red-500 text-sm mt-2">{error.message}</div>}
          {success && <div className="text-green-500 text-sm mt-2">Password updated successfully!</div>}
          {loading && <div className="text-gray-500 text-sm mt-2">Updating password...</div>}
        </div>
      </FormInputSetting>
    </>
  )
}
