import React from 'react'
import { IIcons } from '@/components/_common/icon'

export type OptionTypeProps<OptKey extends string | number | symbol, OptValue> = {
  [key in OptKey]: OptValue
}

export type OptionTypeSelect = {
  id?: string
  label: string
  value: string
}

type AppInputInterface =
  | {
      type: 'text' | 'number' | 'email'
      placeholder?: string
      options?: never
      rows?: never
      bg?: string
      width?: number
      className?: string
      inputClass?: string
      onChange: (e: React.ChangeEvent<HTMLInputElement>) => void
      onKeyDown?: (e: React.KeyboardEvent<HTMLInputElement>) => void
      value?: string
      name?: string
      errorMsg?: string
      prefixButton?: never
      maxChar?: number
      prefixIcon?: React.ReactNode | IIcons
      suffixIcon?: React.ReactNode | IIcons
      label?: string
      note?: React.ReactNode
      onForgotPassword?: never
      getLabel?: never
      getKey?: never
    }
  | {
      type: 'password'
      placeholder?: string
      options?: never
      rows?: never
      bg?: string
      width?: number
      className?: string
      inputClass?: string
      onChange: (e: React.ChangeEvent<HTMLInputElement>) => void
      onKeyDown?: (e: React.KeyboardEvent<HTMLInputElement>) => void
      value?: string
      name?: string
      errorMsg?: string
      prefixButton?: IIcons
      maxChar?: never
      prefixIcon?: React.ReactNode | IIcons
      suffixIcon?: React.ReactNode | IIcons
      label?: string
      note?: React.ReactNode
      onForgotPassword?: () => void
      getLabel?: never
      getKey?: never
    }
  | {
      type: 'textarea'
      placeholder?: string
      options?: never
      rows: number
      bg?: string
      width?: number
      className?: string
      inputClass?: string
      onChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void
      onKeyDown?: (e: React.KeyboardEvent<HTMLTextAreaElement>) => void
      value?: string | null
      name?: string
      errorMsg?: string
      prefixButton?: never
      maxChar?: number
      prefixIcon?: React.ReactNode | IIcons
      suffixIcon?: React.ReactNode | IIcons
      label?: string
      note?: React.ReactNode
      onForgotPassword?: never
      getLabel?: never
      getKey?: never
    }
  | {
      type: 'select'
      placeholder?: string
      options: Array<string> | OptionTypeProps<string, any> | OptionTypeSelect[]
      rows?: never
      bg?: string
      width?: number
      className?: string
      inputClass?: string
      onChange: (e: React.ChangeEvent<HTMLSelectElement>) => void
      onKeyDown?: never
      value: string | number
      name?: string
      errorMsg?: string
      prefixButton?: never
      maxChar?: never
      prefixIcon?: React.ReactNode | IIcons
      suffixIcon?: React.ReactNode | IIcons
      label?: string
      note?: React.ReactNode
      onForgotPassword?: never
      getLabel?: (e: any) => void
      getKey?: (e: any) => void
    }
  | {
      type: 'date'
      placeholder?: string
      options?: never
      rows?: never
      bg?: string
      width?: number
      className?: string
      inputClass?: string
      onChange: (e: React.ChangeEvent<HTMLSelectElement>) => void
      onKeyDown?: never
      value: string
      name?: string
      errorMsg?: string
      prefixButton?: never
      maxChar?: never
      prefixIcon?: React.ReactNode | IIcons
      suffixIcon?: React.ReactNode | IIcons
      label?: string
      note?: React.ReactNode
      onForgotPassword?: never
      getLabel?: never
      getKey?: never
    }

export default AppInputInterface
