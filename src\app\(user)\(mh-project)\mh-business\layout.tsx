import { FooterSection } from '@/components/home/<USER>/Footer/FooterSection'
import { STATIC_DATA } from '@/constans/STATIC_DATA'

export default function MHEduLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <>
      {children}
      <div className="hidden md:grid max-w-screen relative w-full pt-10">
        <FooterSection {...STATIC_DATA.MHBusiness.footer} img="/ilustration/footer-business.svg" />
      </div>
      <div className="md:hidden max-w-screen relative w-full pt-10">
        <FooterSection
          showButtonCounselling
          isMobile
          {...STATIC_DATA.MHBusiness.footer}
          img="/ilustration/footer-business.svg"
        />
      </div>
    </>
  )
}
