'use client'

import { AppBigCaption, AppBigText, AppMediumText, Card } from '@/components/_common/ui'
import { HeaderContent } from '../HeaderContent'
import { LabelValue } from '@/components/_common/CardInfo/LabelValue'
import { CardCounsellingStatus } from './CardCounsellingStatus'
import { translateCounselingStatus } from '@/utils/translationStatus'
import {
  formatStringToDateOutput,
  formatStringToFulldateOutput,
  formatStringToStartEndTimeOutput,
} from '@/utils/displayDate'
import { AvatarWithInfo } from '@/components/_common/CardInfo/AvatarWithInfo'
import {
  CancelledStatus,
  CompletedStatus,
  defaultTimezone,
  PaymentStatus,
  UpcomingStatusForAdmin,
  WaitConfirmationsStatusForAdmin,
  WaitPaymentStatusForAdmin,
} from '@/constans/StaticOptions'
import { useGetCounselingDetails } from '@/hooks/useGetCounselingDetails.hook'
import { LoadingAccordionClientReport } from '@/components/loading/LoadingAccordionClientReport'
import ButtonPrimary from '@/components/_common/ButtonPrimary'
import { ClientReportItemContent } from '@/components/psikolog/report/ClientReportContent'
import { useRef } from 'react'
import { IModalActionState } from '@/interfaces/components/psikolog/schedule'
import { AppModal } from '@/components/_common/Modal/AppModal'
import AdminScheduleForm from './reschedule/AdminScheduleForm'
import AdminRejectForm from './RejectSchedule/AdminRejectForm'
import AdminStartCounselingDialog from './StartCounseling/AdminStartCounselingDialog'
import ApproveCounselingDialog from './ApproveCounseling/ApproveCounselingDialog'
import CompleteConfirmationDialog from './CompleteConfirmation/CompleteConfirmation'
import CancelSessionDialog from './CancelSession/CancelSession'
import { useCounselingAction } from './useCounselingAction'
import { counsellingService } from '@/services/counselling.service'
import { useToast } from '@/components/ui/use-toast'

export const DetailCounselling = ({ idCounseling }: { idCounseling: string }) => {
  const { toast } = useToast()
  const { data: selectedCounseling, isLoading, isPending, refetch } = useGetCounselingDetails(idCounseling)
  const isLoadingApp = isLoading || isPending
  const translation = translateCounselingStatus(selectedCounseling?.status ?? '')
  const variant = translation.variant
  const status = translation.value
  const counselingStatus = selectedCounseling?.status

  const showStartCounselingButton =
    UpcomingStatusForAdmin.includes(counselingStatus) || counselingStatus === PaymentStatus.INPROGRESS
  const showAcceptCounselingButton = WaitConfirmationsStatusForAdmin.includes(counselingStatus)
  const showChangeCounselingButton =
    WaitConfirmationsStatusForAdmin.includes(counselingStatus) ||
    UpcomingStatusForAdmin.includes(counselingStatus)
  const showDeclineCounselingButton = WaitConfirmationsStatusForAdmin.includes(counselingStatus)
  const showContactClientButton =
    WaitPaymentStatusForAdmin.includes(counselingStatus) ||
    CompletedStatus.includes(counselingStatus) ||
    CancelledStatus.includes(counselingStatus) ||
    counselingStatus === PaymentStatus.INPROGRESS
  const showContactPsychologistButton =
    WaitPaymentStatusForAdmin.includes(counselingStatus) ||
    CompletedStatus.includes(counselingStatus) ||
    CancelledStatus.includes(counselingStatus) ||
    counselingStatus === PaymentStatus.INPROGRESS
  const showCreateNewCounselingButton = CompletedStatus.includes(counselingStatus)
  const showCancelCounselingButton = counselingStatus === PaymentStatus.INPROGRESS

  const modalRef = useRef<any>(null)

  const {
    handleCLickStartCounselling,
    handleCLickReschedule,
    handleCLickReject,
    handleClickApproveCounseling,
    handleContactClient,
    handleContactPsychologist,
    handleCancelSession,
    handleClose,
    isRefetchOncloseModalX,
    modalOpen,
    titleModal,
    modalState,
  } = useCounselingAction(refetch)

  if (isLoadingApp) {
    return <LoadingAccordionClientReport />
  }

  const handleClickRemindClientReport = async () => {
    try {
      await counsellingService.adminRemindClientReport(selectedCounseling?.id)
      toast({
        description: 'Berhasil mengingatkan psikolog untuk mengisi klien report',
        variant: 'success',
      })
    } catch (error) {
      toast({
        description: 'Terjadi masalah saat mengingatkan psikolog untuk mengisi klien report',
        variant: 'danger',
      })
    }
  }

  return (
    <>
      <div className="flex flex-col gap-y-6">
        {isLoadingApp ? (
          <LoadingAccordionClientReport />
        ) : (
          <>
            <HeaderContent className="mb-0 xs:mb-0 sm:mb-0 md:mb-0" title="Detail Konseling" />
            <CardCounsellingStatus
              variant={variant}
              status={status}
              psikologName={selectedCounseling?.psychologist?.fullName ?? ''}
              clientName={selectedCounseling?.client?.fullName ?? ''}
              date={formatStringToFulldateOutput(selectedCounseling?.startTime ?? '')}
              time={formatStringToStartEndTimeOutput({
                date: selectedCounseling?.startTime ?? '',
                duration: selectedCounseling?.duration ?? 60,
                timezone: defaultTimezone,
                timeLabel: 'WIB',
                isUTC: true,
              })}
              method={selectedCounseling?.method === 'Call' ? 'Call' : 'Video Call'}
            >
              <>
                <AppMediumText className="text-gray-300">Psikolog</AppMediumText>
                <AvatarWithInfo
                  className="w-[42px] h-[42px]"
                  image={selectedCounseling?.psychologist?.profilePhoto ?? ''}
                  heading={
                    <span className="text-body-lg font-bold">
                      {selectedCounseling?.psychologist?.fullName}
                    </span>
                  }
                  subHeading={
                    <AppBigCaption className="text-gray-200">
                      {selectedCounseling?.psychologist?.field?.length
                        ? selectedCounseling?.psychologist?.field?.map((val: any) => val.name).join(', ')
                        : 'Tidak Tersedia'}
                    </AppBigCaption>
                  }
                />
                <div className="grid grid-cols-3 grid-rows-1 gap-y-2">
                  <span className="col-span-3">
                    <LabelValue
                      displayRows
                      labelClass="text-body-sm font-medium text-gray-300"
                      valueClass="font-bold"
                      label="Jenis Kegiatan"
                      value={`Konseling - Healing ${selectedCounseling?.duration === 60 ? '1' : '2'}`}
                    />
                  </span>
                  <span className="col-span-3">
                    <LabelValue
                      displayRows
                      labelClass="text-body-sm font-medium text-gray-300"
                      valueClass="font-bold"
                      label="Kategori Permasalahan"
                      value={
                        selectedCounseling?.problemCategory?.length
                          ? selectedCounseling?.problemCategory
                              ?.map((val: any) => val.problemCategory)
                              .join(', ')
                          : 'Tidak Tersedia'
                      }
                    />
                  </span>
                  <span className="col-span-3">
                    <LabelValue
                      displayRows
                      labelClass="text-body-sm font-medium text-gray-300"
                      valueClass="font-bold"
                      label="Harapan Setelah Konseling"
                      value={selectedCounseling?.expectation ?? ''}
                    />
                  </span>
                  <span className="col-span-3">
                    <LabelValue
                      displayRows
                      labelClass="text-body-sm font-medium text-gray-300"
                      valueClass=""
                      label="Detail Keluhan"
                      value={selectedCounseling?.description ?? ''}
                    />
                  </span>
                  <span className="col-span-3">
                    <LabelValue
                      displayRows
                      labelClass="text-body-sm font-medium text-gray-300"
                      valueClass="font-bold"
                      label={`Jadwal dibuat: ${formatStringToDateOutput(selectedCounseling?.createdAt ?? '')}`}
                      value=""
                    />
                  </span>
                  <span className="col-span-3">
                    <LabelValue
                      displayRows
                      labelClass="text-body-sm font-medium text-gray-300"
                      valueClass="font-bold"
                      label={`ID Konseling: ${selectedCounseling?.id ?? ''}`}
                      value=""
                    />
                  </span>
                </div>
                <div className="flex justify-start gap-3 flex-wrap">
                  {showStartCounselingButton && (
                    <ButtonPrimary
                      className="min-w-[143px]"
                      size="sm"
                      variant={'contained'}
                      onClick={() => handleCLickStartCounselling(selectedCounseling)}
                    >
                      Mulai Konseling
                    </ButtonPrimary>
                  )}
                  {showAcceptCounselingButton && (
                    <ButtonPrimary
                      className="min-w-[143px]"
                      size="sm"
                      variant={'contained'}
                      onClick={() => handleClickApproveCounseling(selectedCounseling)}
                    >
                      Terima
                    </ButtonPrimary>
                  )}
                  {showCreateNewCounselingButton && (
                    <ButtonPrimary
                      className="min-w-[143px]"
                      size="sm"
                      variant={'contained'}
                      onClick={() => {}}
                    >
                      Buatkan Jadwal Baru
                    </ButtonPrimary>
                  )}
                  {showContactPsychologistButton && (
                    <ButtonPrimary
                      className="min-w-[143px]"
                      size="sm"
                      color="gray"
                      variant={'outlined'}
                      onClick={() => handleContactPsychologist(selectedCounseling)}
                    >
                      Hubungi Psikolog
                    </ButtonPrimary>
                  )}
                  {showContactClientButton && (
                    <ButtonPrimary
                      className="min-w-[143px]"
                      size="sm"
                      color="gray"
                      variant={'outlined'}
                      onClick={() => handleContactClient(selectedCounseling)}
                    >
                      Hubungi Klien
                    </ButtonPrimary>
                  )}
                  {showChangeCounselingButton && (
                    <ButtonPrimary
                      className="min-w-[143px]"
                      size="sm"
                      color="gray"
                      variant={'outlined'}
                      onClick={() => handleCLickReschedule(selectedCounseling)}
                    >
                      Ubah Jadwal
                    </ButtonPrimary>
                  )}
                  {showDeclineCounselingButton && (
                    <ButtonPrimary
                      className="min-w-[143px]"
                      size="sm"
                      color="danger"
                      variant={'outlined'}
                      onClick={() => handleCLickReject(selectedCounseling)}
                    >
                      Tolak
                    </ButtonPrimary>
                  )}
                  {showCancelCounselingButton && (
                    <ButtonPrimary
                      className="min-w-[143px]"
                      size="sm"
                      color="gray"
                      variant={'outlined'}
                      onClick={() => handleCancelSession(selectedCounseling)}
                    >
                      Batalkan Sesi
                    </ButtonPrimary>
                  )}
                </div>
              </>
            </CardCounsellingStatus>
            {selectedCounseling?.status === PaymentStatus.COMPLETED ? (
              <Card className="col-span-3 xs:col-span-3 sm:col-span-3 xl:col-span-2 items-center gap-x-6 p-2 xs:p-3 sm:p-4 xl:p-6 relative bg-white -top-4 z-index-10 grid">
                <AppBigText bold className="border-b border-line-200 pb-6">
                  Klien Report: {selectedCounseling?.client?.fullName ?? ''}
                </AppBigText>
                <div className="grid grid-cols-1 grid-rows-1 gap-y-4 pt-4">
                  <AppMediumText className="text-gray-300">Psikolog</AppMediumText>
                  <AvatarWithInfo
                    className="w-[42px] h-[42px]"
                    image={selectedCounseling?.psychologist?.profilePhoto ?? ''}
                    heading={
                      <span className="text-body-md font-bold">
                        {selectedCounseling?.psychologist?.fullName}
                      </span>
                    }
                    subHeading={
                      <span className="text-gray-200 text-body-md font-normal ">
                        {selectedCounseling?.problemCategory?.length
                          ? selectedCounseling?.problemCategory
                              ?.map((val: any) => val.problemCategory)
                              .join(', ')
                          : 'Tidak Tersedia'}
                      </span>
                    }
                  />
                  {!selectedCounseling?.clientReport ? (
                    <ButtonPrimary
                      className="w-fit"
                      size="sm"
                      variant={'contained'}
                      onClick={() => handleClickRemindClientReport()}
                    >
                      Ingatkan Isi Report
                    </ButtonPrimary>
                  ) : (
                    <ClientReportItemContent {...(selectedCounseling?.clientReport ?? {})} />
                  )}
                </div>
              </Card>
            ) : null}
          </>
        )}
      </div>
      <AppModal
        ref={modalRef}
        className="md:w-full max-w-[668px]"
        open={modalOpen}
        onClose={() => {
          handleClose(isRefetchOncloseModalX)
        }}
        title={titleModal}
        showOverlay={true}
      >
        {modalState.modal === IModalActionState.RESCHEDULE ? (
          <AdminScheduleForm
            item={modalState.item}
            onClose={() => handleClose(true)}
            callbackToggleCalendar={(arg) => {
              modalRef.current.className = arg
                ? modalRef.current.className.replace('bg-black/80', 'bg-black/10 ')
                : modalRef.current.className + 'bg-black/80 '
            }}
          />
        ) : modalState.modal === IModalActionState.REJECT ? (
          <AdminRejectForm item={modalState.item} onClose={() => handleClose(true)} />
        ) : modalState.modal === IModalActionState.START_COUNSELING ? (
          <AdminStartCounselingDialog item={modalState.item} onClose={() => handleClose(true)} />
        ) : modalState.modal === IModalActionState.APPROVE ? (
          <ApproveCounselingDialog item={modalState.item} onClose={() => handleClose(true)} />
        ) : modalState.modal === IModalActionState.CONFIRMATION_COMPLETELY ? (
          <CompleteConfirmationDialog item={modalState.item} onClose={() => handleClose(true)} />
        ) : modalState.modal === IModalActionState.CANCEL_SESSION ? (
          <CancelSessionDialog item={modalState.item} onClose={() => handleClose(true)} />
        ) : null}
      </AppModal>
    </>
  )
}
