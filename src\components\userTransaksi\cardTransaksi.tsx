'use client'
import ButtonPrimary from '../_common/ButtonPrimary'
import { IIcons, SVGIcons } from '../_common/icon'
import InfoCardTransaksi from './infoCardTransaksi'
import ProfileCardTransaksi from './profileCardTransaksi'
import { useRouter } from 'next/navigation'

interface CardTransaksiProps {
  id: string
  title: string
  buttonType: string
  buttonTitle: string
  buttonTitle2: string
  textButtonColor: string
  numberButton: string
  date: string
  time: string
  method: string
  psychologistName: string
  psychologistPhoto?: string
  paymentUrl?: string
}

export default function CardTransaksi({
  id,
  title,
  buttonType,
  buttonTitle,
  buttonTitle2,
  textButtonColor,
  numberButton,
  date,
  time,
  method,
  psychologistName,
  psychologistPhoto,
  paymentUrl,
}: CardTransaksiProps) {
  const router = useRouter()

  const handleButtonClick = (
    action: 'start' | 'pay' | 'accept' | 'change' | 'reschedule',
    e: React.MouseEvent
  ) => {
    e.stopPropagation() // Prevent card click event

    if (action === 'pay' && paymentUrl) {
      window.open(paymentUrl, '_blank')
    } else if (action === 'start') {
      router.push(`/konseling/room/${id}`)
    } else if (action === 'reschedule') {
      router.push(`/booking/${id}?reschedule=true`)
    } else if (action === 'accept') {
      // Call API to accept the counseling
      console.log('Accept counseling', id)
    } else if (action === 'change') {
      router.push(`/booking/${id}?reschedule=client`)
    }
  }

  const Button = () => {
    if (numberButton === '0') {
      return (
        <>
          <ButtonPrimary
            className="min-w-[143px] rounded-[15px] hidden lg:block lg:invisible"
            textSize="text-[12px]"
            variant={'contained'}
            textColor={''}
            size="xs"
          >
            {buttonTitle}
          </ButtonPrimary>
          <ButtonPrimary
            className="min-w-[143px] rounded-[15px] hidden lg:block lg:invisible"
            textSize="text-[12px]"
            variant={'contained'}
            textColor={''}
            size="xs"
          >
            {buttonTitle2}
          </ButtonPrimary>
        </>
      )
    } else if (numberButton === '1') {
      const actionType =
        title.includes('Selesai') || title.includes('Dibatalkan') || title.includes('Gagal')
          ? 'reschedule'
          : 'start'

      return (
        <>
          <ButtonPrimary
            className="min-w-[143px] rounded-[15px]"
            textSize="text-[11px]"
            variant={buttonType === 'contained' ? 'contained' : 'outlined'}
            textColor={''}
            size="xs"
            onClick={(e) => handleButtonClick(actionType, e)}
          >
            {buttonTitle}
          </ButtonPrimary>
        </>
      )
    } else {
      // For numberButton === '2'
      let primaryAction: 'start' | 'pay' | 'accept' | 'change' | 'reschedule' = 'pay'
      if (title === 'Menunggu Konfirmasi Kamu') {
        primaryAction = 'accept'
      }

      return (
        <>
          <ButtonPrimary
            className="min-w-[143px] rounded-[15px]"
            textSize="text-[11px]"
            variant={'contained'}
            textColor={''}
            size="xs"
            onClick={(e) => handleButtonClick(primaryAction, e)}
          >
            {buttonTitle}
          </ButtonPrimary>
          <ButtonPrimary
            className="min-w-[143px] rounded-[15px]"
            textSize="text-[11px]"
            color="gray"
            textColor={textButtonColor}
            variant={'outlined'}
            size="xs"
            onClick={(e) => handleButtonClick('change', e)}
          >
            {buttonTitle2}
          </ButtonPrimary>
        </>
      )
    }
  }

  const tipColor = () => {
    if (title === 'Konseling dengan Psikolog' || title === 'Konseling Selesai') {
      return 'bg-[#039EE9]'
    } else if (
      title === 'Menunggu Konfirmasi Psikolog' ||
      title === 'Menunggu Konfirmasi Kamu' ||
      title === 'Menunggu Pembayaran Konseling'
    ) {
      return 'bg-[#FED748]'
    } else {
      return 'bg-[#E42B3B]'
    }
  }

  const tipColorBorder = () => {
    if (title === 'Konseling dengan Psikolog' || title === 'Konseling Selesai') {
      return 'hover:border-[#039EE9]'
    } else if (
      title === 'Menunggu Konfirmasi Psikolog' ||
      title === 'Menunggu Konfirmasi Kamu' ||
      title === 'Menunggu Pembayaran Konseling'
    ) {
      return 'hover:border-[#FED748]'
    } else {
      return 'hover:border-[#E42B3B]'
    }
  }

  const titleRename = () => {
    if (title === 'Konseling dengan Psikolog') {
      return 'Konseling-dengan-psikolog'
    } else if (title === 'Menunggu Konfirmasi Psikolog') {
      return 'Menunggu-konfirmasi-psikolog'
    } else if (title === 'Menunggu Konfirmasi Kamu') {
      return 'Menunggu-konfirmasi-kamu'
    } else if (title === 'Menunggu Pembayaran Konseling') {
      return 'Menunggu-pembayaran-konseling'
    } else if (title === 'Konseling Selesai') {
      return 'Konseling-selesai'
    } else if (title === 'Konseling Dibatalkan') {
      return 'Konseling-dibatalkan'
    } else if (title === 'Pembayaran Konseling Gagal') {
      return 'Pembayaran-konseling-gagal'
    }
  }

  const handleCardClick = () => {
    router.push(`/transaksi/${titleRename()}/${id}`)
  }

  return (
    <>
      <div
        onClick={handleCardClick}
        className={`bg-white border border-[#EBEBEB] rounded-[15px] w-full px-4 py-6 transition-all duration-500 ease-in-out ${tipColorBorder()} cursor-pointer relative`}
      >
        {/* tip */}
        <div
          className={`${tipColor()} h-5 w-1 rounded-tr-md rounded-br-md absolute left-0 top-[26px] lg:top-[30px]`}
        ></div>
        <div className="flex flex-col lg:flex-row gap-6 lg:gap-0 justify-normal lg:items-center lg:justify-between">
          <div className="flex flex-col gap-2">
            <div className="flex items-center justify-between">
              <p className="text-[#222222] text-[16px] font-bold">{title}</p>
              <SVGIcons name={IIcons.ArrowRight} className="w-6 h-6 block md:hidden" />
            </div>
            <div className="flex flex-col md:flex-row gap-4">
              {/* jadwal */}
              <InfoCardTransaksi icon="Calendar" title={date} />
              {/* waktu */}
              <InfoCardTransaksi icon="Time" title={time} />
              {/* metode */}
              <InfoCardTransaksi icon={method === 'Video' ? 'Video' : 'Call'} title={method} />
            </div>
          </div>
          {/* mobile and tablet */}
          <div className="flex flex-col gap-3 md:gap-0 md:flex-row lg:hidden md:items-center justify-between border-t md:border-none border-[#EBEBEB] pt-3 md:pt-0">
            <ProfileCardTransaksi
              nama={psychologistName}
              photo={psychologistPhoto || ''}
              job="Psi., Psikolog"
              flexDirection="flex-col"
            />
            <div className="flex items-center justify-between md:justify-normal gap-3">{Button()}</div>
          </div>
          {/* desktop */}
          <div className="hidden lg:inline-block">
            <ProfileCardTransaksi
              nama={psychologistName}
              photo={psychologistPhoto || ''}
              job="Psi., Psikolog"
              flexDirection="flex-row"
            />
          </div>
          <div className="lg:flex items-center gap-3 hidden">{Button()}</div>
        </div>
      </div>
    </>
  )
}
