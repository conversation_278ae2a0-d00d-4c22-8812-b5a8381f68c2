import { NoDataFound } from '@/components/_common/NoData/NoDataFound'
import { TestimoniItem } from './TestimoniItem'

export type TestimonyItemProps = {
  id: string
  psychologistId: string
  clientId: string
  counselingId: string
  clientName: string
  rating: 2
  testimony: string
  messageForPsychologist: string
  isDisplayed: true
  createdAt: string
  client: {
    id: string
    userIdentityId: string
    fullName: string
    nickname: string
  }
  counseling: {
    id: string
    psychologistId: string
    clientId: string
    startTime: string
    endTime: string
    method: string
    location: string
    duration: number
  }
}

export type TestimonyListProps = {
  itemList: TestimonyItemProps[]
  isLoading: boolean
}

const TestimoniList = ({ itemList, isLoading }: TestimonyListProps) => {
  return (
    <div className="grid grid-cols-6 grid-rows-1">
      <div className="grid col-span-6 lg:col-span-5 xl:col-span-4">
        {itemList.length > 0 || isLoading ? (
          <TestimoniItem itemList={itemList} isLoading={isLoading} />
        ) : (
          <NoDataFound />
        )}
      </div>
    </div>
  )
}

export default TestimoniList
