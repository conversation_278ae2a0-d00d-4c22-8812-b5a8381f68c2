import { httpRequest } from '@/utils/network'

const apiRegion = '/region/'

export class RegionService {
  async getProvince() {
    return await httpRequest({
      method: 'get',
      headers: { 'Access-Control-Allow-Credentials': 'true' },
      url: `${apiRegion}provinces.json`,
    })
  }
  async getRegencies(province: string) {
    return await httpRequest({
      method: 'get',
      url: `${apiRegion}regencies/${province}.json`,
    })
  }
  async getDistricts(regencies: string) {
    return await httpRequest({
      method: 'get',
      url: `${apiRegion}districts/${regencies}.json`,
    })
  }
  async getSubDistricts(districts: string) {
    return await httpRequest({
      method: 'get',
      url: `${apiRegion}villages/${districts}.json`,
    })
  }
}

export const regionService = new RegionService()
