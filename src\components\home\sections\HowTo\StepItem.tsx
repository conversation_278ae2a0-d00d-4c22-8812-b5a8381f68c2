export const StepItem = ({ id, label, content }: { id: number; label: string; content: string }) => {
  return (
    <div className="flex bg-[#F8FAFC] p-6 w-full gap-2 rounded-lg">
      <div className="rounded-full bg-main-50 p-2 w-[30px] h-[30px] flex justify-center items-center text-main-100 font-bold">
        {id}
      </div>
      <div className="flex flex-col gap-y-1">
        <span className="text-body-lg font-bold text-gray-400">{label}</span>
        <span className="text-body-md font-medium text-gray-300">{content}</span>
      </div>
    </div>
  )
}
