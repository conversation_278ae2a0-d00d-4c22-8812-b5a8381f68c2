export const LoadingCard = () => {
  return (
    <>
      <div className="bg-white border border-line-200 rounded-md w-full mx-auto">
        <div className="animate-pulse flex flex-col space-y-2">
          <div className="rounded-t-md bg-slate-200 h-32 w-full"></div>
          <div className="flex-1 space-y-6 py-1 px-4">
            <div className="space-y-3">
              <div className="grid grid-cols-3 gap-4">
                <div className="h-5 bg-slate-200 rounded col-span-2"></div>
                <div className="h-5 bg-slate-200 rounded col-span-1"></div>
              </div>
              <div className="h-3 bg-slate-200 rounded"></div>
              <div className="flex gap-4">
                <div className="h-3 bg-slate-200 rounded w-3"></div>
                <div className="h-3 bg-slate-200 rounded w-1/3"></div>
              </div>
            </div>
            <div className="pb-3">
              <div className="h-8 bg-slate-200 rounded-full"></div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

export const LoadingCardWithAvatar = () => {
  return (
    <>
      <div className="bg-white border border-line-200 rounded-md w-full mx-auto p-4">
        <div className="animate-pulse flex space-x-4">
          <div className="rounded-full bg-slate-200 h-20 w-20"></div>
          <div className="flex-1 space-y-6 py-1">
            <div className="space-y-3">
              <div className="grid grid-cols-3 gap-2">
                <div className="h-3 bg-slate-200 rounded col-span-2"></div>
                <div className="h-3 bg-slate-200 rounded col-span-1"></div>
                <div className="h-3 bg-slate-200 rounded"></div>
              </div>
              <div className="h-2 bg-slate-200 rounded w-1/2"></div>
            </div>
            <div className="flex gap-4">
              <div className="h-3 bg-slate-200 rounded w-3"></div>
              <div className="h-3 bg-slate-200 rounded w-1/3"></div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
