import { psychologistService } from '@/services/psychologist.service'
import { useState, useEffect } from 'react'

export const useGetPsychologistAvailabilityDate = (psychologistId: string) => {
  const [availabilityDate, setAvailabilityDate] = useState(null)
  const [loadingDate, setLoadingDate] = useState(true)
  const [errorDate, setErrorDate] = useState(null)

  const fetchPsychologistAvailabilityDate = async () => {
    try {
      setLoadingDate(true)
      const response = await psychologistService.getPsychologistAvailabilityDateById(psychologistId)
      setAvailabilityDate(response)
      setErrorDate(null)
    } catch (err) {
      setErrorDate(err as null)
      setAvailabilityDate(null)
    } finally {
      setLoadingDate(false)
    }
  }

  useEffect(() => {
    if (psychologistId) {
      fetchPsychologistAvailabilityDate()
    }
  }, [psychologistId])

  return {
    availabilityDate,
    loadingDate,
    errorDate,
    refetch: fetchPsychologistAvailabilityDate,
  }
}
