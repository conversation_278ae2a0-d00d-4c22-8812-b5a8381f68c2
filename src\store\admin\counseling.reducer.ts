import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import { RescheduleDetailProps, ScheduleListProps } from '../psikolog/schedule.reducer'
import { IModalActionState } from '@/interfaces/components/psikolog/schedule'
import moment from 'moment'
import { DateRange } from 'react-day-picker'
import { MOMENT_INPUT_DATE_FORMAT } from '@/constans/date'

export type PsychologistItem = {
  id: string
  userIdentityId: string
  fullName: string
  nickname: string
  bio: string | null
  phoneNumber: string
  birthDate: string
  birthOrder: string | null
  religion: string
  maritalStatus: string
  domicile: string
  ethnicity: string | null
  joinDate: string
  endDate: string | null
  gender: string
  firstCareerDate: string
  profilePhoto: string
  video: string | null
  counselingCount: number
  penalty: number
  occupation: string | null
  workplace: string | null
  sipp: string
  str: string | null
  service: string | null
  offlineLocation: string | null
  createdAt: string
  createdBy: string | null
  modifiedAt: string
  modifiedBy: string | null
}

export type CounselingItemProps = ScheduleListProps & {
  psychologist: PsychologistItem
}
export type CounselingProps = {
  selectedCounseling: CounselingItemProps | null
  modalState: {
    modal: IModalActionState | null
    item: ScheduleListProps | null
  }
  weekView: {
    start: string | null
    end: string | null
  }
  rescheduleDetail: RescheduleDetailProps
  rescheduleStep: number
  rejectStep: number
  rejectNote?: string | null
  filterDate: { from: string | undefined; to: string | undefined }
}

const adminCounselingSlice = createSlice({
  name: 'counseling-admin',
  initialState: {
    selectedCounseling: null,
    isLoading: false,
    showModalAction: null,
    filterDate: {
      from: moment().startOf('month').format(MOMENT_INPUT_DATE_FORMAT),
      to: moment().endOf('month').format(MOMENT_INPUT_DATE_FORMAT),
    },
    modalState: {
      modal: null,
      item: null,
    },
    rescheduleDetail: {
      date: undefined,
      time: null,
      note: null,
    },
    weekView: {
      start: moment().format('YYYY-MM-DD'),
      end: moment().add(6, 'days').format('YYYY-MM-DD'),
    },
    rescheduleStep: 1,
    rejectStep: 1,
    rejectNote: '',
  } as CounselingProps,
  reducers: {
    resetRescheduleState(state) {
      state.rescheduleDetail = {
        date: undefined,
        time: null,
        note: null,
      }
      state.rescheduleStep = 1
      state.rejectStep = 1
      state.rejectNote = ''
      return state
    },
    setSelectedCounseling(state, action: PayloadAction<CounselingItemProps>) {
      state.selectedCounseling = action.payload
    },
    setModalState(state, action: PayloadAction<CounselingProps['modalState']>) {
      state.modalState = action.payload
    },
    setRescheduleStep(state, action: PayloadAction<number>) {
      state.rescheduleStep = action.payload
    },
    setRescheduleDetail(state, action: PayloadAction<RescheduleDetailProps>) {
      state.rescheduleDetail = action.payload
    },
    setRejectStep(state, action: PayloadAction<number>) {
      state.rejectStep = action.payload
    },
    setRejectNote(state, action: PayloadAction<string | null>) {
      state.rejectNote = action.payload
    },
    setFilterDate(state, action: PayloadAction<{ from: string | undefined; to: string | undefined }>) {
      state.filterDate = {
        from: action.payload.from ? moment(action.payload.from).format(MOMENT_INPUT_DATE_FORMAT) : undefined,
        to: action.payload.to ? moment(action.payload.to).format(MOMENT_INPUT_DATE_FORMAT) : undefined,
      }
    },
  },
})

export const {
  setSelectedCounseling,
  setModalState,
  setRescheduleStep,
  setRescheduleDetail,
  setRejectStep,
  setRejectNote,
  setFilterDate,
  resetRescheduleState,
} = adminCounselingSlice.actions
export default adminCounselingSlice.reducer
