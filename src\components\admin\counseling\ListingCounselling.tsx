'use client'
import { DataGrid } from '@/components/datagrid/DataTable'
import { ColumnDef } from '@tanstack/react-table'
import { useRouter } from 'next/navigation'
import { DateRange } from 'react-day-picker'
import { FilterHeader } from '../FilterHeader'
import { CounsellingMethod } from '@/constans/StaticOptions'
import { useMemo, useRef } from 'react'
import { Routes } from '@/constans/routes'
import { useDispatch } from '@/store'
import { setIsFetched, setMetaCounseling } from '@/store/admin/meta.reducer'
import { CounselingItemProps, setSelectedCounseling } from '@/store/admin/counseling.reducer'
import { AppModal } from '@/components/_common/Modal/AppModal'
import { IModalActionState } from '@/interfaces/components/psikolog/schedule'
import AdminScheduleForm from './reschedule/AdminScheduleForm'
import AdminRejectForm from './RejectSchedule/AdminRejectForm'
import ApproveCounselingDialog from './ApproveCounseling/ApproveCounselingDialog'
import CompleteConfirmationDialog from './CompleteConfirmation/CompleteConfirmation'
import CancelSessionDialog from './CancelSession/CancelSession'
import AdminStartCounselingDialog from './StartCounseling/AdminStartCounselingDialog'
import { useCounselingAction } from './useCounselingAction'

type ListingComponentType = {
  columns: ColumnDef<any, any>
  rangeDate: DateRange | undefined
  actions: string[]
  showSecondFilter?: boolean
  refRefetch: any
  fetchPath: string
  pageFilter: any[]
  bulkFilter: any
  onSearchChange?: (val: string) => void
  onFilterChange?: (val: string) => void
  onStatusChange?: (val: string) => void
}

export const ListingCounselling = ({
  columns,
  rangeDate,
  actions = [],
  showSecondFilter = false,
  refRefetch,
  fetchPath,
  pageFilter,
  bulkFilter,
  onSearchChange,
  onFilterChange,
  onStatusChange,
}: ListingComponentType) => {
  const dispatch = useDispatch()
  const modalRef = useRef<any>(null)
  const router = useRouter()
  const filterOptions = [
    { value: null, label: 'Semua Metode' },
    { value: CounsellingMethod.CALL, label: 'Call' },
    { value: CounsellingMethod.VIDEO, label: 'Video Call' },
  ]
  const secondFilterOptions = [
    { value: CounsellingMethod.ALL, label: 'Semua Status' },
    { value: 'RESCHEDULE_BY_PSYCHOLOGIST', label: 'Konfirmasi Klien' },
    { value: 'RESCHEDULE_BY_CLIENT', label: 'Konfirmasi Psikolog' },
  ]

  const {
    handleCLickStartCounselling,
    handleCLickReschedule,
    handleCLickReject,
    handleClickApproveCounseling,
    handleContactClient,
    handleContactPsychologist,
    handleCancelSession,
    handleClose,
    isRefetchOncloseModalX,
    modalOpen,
    titleModal,
    modalState,
    rescheduleStep,
  } = useCounselingAction(refRefetch.current)

  // const handleConfirmationSession = async (item: ScheduleListProps) => {
  //   try {
  //     await counsellingService.completeCounselling(item.id)
  //     dispatch(
  //       setModalState({
  //         modal:
  //           modalState.modal === IModalActionState.CONFIRMATION_COMPLETELY
  //             ? null
  //             : IModalActionState.CONFIRMATION_COMPLETELY,
  //         item,
  //       })
  //     )
  //   } catch (error) {
  //     toast({
  //       title: 'Terjadi Masalah Jaringan',
  //       description: 'Terjadi masalah pada server, silahkan hubungi admin untuk bantuan.',
  //       variant: 'danger',
  //     })
  //   }
  // }

  const StartCounselling = useMemo(
    () => ({
      name: 'start',
      icon: null,
      label: 'Mulai Konseling',
      onClick: (item: any) => {
        console.log('Mulai Konseling = ', item)
        handleCLickStartCounselling(item?.original)
      },
    }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  )
  const Reschedule = useMemo(
    () => ({
      name: 'reschedule',
      icon: null,
      label: 'Ubah Jadwal',
      onClick: (item: any) => {
        console.log('Ubah Jadwal = ', item)
        handleCLickReschedule(item?.original)
      },
    }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  )
  const ContactPsychologist = useMemo(
    () => ({
      name: 'contactPsychologist',
      icon: null,
      label: 'Hubungi Psikolog',
      onClick: (item: any) => {
        console.log('Hubungi Psikolog = ', item)
        handleContactPsychologist(item?.original)
      },
    }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  )
  const ContactClient = useMemo(
    () => ({
      name: 'contactClient',
      icon: null,
      label: 'Hubungi Klien',
      onClick: (item: any) => {
        console.log('Hubungi Klien = ', item)
        handleContactClient(item?.original)
      },
    }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  )
  const Approve = useMemo(
    () => ({
      name: 'approve',
      icon: null,
      label: 'Terima',
      onClick: (item: any) => {
        console.log('Terima = ', item)
        handleClickApproveCounseling(item?.original)
      },
    }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  )
  const Decline = useMemo(
    () => ({
      name: 'decline',
      icon: null,
      label: 'Tolak',
      onClick: (item: any) => {
        console.log('Tolak = ', item)
        handleCLickReject(item?.original)
      },
    }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  )

  const Cancel = useMemo(
    () => ({
      name: 'cancel',
      icon: null,
      label: 'Batalkan Sesi',
      onClick: (item: any) => {
        console.log('Batal = ', item)
        handleCancelSession(item?.original)
      },
    }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  )

  const actionsMenu = (row: any) => {
    let actionsMenus: any[] = []
    if (actions.includes('approve')) {
      actionsMenus = [...actionsMenus, Approve]
    }
    if (actions.includes('start')) {
      actionsMenus = [...actionsMenus, StartCounselling]
    }
    if (actions.includes('reschedule')) {
      actionsMenus = [...actionsMenus, Reschedule]
    }
    if (actions.includes('decline')) {
      actionsMenus = [...actionsMenus, Decline]
    }
    if (actions.includes('contactPsychologist')) {
      actionsMenus = [...actionsMenus, ContactPsychologist]
    }
    if (actions.includes('contactClient')) {
      actionsMenus = [...actionsMenus, ContactClient]
    }
    if (actions.includes('cancel')) {
      actionsMenus = [...actionsMenus, Cancel]
    }
    return actionsMenus
  }
  const handleClickRow = (row: CounselingItemProps) => {
    dispatch(setSelectedCounseling(row))
    setTimeout(() => {
      router.push(Routes.AdminCounselingDetail.replace('[id]', row.id))
    }, 800)
  }

  return (
    <>
      <div className="flex flex-col">
        <div className="flex gap-x-5 mb-4 justify-between md:justify-start">
          <FilterHeader
            showSecondFilter={showSecondFilter}
            firstOptions={filterOptions}
            secondOptions={secondFilterOptions}
            onChangeSearch={(val) => onSearchChange && onSearchChange(val)}
            onChangeFilter={(val) => onFilterChange && onFilterChange(val)}
            onChangeSecondFilter={(val) => onStatusChange && onStatusChange(val)}
            labelFirstFilter="Metode"
            labelSecondFilter="Status"
          />
        </div>
        <DataGrid
          bulkFilter={bulkFilter}
          pageFilter={pageFilter}
          dateRange={rangeDate}
          fetchPath={fetchPath}
          refetchRef={refRefetch}
          actionMenuList={actionsMenu}
          columns={columns as unknown as ColumnDef<any, any>[]}
          onClickRow={(row) => {
            handleClickRow(row as CounselingItemProps)
          }}
          setMeta={(meta) => {
            dispatch(setIsFetched(meta ? true : false))
            dispatch(setMetaCounseling(meta ? meta : null))
          }}
        />
      </div>
      <AppModal
        ref={modalRef}
        className="md:w-full max-w-[668px]"
        open={modalOpen}
        onClose={() => {
          handleClose(isRefetchOncloseModalX)
        }}
        title={titleModal}
        showOverlay={true}
      >
        {modalState.modal === IModalActionState.RESCHEDULE ? (
          <AdminScheduleForm
            item={modalState.item}
            onClose={() => handleClose()}
            callbackToggleCalendar={(arg) => {
              modalRef.current.className = arg
                ? modalRef.current.className.replace('bg-black/80', 'bg-black/10 ')
                : modalRef.current.className + 'bg-black/80 '
            }}
          />
        ) : modalState.modal === IModalActionState.REJECT ? (
          <AdminRejectForm item={modalState.item} onClose={() => handleClose(true)} />
        ) : modalState.modal === IModalActionState.START_COUNSELING ? (
          <AdminStartCounselingDialog item={modalState.item} onClose={() => handleClose()} />
        ) : modalState.modal === IModalActionState.APPROVE ? (
          <ApproveCounselingDialog item={modalState.item} onClose={() => handleClose()} />
        ) : modalState.modal === IModalActionState.CONFIRMATION_COMPLETELY ? (
          <CompleteConfirmationDialog item={modalState.item} onClose={() => handleClose()} />
        ) : modalState.modal === IModalActionState.CANCEL_SESSION ? (
          <CancelSessionDialog item={modalState.item} onClose={() => handleClose(true)} />
        ) : null}
      </AppModal>
    </>
  )
}
