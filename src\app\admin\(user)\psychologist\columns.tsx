'use client'

import { ColumnDef } from '@tanstack/react-table'
import Link from 'next/link'

export type Payment = {
  id: string
  fact: string
  length: number
}

export const columns: ColumnDef<Payment>[] = [
  {
    accessorKey: 'ID Nation',
    header: 'ID Nation',
    cell: ({ row }) => {
      const amount = parseFloat(row.getValue('amount'))
      const formatted = new Intl.NumberFormat('id-ID', {
        style: 'currency',
        currency: 'IDR',
        maximumFractionDigits: 0,
        minimumFractionDigits: 0,
      }).format(amount)

      return (
        <div className="font-bold hover:underline hover:text-main-100">
          <Link
            href={{
              pathname: `psikolog/${row.getValue('ID Nation')}`,
              query: { id: row.getValue('ID Nation') },
            }}
          >
            {row.getValue('ID Nation')}
          </Link>
        </div>
      )
    },
  },
  {
    accessorKey: 'Nation',
    header: 'Nation',
  },
  {
    accessorKey: 'ID Year',
    header: 'ID Year',
  },
  {
    accessorKey: 'Year',
    header: 'Year',
  },
  {
    accessorKey: 'Population',
    header: 'Population',
  },
  {
    accessorKey: 'Slug Nation',
    header: 'Slug Nation',
  },
]
