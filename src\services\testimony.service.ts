import { config } from '@/constans/config'
import { httpRequest } from '@/utils/network'
import { CategoryProps } from './faq.service'

export class TestimonyService {
  async getTestimonyListing(search: string, filter: number | null) {
    const filterParam = filter ? `?where={ "rating": { "in": [${filter}] } }` : ''
    return await httpRequest({
      method: 'get',
      url: `${config?.apiBaseUrl}api/psychologists/testimonies${filterParam}`,
    })
  }
  async getTestimonyByCategory(category: CategoryProps) {
    return await httpRequest({
      method: 'get',
      url: `${config?.apiBaseUrl}api/testimonies/category/${category}`,
    })
  }

  async adminRemoveTestimony(id: string) {
    return await httpRequest({
      method: 'delete',
      url: `${config?.apiBaseUrl}api/testimonies/${id}`,
    })
  }
  async adminRequestTestimony(id: string) {
    return await httpRequest({
      method: 'post',
      url: `${config?.apiBaseUrl}api/testimonies/${id}/request`,
    })
  }
}

export const testimonyService = new TestimonyService()
