'use client'
import { DataGrid } from '@/components/datagrid/DataTable'
import { Card } from '@/components/_common/ui'
import { HeaderContent } from '../HeaderContent'
import { useRouter } from 'next/navigation'
import { columns } from './columns'
import { PsychologistStatus } from '@/constans/StaticOptions'
import { FilterHeader } from '../FilterHeader'
import { PsychologistProfile } from '@/interfaces/profile-service'
import { useDispatch } from '@/store'
import { Routes } from '@/constans/routes'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { psychologistService } from '@/services/psychologist.service'
import { useToast } from '@/components/ui/use-toast'

export const PsikologComponent = () => {
  const dispatch = useDispatch()
  const { toast } = useToast()
  const router = useRouter()
  const refRefetch = useRef<any>()
  const debounceRef = useRef<NodeJS.Timeout | null>(null)

  const filterOptions = [
    { value: PsychologistStatus.ALL, label: 'Semua Status' },
    { value: PsychologistStatus.ACTIVE, label: 'Aktif' },
    { value: PsychologistStatus.INACTIVE, label: 'Nonaktif' },
  ]

  const [toggle, setToggle] = useState<boolean>(false)
  const [psyichologist, setPsyichologist] = useState<any>(null)
  const actions = ['activate', 'deactivate']
  const [status, setStatus] = useState<string>('All')
  const [searchName, setSearchName] = useState<string>('')

  useEffect(() => {
    if (!toggle) {
      setPsyichologist(null)
    }
  }, [toggle])

  const handleClickRow = (row: PsychologistProfile) => {
    setTimeout(() => {
      router.push(Routes.AdminPsychologistDetail.replace('[id]', row.id))
    }, 800)
  }

  const fetchData = useCallback(async () => {
    try {
      const statusQuery =
        status === PsychologistStatus.ALL
          ? PsychologistStatus.ALL
          : status === PsychologistStatus.ACTIVE
            ? 'true'
            : 'false'
      const response = await psychologistService.adminGetPsychologistList(statusQuery, searchName)

      return {
        data: response.data || [],
        meta: response.meta || { total: 0, perPage: 10, currentPage: 1, lastPage: 1 },
      }
    } catch (error) {
      console.error('Error fetching psychologists:', error)
      return { data: [], meta: { total: 0, perPage: 10, currentPage: 1, lastPage: 1 } }
    }
  }, [status, searchName])

  useEffect(() => {
    if (debounceRef.current) {
      clearTimeout(debounceRef.current)
    }

    debounceRef.current = setTimeout(() => {
      refRefetch.current()
    }, 500)

    return () => {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current)
      }
    }
  }, [searchName])

  useEffect(() => {
    refRefetch.current()
  }, [status])

  const handleActivate = useCallback(async (id?: string) => {
    try {
      await psychologistService.postPsychologistEnable(id ?? '')
      toast({ variant: 'success', title: 'Psikolog berhasil diaktifkan' })
      setTimeout(() => {
        setToggle(false)
        refRefetch.current()
      }, 500)
    } catch (error) {
      toast({ variant: 'danger', title: 'Psikolog gagal diaktifkan' })
    }
  }, [])

  const handleDeactivate = useCallback(async (id?: string) => {
    try {
      await psychologistService.postPsychologistDisable(id ?? '')
      toast({ variant: 'success', title: 'Psikolog berhasil dinonaktifkan' })
      setTimeout(() => {
        setToggle(false)
        refRefetch.current()
      }, 500)
    } catch (error) {
      toast({ variant: 'danger', title: 'Psikolog gagal dinonaktifkan' })
    }
  }, [])

  const Activate = useMemo(
    () => ({
      name: 'activate',
      icon: null,
      label: 'Aktifkan Akun',
      onClick: (item: any) => {
        setPsyichologist(item?.original)
        handleActivate(item?.original?.userIdentity?.id)
      },
    }),
    [handleActivate]
  )

  const Deactivate = useMemo(
    () => ({
      name: 'deactivate',
      icon: null,
      label: 'Nonaktifkan Akun',
      onClick: (item: any) => {
        setPsyichologist(item?.original)
        handleDeactivate(item?.original?.userIdentity?.id)
      },
    }),
    [handleDeactivate]
  )

  const actionsMenu = (row: any) => {
    let actionsMenus: any[] = []
    if (actions.includes('activate')) {
      actionsMenus = [...actionsMenus, { ...Activate, key: `activate-${row.id}` }]
    }
    if (actions.includes('deactivate')) {
      actionsMenus = [...actionsMenus, { ...Deactivate, key: `deactivate-${row.id}` }]
    }
    return actionsMenus
  }

  return (
    <>
      <HeaderContent
        title="Psikolog"
        handleDatePicker={() => undefined}
        handleAdd={() => router.push('/admin/psychologist/add')}
      />
      <Card className="p-6">
        <div className="flex flex-col">
          <div className="flex gap-x-5 mb-4 justify-between md:justify-start">
            <FilterHeader
              firstOptions={filterOptions}
              onChangeSearch={setSearchName}
              onChangeFilter={setStatus}
              labelFirstFilter="Status"
            />
          </div>
          <DataGrid
            fetchData={fetchData}
            actionMenuList={actionsMenu}
            columns={columns}
            handleViewItem={(item: any) => {
              const row = item.original
              router.push(`/admin/psychologist/${row.id}?name=${row.pengguna}&email=${row.email}`)
            }}
            onClickRow={(row) => handleClickRow(row as PsychologistProfile)}
            refetchRef={refRefetch}
          />
        </div>
      </Card>
    </>
  )
}
