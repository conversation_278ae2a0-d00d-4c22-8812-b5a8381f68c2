'use client'
import { HeaderContent } from '@/components/admin/HeaderContent'
import { Tabs, TabsContent } from '@/components/ui/tabs'
import AvaibilityListVew from './AvaibilityListView'
import { AppModal } from '@/components/_common/Modal/AppModal'
import { useState } from 'react'
import { TimezoneForm } from './timezone/TimezoneForm'
import useGetTimezoneLabel from '@/hooks/useGetTimezone.hook'
import { useSelector } from '@/store'
import { translateTimezone } from '@/utils/displayDate'

const AvaibilityComponent = () => {
  const [isChangeTimeZone, setIsChangeTimeZone] = useState<boolean>(false)

  const handleClickTimeChange = () => {
    setIsChangeTimeZone((prevIsChangeTimeZone) => !prevIsChangeTimeZone)
  }
  const { userIdentity } = useSelector((state) => state.PsychologistProfile)
  const momentTimezone = userIdentity?.userConfig?.TIMEZONE
  const longTimezone = translateTimezone(momentTimezone || '')
  const timeZone = useGetTimezoneLabel()
  const labelTimezone = longTimezone && timeZone ? `${longTimezone} (${timeZone})` : null

  return (
    <>
      <HeaderContent className="mb-1.5" title={'Ketersediaan'} />
      <span className="text-body-sm font-medium">Zona waktu: Waktu Indonesia Bagian Barat (WIB)</span>
      {/* hdie for while until got new decission for this feature */}
      {/* <span className="text-body-sm font-medium">
        Zona waktu menyesuaikan lokasi Anda: <span className="font-bold">{labelTimezone}</span>.{' '}
        <span
          className="font-bold cursor-pointer text-main-100 hover:text-main-200"
          onClick={handleClickTimeChange}
        >
          Atur Zona Waktu
        </span>
      </span> */}
      <Tabs defaultValue="list" className="mt-4 w-full">
        <TabsContent className="mt-4" value="list">
          <AvaibilityListVew />
        </TabsContent>
      </Tabs>
      <AppModal
        className="w-full"
        open={isChangeTimeZone}
        onClose={() => {
          handleClickTimeChange()
        }}
        title={'Atur zona waktu'}
        showOverlay={true}
      >
        <TimezoneForm onClose={() => setIsChangeTimeZone(false)} />
      </AppModal>
    </>
  )
}

export default AvaibilityComponent
