import { storage } from '@/lib/firebase/config'
import { useState, useEffect } from 'react'
import { getDownloadURL, ref as storageRef, uploadBytes, uploadBytesResumable } from 'firebase/storage'

export const useStorage = (file: any) => {
  const [progress, setProgress] = useState(0)
  const [error, setError] = useState<any>(null)
  const [url, setUrl] = useState<any>(null)

  // runs every time the file value changes
  useEffect(() => {
    if (file) {
      const imageRef = storageRef(storage, `profile/${file.name}`)
      const uploadTask = uploadBytesResumable(imageRef, file)

      uploadTask.on(
        'state_changed',
        (snapshot) => {
          const percent = Math.round((snapshot.bytesTransferred / snapshot.totalBytes) * 100)

          // update progress
          setProgress(percent)
        },
        (err) => {
          setError('Failed upload image')
        },
        () => {
          // download url

          getDownloadURL(uploadTask.snapshot.ref).then((url) => {
            setUrl(url)
          })
        }
      )
    }
  }, [file])

  return { progress, url, error }
}
