import moment from 'moment-timezone'
import 'moment/locale/id'

export const displayLocalDate = (date: Date) => {
  const result = moment(date).locale('id').format('dddd, DD MMMM YYYY')
  return result
}

export const formatStringToFulldateOutput = (date: string, isUTC: boolean = true) => {
  const result = moment(date).locale('id').utc(isUTC).format('dddd, DD MMMM YYYY')
  return result
}

export const formatStringToDateOutput = (date: string) => {
  const result = moment(date).locale('id').utc(false).format('DD MMMM YYYY')
  return result
}

export const formatStringToDate = (date: string, isUTC: boolean = true) => {
  const result = moment(date).locale('id').utc(isUTC).format('DD')
  return result
}

export const formatStringToDay = (date: string, isUTC: boolean = true) => {
  const result = moment(date).locale('id').utc(isUTC).format('ddd')
  return result
}

export const formatStringToFullDateTimeOutput = ({
  date = '',
  timeLabel,
  isUTC = true,
  timezone,
}: {
  date: string
  timeLabel?: string
  isUTC?: boolean
  timezone?: string
}) => {
  if (!date) return 'Invalid date'
  if (timezone) {
    return `${moment(date)
      .tz(timezone)
      .locale('id')
      .utc(isUTC ?? false)
      .format('DD MMMM YYYY, HH:mm')}${timeLabel ? ` (${timeLabel})` : ''}`
  }
  return `${moment(date)
    .locale('id')
    .utc(isUTC ?? false)
    .format('DD MMMM YYYY, HH:mm')}${timeLabel ? ` (${timeLabel})` : ''}`
}

export const formatStringToTimeOutput = ({
  date = '',
  timeLabel,
  isUTC = true,
  timezone,
}: {
  date: string
  timeLabel?: string
  isUTC?: boolean
  timezone?: string
}) => {
  if (!date) return 'Invalid date'
  if (timezone) {
    return `${moment(date)
      .tz(timezone)
      .locale('id')
      .utc(isUTC ?? false)
      .format('HH:mm')}${timeLabel ? ` (${timeLabel})` : ''}`
  }
  return `${moment(date)
    .locale('id')
    .utc(isUTC ?? false)
    .format('HH:mm')}${timeLabel ? ` (${timeLabel})` : ''}`
}

export const formatStringToStartEndTimeOutput = ({
  date = '',
  duration = 60,
  timeLabel,
  isUTC = true,
  timezone,
}: {
  date: string
  duration: number
  timeLabel?: string
  isUTC?: boolean
  timezone?: string
}) => {
  if (!date) return 'Invalid date time'
  if (timezone) {
    return `${moment(date)
      .tz(timezone)
      .locale('id')
      .utc(isUTC ?? false)
      .format('HH:mm')} - ${moment(date)
      .tz(timezone)
      .add(duration === 120 ? 2 : 1, 'hours')
      .locale('id')
      .utc(isUTC)
      .format('HH:mm')}${timeLabel ? ` ${timeLabel}` : ''}`
  }
  return `${moment(date)
    .locale('id')
    .utc(isUTC ?? false)
    .format('HH:mm')} - ${moment(date)
    .add(duration === 120 ? 2 : 1, 'hours')
    .locale('id')
    .utc(isUTC)
    .format('HH:mm')}${timeLabel ? ` ${timeLabel}` : ''}`
}

export const translateTimezone = (timezone: string) => {
  let label = ''
  switch (timezone) {
    case 'Asia/Jakarta':
    case 'Asia/Pontianak':
      label = 'Waktu Indonesia Bagian Barat'
      break
    case 'Asia/Makassar':
      label = 'Waktu Indonesia Bagian tengah'
      break
    case 'Asia/Jayapura':
      label = 'Waktu Indonesia Bagian Timur'
      break
    default:
      label = 'Zona waktu belum di dukung oleh sistem'
      break
  }
  return label
}
