'use client'
import { FooterSection } from '@/components/home/<USER>/Footer/FooterSection'
import { STATIC_DATA } from '@/constans/STATIC_DATA'
import { getValidAuthTokens } from '@/lib/cookies'

export default function UserLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  const token = getValidAuthTokens()
  const isLoggedIn = !!token

  return (
    <>
      {children}
      <div className="hidden md:grid max-w-screen relative w-full pt-10">
        <FooterSection {...STATIC_DATA.Home.footer} showButtonCounselling />
      </div>
      <div className="md:hidden max-w-screen relative w-full pt-10">
        <FooterSection {...STATIC_DATA.Home.footer} />
      </div>
    </>
  )
}
