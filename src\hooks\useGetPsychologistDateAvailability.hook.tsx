import { psychologistService } from '@/services/psychologist.service'
import { useQuery } from '@tanstack/react-query'

export const useGetPsychologistDateAvailability = (psychologistId: string) => {
  return useQuery<Date[]>({
    queryKey: ['PsychologistDateAvailability', { psychologistId }],
    queryFn: () => {
      if (!psychologistId) return []
      return psychologistService
        .adminGetPsychologistDateAvailability(psychologistId)
        .then((response) => {
          const formattedDates = response.map((date: string) => new Date(date))
          return formattedDates
        })
        .catch((error) => {
          return []
        })
    },
    placeholderData: [],
  })
}
