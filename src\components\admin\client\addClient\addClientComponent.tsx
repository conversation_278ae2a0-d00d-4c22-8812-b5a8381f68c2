'use client'

import * as yup from 'yup'
import Breadcrumb from '@/components/breadcrumbs/Breadcrumbs'
import { HeaderContent } from '../../HeaderContent'
import InformationHeader from './informationHeader'
import { useState } from 'react'
import FormAddClientStepOne from './formAddClientStepOne'
import FormAddClientStepTwo from './formAddClientStepTwo'
import FormAddClientStepThree from './formAddClientStepThree'
import Translation from '@/constans/Translation'
import { RemoveIndex } from '@/utils/type'
import { useToast } from '@/components/ui/use-toast'
import { useRouter } from 'next/navigation'
import { useForm } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import { clientsService } from '@/services/client.service'
import { Routes } from '@/constans/routes'
import ButtonPrimary from '@/components/_common/ButtonPrimary'
import { AppModal } from '@/components/_common/Modal/AppModal'

const validationSchema = yup.object().shape({
  email: yup.string().required(Translation.RequiredEmail).email(Translation.ValidEmail),
  password: yup
    .string()
    .min(6, Translation.PasswordMinChar)
    .matches(/[A-Z]/, Translation.RequiredUppercase)
    .matches(/[a-z]/, Translation.RequiredLowercase)
    .matches(/\d/, Translation.RequiredNumber)
    .required(Translation.PasswordRequired),
  fullName: yup.string().required(Translation.RequiredFullName),
  nickname: yup.string().required(Translation.RequiredNickName),
  phoneNumber: yup.string().required(Translation.RequiredPhoneNumber),
  gender: yup.string().required(Translation.RequiredGender),
  birthDate: yup.date().nullable(),
  childTo: yup.string().required('Wajib diisi').matches(/\d/, Translation.RequiredNumber),
  totalSibling: yup.string().required('Wajib diisi').matches(/\d/, Translation.RequiredNumber),
  occupation: yup.string().nullable(),
  maritalStatus: yup.string().nullable(),
  domicile: yup.string().nullable(),
  ethnicity: yup.string().nullable(),
  religion: yup.string().nullable(),
  workplace: yup.string().nullable(),
  service: yup.string().nullable(),
  bankName: yup.string().nullable(),
  bankAccount: yup.string().nullable(),
  bankAccountName: yup.string().nullable(),
  profilePhoto: yup
    .mixed()
    .nullable()
    .test('isFileOrNull', 'Format file tidak valid', (value) => {
      if (!value) return true
      return value instanceof File
    }),

  video: yup
    .mixed()
    .nullable()
    .test('isFileOrNull', 'Format file tidak valid', (value) => {
      if (!value) return true
      return value instanceof File
    }),
})

export type ClientType = RemoveIndex<yup.InferType<typeof validationSchema>>

export default function AddClientComponent() {
  const router = useRouter()
  const { toast } = useToast()
  const [pageNumber, setPageNumber] = useState(1)
  const [isOpenModal, setIsOpenModal] = useState<boolean>(false)
  const [isLoading, setIsLoading] = useState<boolean>(false)

  const handleClickAccountInformation = () => {
    setIsOpenModal((prevIsOpenModal) => !prevIsOpenModal)
  }

  const {
    register,
    handleSubmit,
    trigger,
    getValues,
    setValue,
    reset,
    formState: { errors, isLoading: isLoadingForm, isSubmitting },
  } = useForm({
    resolver: yupResolver(validationSchema),
    defaultValues: {
      email: '',
      password: '',
      fullName: '',
      nickname: '',
      birthDate: null,
      gender: '',
      phoneNumber: '',
      childTo: '',
      totalSibling: '',
      religion: '',
      ethnicity: '',
      domicile: '',
      maritalStatus: '',
      occupation: '',
      bankName: '',
      bankAccount: '',
      bankAccountName: '',
    },
  })

  async function onSubmit(data: ClientType) {
    setIsLoading(true)
    try {
      const formData = new FormData()

      Object.entries(data).forEach(([key, value]) => {
        if (value instanceof File || value instanceof Blob) {
          formData.append(key, value)
        } else if (Array.isArray(value) || typeof value === 'object') {
          formData.append(key, JSON.stringify(value))
        } else {
          formData.append(key, value.toString())
        }
      })

      const createdClient = await clientsService.postClient(formData)

      if (!createdClient) {
        throw new Error('API response is empty')
      }

      toast({
        variant: 'success',
        title: 'Submit data berhasil. Terimakasih',
      })

      reset()
      router.replace(Routes.AdminClientDetail.replace('[id]', createdClient.id))
    } catch (error) {
      console.error('Error during submission:', error)
      toast({
        variant: 'danger',
        title: 'Terjadi kesalahan saat menyimpan data, silahkan ulangi beberapa saat lagi.',
      })
    } finally {
      setIsLoading(false)
    }
  }

  const nextPage = async () => {
    if (pageNumber === 1) {
      const isValid = await trigger(['fullName', 'nickname', 'gender', 'phoneNumber', 'email', 'password'])
      if (!isValid) {
        return
      }
      setPageNumber((prev) => prev + 1)
    } else if (pageNumber === 2) {
      const isValid = await trigger(['childTo', 'totalSibling'])
      if (!isValid) {
        return
      }
      setPageNumber((prev) => prev + 1)
    } else {
      handleSubmit(onSubmit)()
    }
  }
  const prevPage = () => {
    if (pageNumber > 1) {
      setPageNumber(pageNumber - 1)
    }
  }
  return (
    <>
      <Breadcrumb containerClasses="pb-2" pageName="Add Client" />
      <HeaderContent title="Tambah Klien" className="mb-0 md:mb-0" />
      <div className="grid gap-y-4">
        {pageNumber === 1 && (
          <FormAddClientStepOne
            pageNumber={pageNumber}
            register={register}
            getValues={getValues}
            setValue={setValue}
            errors={errors}
            onSubmit={() => trigger()}
          />
        )}
        {pageNumber === 2 && (
          <FormAddClientStepTwo
            pageNumber={pageNumber}
            register={register}
            getValues={getValues}
            setValue={setValue}
            errors={errors}
            onSubmit={() => trigger()}
          />
        )}
        {pageNumber === 3 && (
          <FormAddClientStepThree
            pageNumber={pageNumber}
            register={register}
            getValues={getValues}
            setValue={setValue}
            errors={errors}
            onSubmit={() => trigger()}
          />
        )}

        <div className="flex justify-end space-x-2">
          <ButtonPrimary
            onClick={handleClickAccountInformation}
            className="min-w-[140px]"
            size="base"
            variant="outlined"
            color="gray"
          >
            Batal
          </ButtonPrimary>
          {pageNumber > 1 && (
            <ButtonPrimary
              variant="outlined"
              size="xs"
              className="p-3 min-w-[140px] space-x-2"
              onClick={() => prevPage()}
              color="gray"
            >
              Kembali
            </ButtonPrimary>
          )}
          <ButtonPrimary
            isLoading={isSubmitting || isLoading || isLoadingForm}
            disabled={isSubmitting || isLoading || isLoadingForm}
            variant="contained"
            size="xs"
            className="p-3 min-w-[140px] space-x-2"
            onClick={() => nextPage()}
          >
            {pageNumber === 3 ? 'Simpan' : 'Selanjutnya'}
          </ButtonPrimary>
        </div>

        <AppModal
          className={`w-full transition-all duration-500 ease-in-out ${isOpenModal ? 'opacity-100 scale-100' : 'opacity-0 scale-0'}`}
          open={isOpenModal}
          onClose={() => {
            handleClickAccountInformation()
          }}
          title={'Batalkan proses tambah Klien?'}
          showOverlay={true}
        >
          <div className="flex flex-col bg-white gap-4">
            <div className="flex flex-col items-end gap-4">
              <div className="flex flex-col justify-center items-center gap-4">
                <p className="text-[16px] text-[#222222]">
                  Data yang Anda sudah masukkan tidak akan tersimpan.
                </p>
              </div>
              <div className="flex gap-4 items-end justify-end mt-6 w-full md:w-[70%]">
                <ButtonPrimary
                  className="min-w-[140px]"
                  size="base"
                  variant="outlined"
                  color="gray"
                  onClick={() => {
                    handleClickAccountInformation()
                  }}
                >
                  Tidak
                </ButtonPrimary>
                <ButtonPrimary
                  className="min-w-[140px]"
                  size="base"
                  variant="contained"
                  onClick={() => router.push('/admin/client')}
                >
                  Iya
                </ButtonPrimary>
              </div>
            </div>
          </div>
        </AppModal>
      </div>
    </>
  )
}
