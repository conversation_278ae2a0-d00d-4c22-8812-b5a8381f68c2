// import { useToast } from '@/components/ui/use-toast'
import { GlobalAllStatus } from '@/constans/StaticOptions'
import { clientReportService } from '@/services/clientReport.service'
import { testimonyService } from '@/services/testimony.service'
import { useSelector } from '@/store'
import { AuthRole } from '@/store/auth/auth.action'
import { useQuery } from '@tanstack/react-query'

export const useGetTestimonyList = (search: string, filter: number | string) => {
  const { user } = useSelector((state) => state.Authentication)
  const filterParam = filter === GlobalAllStatus ? null : (filter as number)

  console.log(filterParam)

  return useQuery({
    queryKey: ['PsychologistTestimonyList', { search, filter }],
    queryFn: () => {
      if (user?.role === AuthRole.PSIKOLOG) {
        return testimonyService
          .getTestimonyListing(search, filterParam)
          .then((response) => {
            return response?.data
          })
          .catch((error) => {
            // do soemthing
          })
      }
    },
  })
}
