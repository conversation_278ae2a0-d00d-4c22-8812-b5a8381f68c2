import ButtonPrimary from '@/components/_common/ButtonPrimary'
import AppInput from '@/components/_common/input/Input'
import { RadioInput } from '@/components/_common/RadioInput/RadioInput'
import { AppBigText } from '@/components/_common/ui'
import { useToast } from '@/components/ui/use-toast'
import { OtherReasonCancelationOptions, ReasonCancelationOptions } from '@/constans/StaticOptions'
import { counsellingService } from '@/services/counselling.service'
import { useState } from 'react'

export default function CancelSessionDialog({ item, onClose }: { item: any; onClose: () => void }) {
  const { toast } = useToast()
  const [reason, setReason] = useState<string>('')
  const [note, setNote] = useState<string>('')

  const handleCancelSession = async () => {
    const payload = {
      message: reason === OtherReasonCancelationOptions ? note : reason,
    }
    if (!reason) {
      toast({
        description: 'Silahkan pilih alasan pembatalan terlebih dahulu.',
        variant: 'danger',
      })
      return
    }
    try {
      await counsellingService.cancelCounselling(payload, item.id)
      toast({
        title: 'Berhasil',
        description: 'Sesi konseling berhasil dibatalkan.',
        variant: 'success',
      })
      onClose && onClose()
    } catch (error) {
      toast({
        title: 'Terjadi Masalah Jaringan',
        description: 'Terjadi masalah pada server, silahkan hubungi admin untuk bantuan.',
        variant: 'danger',
      })
    }
  }

  return (
    <>
      <div className="grid grid-cols-1 grid-rows-1 gap-4">
        <div className="grid gap-x-2 gap-y-4">
          <RadioInput
            options={ReasonCancelationOptions}
            name={'reason'}
            value={reason}
            label="Alasan Pembatalan:"
            onChange={(val) => {
              setReason(val)
            }}
          />
          {reason === OtherReasonCancelationOptions && (
            <AppInput
              value={note}
              className="pt-0"
              label="Catatan untuk sesi ini"
              type="text"
              name="note"
              onChange={(val) => setNote(val.target.value)}
            />
          )}
        </div>

        <div className="flex flex-wrap justify-center sm:justify-end items-center gap-2 pt-6">
          <ButtonPrimary
            onClick={() => onClose && onClose()}
            className="rounded-sm w-full sm:w-auto"
            variant="outlined"
            size="xs"
            color="gray"
          >
            Kembali
          </ButtonPrimary>
          <ButtonPrimary
            onClick={() => handleCancelSession()}
            className="rounded-sm w-full sm:w-auto"
            variant="contained"
            size="xs"
          >
            Ya, Batalkan
          </ButtonPrimary>
        </div>
      </div>
    </>
  )
}
