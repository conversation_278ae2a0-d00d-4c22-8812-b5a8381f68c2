import { config } from '@/constans/config'
import { httpRequest } from '@/utils/network'

export class DashboardService {
  // PSYCHOLOGIST DAHBOARD
  async getOnboardingSteps() {
    return await httpRequest({
      method: 'get',
      url: `${config?.apiBaseUrl}api/profile/onboarding-steps`,
    })
  }
  async postOnboardingSteps(payload: { onboardingSteps: string[] }) {
    return await httpRequest({
      method: 'post',
      url: `${config?.apiBaseUrl}api/profile/onboarding-steps`,
      data: payload,
    })
  }
  async getCounselingCounter() {
    return await httpRequest({
      method: 'get',
      url: `${config?.apiBaseUrl}api/psychologists/counseling/counter`,
    })
  }
  async getCounselingByRangeTime(startTime: string, endTime: string) {
    return await httpRequest({
      method: 'get',
      url: `${config?.apiBaseUrl}api/psychologists/counseling/widget?startTime=${startTime}&endTime=${endTime}`,
    })
  }

  // ADMIN DAHBOARD
  async adminGetTotalCounselingCounter() {
    return await httpRequest({
      method: 'get',
      url: `${config?.apiBaseUrl}api/counselings/widget/counter`,
    })
  }
  async adminGetIncome() {
    return await httpRequest({
      method: 'get',
      url: `${config?.apiBaseUrl}api/transactions/income/counter`,
    })
  }
  async adminGetCounselingByRangeTime(startTime: string, endTime: string) {
    return await httpRequest({
      method: 'get',
      url: `${config?.apiBaseUrl}api/counselings/widget?startTime=${startTime}&endTime=${endTime}`,
    })
  }
  async adminGetUserCounter() {
    return await httpRequest({
      method: 'get',
      url: `${config?.apiBaseUrl}api/user-identity/counter`,
    })
  }
  async adminGetCounselingCounter() {
    return await httpRequest({
      method: 'get',
      url: `${config?.apiBaseUrl}api/counselings/counter`,
    })
  }
  async adminGetTotaltestimonyCounter() {
    return await httpRequest({
      method: 'get',
      url: `${config?.apiBaseUrl}api/testimonies/counter`,
    })
  }
}

export const dashboardService = new DashboardService()
