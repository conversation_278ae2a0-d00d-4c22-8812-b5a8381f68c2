'use client'
import { useState } from 'react'
import ButtonPrimary from '@/components/_common/ButtonPrimary'
import AppInput from '@/components/_common/input/Input'
import { Separator } from '@/components/ui/separator'
import { AppSelect } from '@/components/_common/Select/AppSelect'
import { RadioInput } from '@/components/_common/RadioInput/RadioInput'
import { DatePicker } from '@/components/ui/DatePicker'

type FormInputProfile = {
  label: string
  type: 'text' | 'select' | 'radio' | 'date' | 'file' | 'textarea' | 'number'
  value?: any
  children: React.ReactNode
  isEdit?: boolean
  onChange?: (value: string) => void
  withMargin?: boolean
  onSubmit?: () => void
  onCancel?: () => void
  renderViewMode?: React.ReactNode
  option?: any
}

export const FormInputProfile = ({
  label,
  isEdit,
  type,
  onChange,
  value,
  children,
  withMargin,
  onSubmit,
  renderViewMode,
  onCancel,
  option,
}: FormInputProfile) => {
  const [toggleEdit, setToggleEdit] = useState<boolean>(isEdit || false)

  const toggleIsEdit = () => {
    if (toggleEdit) onCancel && onCancel()
    setToggleEdit((prevToggleEdit) => !prevToggleEdit)
  }

  const handleSubmit = () => {
    onSubmit && onSubmit()
    setToggleEdit(false)
  }

  // Function to render the appropriate edit component based on type
  const renderEditComponent = () => {
    switch (type) {
      case 'text':
      case 'number':
        return <AppInput type={type} value={value} onChange={(e) => onChange && onChange(e.target.value)} />
      case 'textarea':
        return (
          <AppInput
            type="textarea"
            rows={4}
            value={value}
            onChange={(e) => onChange && onChange(e.target.value)}
          />
        )
      case 'select':
        return (
          <AppSelect
            options={option || []}
            value={value}
            onChange={(val) => onChange && onChange(val)}
            className="h-[50px]"
            placeholder={`Pilih ${label}`}
          />
        )
      case 'radio':
        return (
          <RadioInput
            options={option || []}
            name={label.toLowerCase().replace(/\s/g, '')}
            value={value}
            onChange={(val) => onChange && onChange(val)}
          />
        )
      case 'date':
        return (
          <DatePicker
            date={value instanceof Date ? value : undefined}
            onSelect={(date) => onChange && onChange(date?.toISOString() || '')}
            className="w-full"
            placeholder={`Pilih ${label}`}
          />
        )
      default:
        return children
    }
  }

  return (
    <>
      <div className="flex flex-col py-4">
        <div className="flex justify-between w-full">
          <span className="text-gray-200 text-body-sm">{label}</span>
          <span
            className="text-body-md font-bold text-main-100 hover:text-main-200 cursor-pointer w-[40px] text-right"
            onClick={toggleIsEdit}
          >
            {toggleEdit ? 'Batal' : 'Atur'}
          </span>
        </div>
        <div className={toggleEdit ? 'py-6' : ''}>
          <div className={`${withMargin ? 'mr-[40px]' : ''}`}>{toggleEdit ? children : renderViewMode}</div>

          {toggleEdit && (
            <div className="mt-4">
              <ButtonPrimary variant="contained" size="xs" onClick={handleSubmit}>
                Simpan
              </ButtonPrimary>
            </div>
          )}
        </div>
      </div>
      {!toggleEdit && <Separator orientation="horizontal" />}
    </>
  )
}
