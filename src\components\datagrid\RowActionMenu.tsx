import React from 'react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '../ui/DropdownMenu'
import { Button } from '../ui/button'
import { IIcons, SVGIcons } from '../_common/icon'

export type ActionMenuItemType = {
  name: string
  icon?: JSX.Element
  label: string
  disabled?: boolean
  onClick?: (item?: any) => void
  items?: (ActionMenuItemType | 'divider')[]
}

export type RowActionMenuProps = {
  actions: ActionMenuItemType[]
  row: any
}

export const RowActionMenu = ({ actions, row }: RowActionMenuProps) => {
  const [isOpen, setIsOpen] = React.useState<boolean>(false)

  return actions && actions.length > 0 ? (
    <DropdownMenu modal={false} onOpenChange={() => setIsOpen(!isOpen)} open={isOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          className="flex items-center gap-x-2"
          onClick={(e) => {
            e.stopPropagation()
          }}
        >
          <div>Pilih Aksi</div>
          <SVGIcons name={!isOpen ? IIcons.ArrowDown : IIcons.ArrowUp} />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent>
        <DropdownMenuGroup>
          {actions.map((menuItem: ActionMenuItemType, index: number) => (
            <DropdownMenuItem
              key={`${menuItem.name}${index}`}
              onClick={(e) => {
                e.stopPropagation()
                menuItem.onClick && menuItem.onClick(row)
              }}
              disabled={menuItem.disabled || false}
            >
              <span>{menuItem.label}</span>
            </DropdownMenuItem>
          ))}
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  ) : null
}
