import { useToast } from '@/components/ui/use-toast'
import { balanceService } from '@/services/balance.service'
import { dashboardService } from '@/services/dashboard.service'
import { useSelector } from '@/store'
import { AuthRole } from '@/store/auth/auth.action'
import { useQuery } from '@tanstack/react-query'

export const useGetPsychologistBalance = () => {
  const { user } = useSelector((state) => state.Authentication)
  const { toast } = useToast()
  return useQuery({
    queryKey: ['PsychologistBalance'],
    queryFn: () => {
      if (user?.role === AuthRole.PSIKOLOG) {
        return balanceService
          .getPsychologistBalance()
          .then((response) => {
            return response
          })
          .catch((error) => {
            toast({
              title: 'Gagal',
              description: 'Terjadi masalah dengan server, Silahkan hubungi Admin',
              variant: 'danger',
            })
          })
      }
    },
  })
}
