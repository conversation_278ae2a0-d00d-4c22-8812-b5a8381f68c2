import { Separator } from '../ui/separator'

export const LoadingAccordionClientReport = () => {
  return (
    <>
      <div className="bg-white border border-line-200 rounded-md w-full mx-auto">
        <div className="animate-pulse flex flex-col space-y-1">
          <div className="rounded-t-md w-full">
            <div className="space-y-1">
              <div className="grid grid-cols-6 gap-4 p-4">
                <div className="h-5 bg-slate-200 rounded col-span-2"></div>
                <div className="h-5 bg-slate-200 rounded col-span-1"></div>
                <div className="h-5 bg-slate-200 rounded col-start-6"></div>
                <div className="flex gap-4 col-span-4">
                  <div className="h-3 bg-slate-200 rounded w-3"></div>
                  <div className="h-3 bg-slate-200 rounded w-1/3"></div>
                  <div className="h-3 bg-slate-200 rounded w-3"></div>
                  <div className="h-3 bg-slate-200 rounded w-1/3"></div>
                  <div className="h-3 bg-slate-200 rounded w-3"></div>
                  <div className="h-3 bg-slate-200 rounded w-1/3"></div>
                </div>
              </div>
            </div>
          </div>
          <Separator />
          <div className="flex flex-col w-full sm:flex-row gap-3 p-4">
            <div className="h-8 w-20 bg-slate-200 rounded-full"></div>
          </div>
        </div>
      </div>
    </>
  )
}
