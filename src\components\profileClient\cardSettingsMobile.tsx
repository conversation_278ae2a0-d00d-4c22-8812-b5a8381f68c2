'use client'
import { useRouter } from 'next/navigation'
import { IIcons, SVGIcons } from '../_common/icon'

type IconName = keyof typeof IIcons // mendefinisikan tipe dari icon

export default function CardSettingsMobile({
  icon,
  icon2,
  icon3,
  header,
  title,
  title2,
  title3,
  routeInfo,
  routeSetting,
  routePusat,
  routeKebijakan,
  routeSyarat,
  style,
}: {
  icon: IconName
  icon2: IconName
  icon3: IconName
  header: string
  title: string
  title2: string
  title3: string
  routeInfo: string
  routeSetting: string
  routePusat: string
  routeKebijakan: string
  routeSyarat: string
  style: string
}) {
  const router = useRouter()

  const route1 = () => {
    if (title == 'Informasi Personal') {
      return (
        <div onClick={() => router.push(routeInfo)} className="flex justify-between items-center">
          <div className="flex gap-2 items-center">
            <SVGIcons name={IIcons[icon]} className="w-6 h-6" />
            <span className="text-[16px] text-[#535353]">{title}</span>
          </div>
          <SVGIcons name={IIcons.ArrowRight} className="w-6 h-6" />
        </div>
      )
    } else {
      return (
        <div onClick={() => router.push(routePusat)} className="flex justify-between items-center">
          <div className="flex gap-2 items-center">
            <SVGIcons name={IIcons[icon]} className="w-6 h-6" />
            <span className="text-[16px] text-[#535353]">{title}</span>
          </div>
          <SVGIcons name={IIcons.ArrowRight} className="w-6 h-6" />
        </div>
      )
    }
  }
  const route2 = () => {
    if (title2 == 'Pengaturan') {
      return (
        <div onClick={() => router.push(routeSetting)} className="flex justify-between items-center">
          <div className="flex gap-2 items-center">
            <SVGIcons name={IIcons[icon2]} className="w-6 h-6" />
            <span className="text-[16px] text-[#535353]">{title2}</span>
          </div>
          <SVGIcons name={IIcons.ArrowRight} className="w-6 h-6" />
        </div>
      )
    } else {
      return (
        <div onClick={() => router.push(routeKebijakan)} className="flex justify-between items-center">
          <div className="flex gap-2 items-center">
            <SVGIcons name={IIcons[icon2]} className="w-6 h-6" />
            <span className="text-[16px] text-[#535353]">{title2}</span>
          </div>
          <SVGIcons name={IIcons.ArrowRight} className="w-6 h-6" />
        </div>
      )
    }
  }
  const route3 = () => {
    if (title3 == 'Syarat dan Ketentuan') {
      return (
        <div onClick={() => router.push(routeSyarat)} className={`${style} justify-between items-center`}>
          <div className="flex gap-2 items-center">
            <SVGIcons name={IIcons[icon3]} className="w-6 h-6" />
            <span className="text-[16px] text-[#535353]">{title3}</span>
          </div>
          <SVGIcons name={IIcons.ArrowRight} className="w-6 h-6" />
        </div>
      )
    } else {
      return (
        <div className={`${style} justify-between items-center`}>
          <div className="flex gap-2 items-center">
            <SVGIcons name={IIcons[icon3]} className="w-6 h-6" />
            <span className="text-[16px] text-[#535353]">{title3}</span>
          </div>
          <SVGIcons name={IIcons.ArrowRight} className="w-6 h-6" />
        </div>
      )
    }
  }

  return (
    <div className="flex flex-col gap-4 pb-4 border-b border-[#EBEBEB]">
      <h4 className="font-bold text-[16px] text-[#1F282D]">{header}</h4>
      {route1()}
      {route2()}
      {route3()}
    </div>
  )
}
