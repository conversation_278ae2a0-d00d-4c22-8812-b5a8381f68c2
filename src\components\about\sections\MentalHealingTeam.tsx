import Image from 'next/image'
import Link from 'next/link'
import LogoLinkedIn from '@/assets/icons/linkedIn.svg'

type MentalHealingTeamProps = {
  heading: string
  subHeading: string
  content: any[]
}
export const MentalHealingTeam = ({ heading, subHeading, content }: MentalHealingTeamProps) => {
  return (
    <div className="flex flex-col gap-y-10 items-center px-4">
      <div className="flex flex-col gap-y-3 items-center">
        <span className="text-subheading-md md:text-[38px] md:leading-[42px] font-bold text-gray-400] text-center text-[#222222]">
          {heading}
        </span>
        <span className="text-body-sm md:text-body-lg font-medium text-gray-300 text-center">
          {subHeading}
        </span>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-16">
        {content.map((item) => {
          return (
            <div
              key={item.id}
              className="flex flex-col gap-y-4 items-center p-8 border border-main-100 rounded-lg w-full"
            >
              <div className="relative h-[210px] w-[210px] flex justify-center">
                <Image
                  src={item.image}
                  fill
                  alt="photo-mh"
                  style={{
                    borderRadius: '100%',
                    objectFit: 'cover',
                    objectPosition: 'center',
                  }}
                />
              </div>
              <div className="flex flex-col gap-y-1">
                <span className="text-heading-sm font-bold text-main-100 text-center">{item.name}</span>
                <span className="text-body-lg text-gray-300 text-center font-bold">{item.as}</span>
              </div>
              <div className="flex flex-col gap-y-1">
                <span className="text-body-md md:text-body-lg font-medium text-gray-300 text-center">
                  {item.bio}
                </span>
                <Link
                  href={item.linkedin}
                  passHref={true}
                  target="_blank"
                  className="flex justify-center items-center"
                >
                  <span className="text-body-lg text-main-100 text-center flex items-center gap-x-2 font-bold">
                    <LogoLinkedIn /> LinkedIn
                  </span>
                </Link>
              </div>
            </div>
          )
        })}
      </div>
    </div>
  )
}
