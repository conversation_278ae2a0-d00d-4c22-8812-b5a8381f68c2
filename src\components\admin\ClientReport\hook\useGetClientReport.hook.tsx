import { clientReportService } from '@/services/clientReport.service'
import { useQuery } from '@tanstack/react-query'

export const useGetClientReport = (id: string) => {
  return useQuery({
    queryKey: ['PsychologistClientReports', { id }],
    queryFn: () => {
      return clientReportService
        .adminGetClientReport(id)
        .then((response) => {
          return response
        })
        .catch((error) => {
          // do soemthing
        })
    },
  })
}
