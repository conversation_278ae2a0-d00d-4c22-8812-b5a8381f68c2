import { formatStringToDateOutput } from '@/utils/displayDate'
import ContentClientReportDetails from '@/components/admin/ClientReport/details/ContentClientReportDetail'

export default function ClientReportDetails({ report }: { report: any }) {
  return (
    <div className="flex flex-col gap-4 py-4 border-t border-[#EBEBEB]">
      <ContentClientReportDetails title={'Anamnesa'} content={report?.anamnesis ?? '-'} />
      <ContentClientReportDetails title={'Intervensi'} content={report?.intervention ?? '-'} />
      <ContentClientReportDetails title={'Tugas'} content={report?.task ?? '-'} />
      <ContentClientReportDetails title={'Catatan untuk Klien'} content={report?.notesForClient ?? '-'} />
      <span className="text-[14px] text-[#535353]">
        Klien report dibuat: {formatStringToDateOutput(report?.createdAt)}
      </span>
    </div>
  )
}
