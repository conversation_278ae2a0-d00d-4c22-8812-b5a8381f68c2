'use client'
import TabItem from '@/components/_common/tabs/TabItem'
import TabList from '@/components/_common/tabs/TabList'
import { HeaderContent } from '@/components/admin/HeaderContent'
import PsikologProfileRoot from './PsychologistProfile/PsychologistProfileRoot'
import PersonalDataPsikolog from './PersonalData/PersonalDataPsikolog'
import MediaComponentRoot from './media/MediaComponentRoot'
import AccountRoot from './account/AccountRoot'
import SecurityRoot from './security/SecurityRoot'
import { useEffect, useState } from 'react'
import { profileService } from '@/services/profile.service'
import { useSearchParams } from 'next/navigation'
import { useGetProfile } from '@/hooks/useGetProfile.hook'
import { PsychologistProfile } from '@/interfaces/profile-service'
// import { Profile } from '@/interfaces/profile-service'

export const SettingPageComponent = () => {
  const searchParam = useSearchParams()
  const activeTabFromUrl = searchParam.get('activeTab')
  const [activeTabIndex, setActiveTabIndex] = useState<number>(0)
  // const [setting, setSetting] = useState<any | null>(null)
  const { data: setting, refetch } = useGetProfile()

  const SettingTab = [
    {
      label: 'Profil Psikolog',
      content: <PsikologProfileRoot setting={(setting ?? {}) as PsychologistProfile} />,
    },
    {
      label: 'Profil Pribadi',
      content: <PersonalDataPsikolog {...(setting as PsychologistProfile)} />,
    },
    {
      label: 'Media',
      content: <MediaComponentRoot refetch={refetch} {...(setting as PsychologistProfile)} />,
    },
    {
      label: 'Rekening',
      content: <AccountRoot {...(setting as PsychologistProfile)} />,
    },
    {
      label: 'Login & Keamanan',
      content: <SecurityRoot {...(setting as PsychologistProfile)} />,
    },
  ]

  // useEffect(() => {
  //   if (activeTabFromUrl) setActiveTabIndex(Number(activeTabFromUrl))
  //   getProfile()
  // }, [activeTabFromUrl])

  // const getProfile = async () => {
  //   try {
  //     const response = await profileService.getPsychologistProfile()
  //     if (response) {
  //       setSetting(response)
  //     }
  //     console.log(response)
  //   } catch (error) {}
  // }
  return (
    <div>
      <HeaderContent title={'Pengaturan'} />
      <div className="gap-4 mt-4">
        <TabList
          onClickTabs={(index) => {
            console.log(index)
            setActiveTabIndex(index)
          }}
          className="sticky top-navbar z-20 bg-white"
          activeTabIndex={activeTabIndex}
        >
          {SettingTab.map((setting, index) => {
            return (
              <TabItem className="bg-main-100" key={index} label={setting.label}>
                {setting.content}
              </TabItem>
            )
          })}
        </TabList>
      </div>
    </div>
  )
}

export default SettingPageComponent
