// lib/cookies.ts
import { deleteCookie, getCookie } from 'cookies-next'

// helpers to get cookies
const getAuthCookie = (name: string) => {
  const cookie = getCookie(name)

  if (!cookie) return undefined

  return Buffer.from(cookie, 'base64').toString('ascii')
}

export const getValidAuthTokens = () => {
  const token = getAuthCookie('auth_token')

  // const now = new Date()
  // const tokenDate = new Date(token || 0)
  return token
}

export const getValidAuthRole = () => {
  const role = getAuthCookie('auth_role')
  return role
}

export const deleteAuthCookies = () => {
  deleteCookie('auth_token')
  deleteCookie('auth_role')
}
