'use client'
import { CounselingData } from '@/hooks/useCounselings'
import CardTransaksi from './cardTransaksi'
import { formatDate, formatTime } from '../../utils/utilsDate'

interface ContentKonselingProps {
  title: string
  data: CounselingData[]
  loading: boolean
}

export default function ContentKonseling({ title, data, loading }: ContentKonselingProps) {
  if (loading) {
    return <div className="flex justify-center my-8">loading...</div>
  }

  if (data.length === 0) {
    return (
      <div className="text-center my-8 px-4">
        <p className="text-gray-500">Tidak ada data konseling saat ini.</p>
      </div>
    )
  }

  return (
    <div className="flex flex-col gap-4 mt-3 px-4 md:px-0">
      {data.map((item) => {
        const startDate = new Date(item.startTime)
        const formattedDate = formatDate(startDate)
        const formattedStartTime = formatTime(new Date(item.startTime))
        const formattedEndTime = formatTime(new Date(item.endTime))

        return (
          <CardTransaksi
            key={item.id}
            id={item.id}
            title={title}
            buttonType="contained"
            buttonTitle="Mulai Konseling"
            buttonTitle2=""
            textButtonColor=""
            numberButton="1"
            date={formattedDate}
            time={`${formattedStartTime} - ${formattedEndTime} WIB`}
            method={item.method}
            psychologistName={item.psychologist.fullName}
            psychologistPhoto={item.psychologist.profilePhoto}
          />
        )
      })}
    </div>
  )
}
