import { configureStore } from '@reduxjs/toolkit'
import {
  TypedUseSelectorHook,
  useDispatch as useAppDispatch,
  use<PERSON>elector as useAppSelector,
} from 'react-redux'
import rootReducer from './rootReducer'
import { persistStore, FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER } from 'redux-persist'
import { injectDispatch } from '@/utils/network'

// Define the root state type using the ReturnType utility of TypeScript
export type RootState = ReturnType<typeof rootReducer>

// Define the type for dispatching actions from the store
export type AppDispatch = typeof store.dispatch

const store = configureStore({
  reducer: rootReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
      },
      immutableCheck: false,
    }),
})

// Extract the dispatch function from the store for convenience
const { dispatch } = store

const useSelector: TypedUseSelectorHook<RootState> = useAppSelector

// Create a custom useDispatch hook with typed dispatch
const useDispatch = () => useAppDispatch<AppDispatch>()

// Inject dispatch
injectDispatch(store.dispatch)

// Export the Redux store, dispatch, useSelector, and useDispatch for use in components
export const Persistor = persistStore(store)
export { store, dispatch, useSelector, useDispatch }
