import { combineReducers } from 'redux'
import enduser from './enduser/enduser.reducer'
import psikologSchedule from './psikolog/schedule.reducer'
import psikologAvaibility from './psikolog/avaibility.reducer'
import psychologistProfile from './psikolog/profile.reducer'
import adminMetaReducer from './admin/meta.reducer'
import adminCounselingReducer from './admin/counseling.reducer'
import Authentication from './auth/auth.reducer'
import persistReducer from 'redux-persist/es/persistReducer'
import storage from 'redux-persist/lib/storage'

const persistConfig = {
  key: 'root',
  storage,
  blacklist: ['token'],
}

const rootReducer = combineReducers({
  Authentication: persistReducer(persistConfig, Authentication),
  Meta: adminMetaReducer,
  AdminCounseling: adminCounselingReducer,
  psikologSchedule,
  psikologAvaibility,
  PsychologistProfile: psychologistProfile,
  enduser,
})

export default rootReducer
