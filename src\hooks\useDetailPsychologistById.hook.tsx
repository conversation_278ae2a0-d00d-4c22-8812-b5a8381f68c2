import { psychologistService } from '@/services/psychologist.service'
import { useQuery } from '@tanstack/react-query'

export const useDetailPsychologistById = (psychologistId: string) => {
  return useQuery({
    queryKey: ['AdminPsychologistDetails', { psychologistId }],
    queryFn: () =>
      psychologistService
        .adminGetPsychologistDetails(psychologistId)
        .then((response) => {
          return response
        })
        .catch((error) => {
          return null
        }),
  })
}
