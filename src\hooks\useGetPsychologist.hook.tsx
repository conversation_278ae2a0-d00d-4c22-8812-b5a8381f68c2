import { useToast } from '@/components/ui/use-toast'
import { profileService } from '@/services/profile.service'
import { psychologistService } from '@/services/psychologist.service'
import { useSelector } from '@/store'
import { AuthRole } from '@/store/auth/auth.action'
import { useQuery } from '@tanstack/react-query'

export const useGetPsychologist = (id: string) => {
  const { user } = useSelector((state) => state.Authentication)
  const { toast } = useToast()
  return useQuery({
    queryKey: ['Psychologist', { id }],
    queryFn: () => {
      if (user?.role === AuthRole.PSIKOLOG) {
        return psychologistService
          .getPsychologistById(id)
          .then((response) => {
            return response
          })
          .catch((error) => {
            toast({
              title: 'Gagal',
              description: 'Terjadi masalah dengan server, Silahkan hubungi Admin',
              variant: 'danger',
            })
          })
      }
    },
  })
}
