import { useToast } from '@/components/ui/use-toast'
import { dashboardService } from '@/services/dashboard.service'
import { useSelector } from '@/store'
import { AuthRole } from '@/store/auth/auth.action'
import { useQuery } from '@tanstack/react-query'

export const useGetOnboardingSteps = () => {
  const { user } = useSelector((state) => state.Authentication)
  const { toast } = useToast()
  return useQuery({
    queryKey: ['DashboardPsychologist'],
    queryFn: () => {
      if (user?.role === AuthRole.PSIKOLOG) {
        return dashboardService
          .getOnboardingSteps()
          .then((response) => {
            return response
          })
          .catch((error) => {
            toast({
              title: 'Gagal',
              description: 'Terjadi masalah dengan server, Silahkan hubungi Admin',
              variant: 'danger',
            })
          })
      }
    },
  })
}
