'use client'

import AppInput from '@/components/_common/input/Input'

export default function ClientForm() {
  return (
    <form action="" className="flex flex-col gap-4">
      <AppInput label="Keluhan/Anamnesa <PERSON>lien*" type="textarea" rows={3} name="<PERSON>lu<PERSON>" onChange={() => {}} />
      <AppInput
        label="Penanganan/Intervensi*"
        type="textarea"
        rows={3}
        name="Penanganan"
        onChange={() => {}}
      />
      <div className="flex flex-col gap-1">
        <AppInput label="Catatan untuk Klien*" type="textarea" rows={3} name="Catatan" onChange={() => {}} />
        <span className="text-[12px] text-[#535353]">Catatan ini akan ditampilkan ke Klien</span>
      </div>
      <div className="flex flex-col gap-1">
        <AppInput label="Tugas yang Diberikan*" type="textarea" rows={3} name="Tugas" onChange={() => {}} />
        <span className="text-[12px] text-[#535353]">Tugas ini akan ditampilkan ke Klien</span>
      </div>
    </form>
  )
}
