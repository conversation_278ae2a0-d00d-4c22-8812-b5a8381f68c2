'use client'

import { twMerge } from 'tailwind-merge'
import ButtonPrimary from '../_common/ButtonPrimary'
import { DatePickerWithRange } from '../ui/DateRangePicker'
import { DateRange } from 'react-day-picker'

type HeaderContentProps = {
  title: string
  handleAdd?: () => void
  handleDatePicker?: (date: DateRange) => void
  className?: string
  date?: DateRange | undefined
}

export const HeaderContent = ({
  className,
  title,
  handleAdd,
  handleDatePicker,
  date,
}: HeaderContentProps) => {
  const labelButton = `Tambah ${title}`
  return (
    <div
      className={twMerge(
        `flex items-center justify-between flex-wrap xs:flex-col xs:items-start sm:flex-row sm:flex sm:items-center sm:justify-between mb-4 md:mb-6 gap-y-4 md:gap-x-4 ${className ?? ''}`
      )}
    >
      <span className="text-heading-sm md:text-heading-lg font-bold">{title}</span>
      <div className="flex items-center justify-between sm:justify-start gap-4 xs:w-full sm:w-auto">
        {handleDatePicker && (
          <DatePickerWithRange
            valueDate={date}
            formatDate={{ from: 'd LLL', to: 'd LLL' }}
            className="h-[42px] sm:h-[55px] xs:w-auto"
            onSelectDate={(date: DateRange) => handleDatePicker(date)}
          />
        )}
        {handleAdd && (
          <>
            <ButtonPrimary
              onClick={() => handleAdd()}
              variant="contained"
              size="lg"
              className="xs:w-auto sm:w-auto hidden sm:flex"
              classLabel="text-nowrap"
            >
              {labelButton}
            </ButtonPrimary>
            <ButtonPrimary
              onClick={() => handleAdd()}
              variant="contained"
              size="sm"
              className="xs:w-auto sm:w-auto sm:hidden"
              classLabel="text-nowrap"
            >
              {labelButton}
            </ButtonPrimary>
          </>
        )}
      </div>
    </div>
  )
}
