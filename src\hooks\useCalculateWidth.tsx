import { useState, useEffect } from 'react'

const useCalculateWidth = () => {
  const [width, setWidth] = useState<number>(0)

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const handleResize = () => setWidth(window?.innerWidth)
      window.addEventListener('resize', handleResize)
      return () => window.removeEventListener('resize', handleResize)
    }
  }, [])

  return width
}

export default useCalculateWidth
