'use client'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { cn } from '@/lib/utils'
import { getInitialName } from '@/utils/stringUtils'
import Image from 'next/image'
import React, { useEffect, useState } from 'react'

type AvatarInfoProps = {
  className?: string
  classImage?: string
  wrapClassName?: string
  image?: string | null
  heading?: string | React.ReactNode
  subHeading?: string | React.ReactNode
  name?: string
  orientation?: 'column' | 'row'
  children?: React.ReactNode
  alt?: string
}

export const stringAvatar = (name: string) => {
  const [firstName, lastName] = name.split(' ', 2)

  const initials =
    firstName && firstName.length > 0
      ? lastName && lastName.length > 0
        ? `${firstName[0]}${lastName[0]}`
        : `${firstName[0]}`
      : ''

  return initials
}

export const AvatarWithInfo = ({
  orientation = 'row',
  image = '',
  heading,
  subHeading,
  className,
  classImage,
  wrapClassName,
  children,
  alt,
}: AvatarInfoProps) => {
  const [imageDisplay, setImageDisplay] = useState<string | null>(image)

  useEffect(() => {
    setImageDisplay(image)
  }, [image])

  let wrapClasses = orientation === 'column' ? 'flex-col' : 'flex-row'
  return (
    <div className={cn(wrapClasses, 'flex gap-4', wrapClassName)}>
      <div className={cn('relative flex-none', className)}>
        {imageDisplay ? (
          <Image
            priority={true}
            fill
            className={cn('object-cover rounded-full', classImage)}
            src={imageDisplay}
            alt={alt ?? 'mental-healing'}
            onError={() => setImageDisplay('/mascot.svg')}
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          />
        ) : (
          <Avatar className="w-full h-full">
            <AvatarFallback>{getInitialName(alt ? String(alt) : 'Mental healing')}</AvatarFallback>
          </Avatar>
        )}
      </div>
      {children ? (
        children
      ) : (
        <div className="text-wrap flex flex-col justify-start">
          {heading}
          {subHeading}
        </div>
      )}
    </div>
  )
}
