import { useState } from 'react'
import moment from 'moment'
import useWeeklyCalendar from '@/hooks/useWeeklyCalendar'
import { AppBigCaption, AppBigText } from '../ui'
import { cn } from '@/lib/utils'
import { IIcons, SVGIcons } from '../icon'
import { AppModal } from '../Modal/AppModal'
import { Calendar } from '@/components/ui/calendar'
import { useToast } from '@/components/ui/use-toast'

type WeeklyCalendarPickerProps = {
  className?: string
  date: Date | undefined
  availableDates: Date[]
  onSelect?: (date: Date | undefined) => void
  modal?: boolean
  callbackToggle?: (arg: boolean) => void
}

export const WeeklyCalendarPickerCustom = ({
  className,
  date,
  availableDates,
  onSelect,
  callbackToggle,
}: WeeklyCalendarPickerProps) => {
  const { toast } = useToast()
  const [toggle, setToggle] = useState<boolean>(false)
  const [isCustomDate, setIsCustomDate] = useState<boolean>(false)
  const { currentDays } = useWeeklyCalendar(date, isCustomDate)

  const isEqual = (dateA: Date, dateB: Date) => {
    return (
      dateA.getDate() === dateB.getDate() &&
      dateA.getMonth() === dateB.getMonth() &&
      dateA.getFullYear() === dateB.getFullYear()
    )
  }
  const toggleCalendar = () => {
    setToggle((prevState) => !prevState)
    callbackToggle && callbackToggle(!toggle)
  }

  const onCustomDate = (date: Date | undefined) => {
    setToggle(false)
    callbackToggle && callbackToggle(false)
    onSelect && onSelect(date)
    setIsCustomDate(true)
  }
  return (
    <>
      <div className="flex items-center justify-start">
        <div className="flex flex-wrap gap-2 ">
          {currentDays.map((detail, index) => {
            const isAvailable = availableDates?.some((d) => moment(d).isSame(detail.isoDate, 'day')) ?? false
            const disableDate = !isAvailable

            return (
              <div
                key={index}
                className={cn('group flex flex-col items-center cursor-pointer w-[59px] h-[74px]', className)}
                style={{
                  pointerEvents: disableDate ? 'none' : 'auto',
                  opacity: disableDate ? 0.5 : 1,
                }}
                onClick={() => {
                  if (disableDate) return
                  setIsCustomDate(false)
                  onSelect && onSelect(detail.isoDate)
                }}
              >
                <div
                  className={cn(
                    'w-full text-center rounded-t-[8px] border text-gray-200 border-line-200 group-hover:border-main-100 group-hover:bg-main-100 group-hover:text-white px-2',
                    isEqual(date || new Date(), detail.isoDate) && 'border-main-100 bg-main-100 text-white'
                  )}
                >
                  <AppBigCaption>{detail.day}</AppBigCaption>
                </div>
                <div
                  className={cn(
                    'flex flex-col items-center justify-center w-full text-center border-x border-b text-gray-200 border-line-200 rounded-b-[8px] py-1 px-2 group-hover:border-main-100',
                    isEqual(date || new Date(), detail.isoDate) && 'border-main-100 text-gray-400'
                  )}
                >
                  <AppBigText bold>{detail.date}</AppBigText>
                  <AppBigCaption>{detail.month}</AppBigCaption>
                </div>
              </div>
            )
          })}
          <div
            key={'index-picker'}
            className={cn('flex flex-col items-center cursor-pointer h-[74px] w-[59px]')}
            onClick={toggleCalendar}
          >
            <div
              className={cn(
                'group flex flex-col h-full items-center justify-center w-full text-center border text-gray-200 border-line-200 rounded-[8px] py-1 px-2 hover:border-main-100 hover:bg-main-100 hover:text-white'
              )}
            >
              <AppBigText bold className="group-hover:text-white">
                <SVGIcons name={IIcons.Calendar} />
              </AppBigText>
              <AppBigCaption>{currentDays[6]?.month}</AppBigCaption>
            </div>
          </div>
        </div>
      </div>
      <AppModal
        open={toggle}
        onClose={toggleCalendar}
        title="Pilih spesifik tanggal"
        className="max-w-[360px]"
      >
        <Calendar
          mode="single"
          selected={date}
          onSelect={(val) => {
            if (!val || !availableDates) return

            const isAvailable = availableDates.some((d) => moment(d).isSame(val, 'day'))

            if (isAvailable) {
              onCustomDate(val)
            } else {
              toast({
                title: 'Tanggal tidak tersedia',
                description: 'Silakan pilih tanggal yang tersedia (warna merah)',
                variant: 'destructive',
              })
            }
          }}
          disabled={(date) => !availableDates.some((d) => moment(d).isSame(date, 'day'))}
          modifiers={{
            available: availableDates,
          }}
          modifiersClassNames={{
            available: 'text-gray-400',
            selected: 'bg-blue-500 text-white',
            disabled: 'bg-white text-gray-100',
            outside: 'bg-white text-gray-100',
          }}
          initialFocus
        />
      </AppModal>
    </>
  )
}
