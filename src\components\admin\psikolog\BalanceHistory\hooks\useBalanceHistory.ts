import { useState, useCallback, useEffect } from 'react'
import { useToast } from '@/components/ui/use-toast'
import { balanceService } from '@/services/balance.service'
import { ClientType } from '../column'

interface TransactionResponse {
  data: ClientType[]
  meta: {
    total: number
    perPage: number
    currentPage: number
    lastPage: number
  }
}

interface UseBalanceHistoryProps {
  id: string
  selectedTransactionType: string
  initialData?: TransactionResponse
}

export const useBalanceHistory = ({ id, selectedTransactionType, initialData }: UseBalanceHistoryProps) => {
  const [isLoading, setIsLoading] = useState(false)
  const [transactionData, setTransactionData] = useState<TransactionResponse>(
    initialData || {
      data: [],
      meta: {
        total: 0,
        perPage: 10,
        currentPage: 1,
        lastPage: 1,
      },
    }
  )
  const { toast } = useToast()

  const fetchHistoryTransaction = useCallback(
    async ({ pageSize = 10, pageIndex = 0 }) => {
      if (!id) return null
      setIsLoading(true)

      try {
        const types = selectedTransactionType === 'ALL' ? ['INCOME', 'OUTCOME'] : [selectedTransactionType]

        const response = await balanceService.getPsychologistBalanceHistoryById({
          id,
          types,
          page: pageIndex + 1,
          perPage: pageSize,
        })

        if (response?.data) {
          const formattedData: TransactionResponse = {
            data: response.data,
            meta: {
              total: response.meta?.total || 0,
              perPage: response.meta?.perPage || pageSize,
              currentPage: response.meta?.currentPage || 1,
              lastPage: response.meta?.lastPage || 1,
            },
          }

          setTransactionData(formattedData)
          return formattedData
        }

        return null
      } catch (error) {
        console.error('Error fetching transaction history:', error)
        toast({
          variant: 'destructive',
          title: 'Error',
          description: 'Failed to fetch transaction history',
        })
        return null
      } finally {
        setIsLoading(false)
      }
    },
    [id, selectedTransactionType, toast]
  )

  useEffect(() => {
    const debounceTimer = setTimeout(() => {
      fetchHistoryTransaction({ pageSize: 10, pageIndex: 0 })
    }, 500)

    return () => clearTimeout(debounceTimer)
  }, [fetchHistoryTransaction, selectedTransactionType])

  return {
    isLoading,
    transactionData,
    fetchHistoryTransaction,
  }
}
