'use client'
import React, { ReactNode } from 'react'
import { ReactElement, useState } from 'react'
import { sanitizeForId } from '@/utils/stringUtils'
import TabItem from './TabItem'
import { AppBigText } from '../ui'
import { twMerge } from 'tailwind-merge'

export type TabItemProps = {
  label: string
  children: ReactNode
}

export type TabListProps = {
  activeTabIndex: number
  className?: string
  onClickTabs?: (index: number) => void
  children: ReactElement<TabItemProps> | ReactElement<TabItemProps>[]
}

const TabList: React.FC<TabListProps> = ({ children, activeTabIndex = 0, className, onClickTabs }) => {
  const [activeTab, setActiveTab] = useState(activeTabIndex)

  const handleTabClick = (index: number) => {
    onClickTabs ? onClickTabs(index) : setActiveTab(index)
  }

  const tabs = React.Children.toArray(children).filter((child): child is ReactElement<TabItemProps> =>
    React.isValidElement(child)
  )

  const selectedTabs = onClickTabs ? activeTabIndex : activeTab

  return (
    <div className="flex flex-wrap">
      <ul
        className={twMerge(
          `flex w-full whitespace-nowrap overflow-y-hidden overflow-x-auto text-sm font-medium text-center text-gray-500 border-b border-line-200 dark:border-gray-700 dark:text-gray-400 h-14 gap-x-3 ${className ?? ''}`
        )}
      >
        {tabs.map((tab, index) => (
          <li key={index} className="me-2">
            <div className="w-full flex flex-col">
              <button
                aria-current="page"
                key={`tab-btn-${index}`}
                role="tab"
                id={`tab-${sanitizeForId(tab.props.label)}`}
                aria-controls={`panel-${sanitizeForId(tab.props.label)}`}
                aria-selected={selectedTabs === index}
                onClick={() => handleTabClick(index)}
                className={`${selectedTabs === index ? 'text-gray-400' : 'text-gray-200'} inline-block py-4 bg-transparent`}
              >
                <AppBigText bold>{tab.props.label}</AppBigText>
              </button>
              <span
                className={`${selectedTabs === index ? 'transform transition -translate-y-[.19rem] ease-in duration-100 border-t-4 border-gray-400 w-full h-2 rounded' : ''}`}
              >
                <span className="collapse text-[0px]">.</span>
              </span>
            </div>
          </li>
        ))}
      </ul>

      {tabs.map((tab, index) => {
        return (
          <TabItem key={tab.key} label={tab.props.label} isActive={selectedTabs === index}>
            {tab.props.children}
          </TabItem>
        )
      })}
    </div>
  )
}

export default TabList
