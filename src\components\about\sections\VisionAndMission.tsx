export const VisionAndMission = ({ visi, misi }: { visi: string; misi: string[] }) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 px-4">
      <div className="flex flex-col gap-y-3 font-bold">
        <span className="text-subheading-md md:text-[38px] md:leading-[42px] text-gray-400 text-center md:text-left">
          Visi
        </span>
        <span className="text-heading-sm md:text-[38px] md:leading-[42px] text-main-100">{visi}</span>
      </div>
      <div className="flex flex-col gap-y-3">
        <span className="text-subheading-md md:text-[38px] md:leading-[42px] text-gray-400 text-center md:text-left font-bold">
          Misi
        </span>
        <ul className="flex flex-col gap-y-6 list-disc px-6">
          {misi.map((item) => {
            return (
              <li key={item} className="text-body-lg md:text-subheading-md font-medium text-gray-300">
                {item}
              </li>
            )
          })}
        </ul>
      </div>
    </div>
  )
}
