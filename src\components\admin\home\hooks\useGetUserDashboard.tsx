import { useToast } from '@/components/ui/use-toast'
import { counsellingService } from '@/services/counselling.service'
import { dashboardService } from '@/services/dashboard.service'
import { useQuery } from '@tanstack/react-query'

export const useGetUserDashboard = () => {
  const { toast } = useToast()
  return useQuery({
    queryKey: ['CounselingDetails'],
    queryFn: () =>
      dashboardService
        .adminGetUserCounter()
        .then((response) => response)
        .catch((error) => {
          console.log(error)
        }),
  })
}
