import { IIcons } from '@/components/_common/icon'
import { Routes } from './routes'

export type MenuItemProps = {
  id?: string
  icon: IIcons
  label: string
  route: string
  showInMobile?: boolean
  showInDesktop?: boolean
  labelMobile?: string
  subMenu?: MenuItemProps[]
}

export const SuperAdminMenuItems: MenuItemProps[] = [
  {
    id: '1',
    icon: IIcons.Home,
    label: 'Home',
    route: Routes.AdminHome,
    showInMobile: true,
    showInDesktop: true,
  },
  {
    id: '2',
    icon: IIcons.Time,
    label: 'Jadwal',
    route: Routes.AdminCounselling,
    showInMobile: true,
    showInDesktop: true,
  },
  {
    id: '3',
    icon: IIcons.Report,
    label: 'Klien Report',
    route: Routes.AdminClientReport,
    labelMobile: 'Report',
    showInMobile: true,
    showInDesktop: true,
  },
  {
    id: '4',
    icon: IIcons.Testimoni,
    label: 'Testimoni',
    route: Routes.AdminTestimony,
    showInMobile: true,
    showInDesktop: true,
  },
  {
    id: '5',
    icon: IIcons.User,
    label: 'User',
    route: Routes.AdminUser,
    showInMobile: true,
    showInDesktop: false,
  },
  {
    id: '6',
    icon: IIcons.User,
    label: 'User',
    route: '#',
    showInMobile: false,
    showInDesktop: true,
    subMenu: [
      {
        icon: IIcons.User,
        label: 'Klien',
        route: Routes.AdminClient,
        showInMobile: true,
        showInDesktop: true,
      },
      {
        icon: IIcons.User,
        label: 'Psikolog',
        route: Routes.AdminPsychologist,
        showInMobile: true,
        showInDesktop: true,
      },
      {
        icon: IIcons.User,
        label: 'Admin',
        route: Routes.AdminUser,
        showInMobile: true,
        showInDesktop: true,
      },
    ],
  },
]
export const AdminMenuItems: MenuItemProps[] = [
  {
    id: '1',
    icon: IIcons.Home,
    label: 'Home',
    route: Routes.AdminHome,
    showInMobile: true,
    showInDesktop: true,
  },
  {
    id: '2',
    icon: IIcons.Time,
    label: 'Jadwal',
    route: Routes.AdminCounselling,
    showInMobile: true,
    showInDesktop: true,
  },
  {
    id: '3',
    icon: IIcons.Report,
    label: 'Klien Report',
    route: Routes.AdminClientReport,
    labelMobile: 'Report',
    showInMobile: true,
    showInDesktop: true,
  },
  {
    id: '4',
    icon: IIcons.Testimoni,
    label: 'Testimoni',
    route: Routes.AdminTestimony,
    showInMobile: true,
    showInDesktop: true,
  },
  {
    id: '5',
    icon: IIcons.User,
    label: 'User',
    route: Routes.AdminUser,
    showInMobile: true,
    showInDesktop: false,
  },
  {
    id: '6',
    icon: IIcons.User,
    label: 'User',
    route: '#',
    showInMobile: false,
    showInDesktop: true,
    subMenu: [
      {
        icon: IIcons.User,
        label: 'Psikolog',
        route: Routes.AdminPsychologist,
        showInMobile: true,
        showInDesktop: true,
      },
    ],
  },
]

export const PsikologMenuItems: MenuItemProps[] = [
  {
    icon: IIcons.Home,
    label: 'Home',
    route: Routes.PsychologistHome,
    showInMobile: true,
    showInDesktop: true,
  },
  {
    icon: IIcons.Time,
    label: 'Jadwal',
    route: Routes.PsychologistCounseling,
    showInMobile: true,
    showInDesktop: true,
  },
  {
    icon: IIcons.Calendar,
    label: 'Ketersediaan',
    route: Routes.PsychologistAvaibility,
    showInMobile: true,
    showInDesktop: true,
  },
  // {
  //   icon: IIcons.Money,
  //   label: 'Pendapatan',
  //   route: Routes.PsychologistIncome,
  //   showInMobile: true,
  //   showInDesktop: true,
  // },
  {
    icon: IIcons.Report,
    label: 'Klien Report',
    route: Routes.PsychologistClientReport,
    labelMobile: 'Report',
    showInMobile: true,
    showInDesktop: true,
  },
  {
    icon: IIcons.Testimoni,
    label: 'Testimoni',
    route: Routes.PsychologistTestimony,
    showInMobile: false,
    showInDesktop: true,
  },
  {
    icon: IIcons.User,
    label: 'User',
    route: Routes.PsychologistSetting,
    showInMobile: false,
    showInDesktop: false,
  },
]

export const UserMenuItems: MenuItemProps[] = [
  {
    icon: IIcons.Home,
    label: 'Home',
    route: '/home',
  },
  {
    icon: IIcons.Time,
    label: 'Konseling',
    route: '/psikolog/konseling',
  },
  {
    icon: IIcons.CounselingGroup,
    label: 'Klien',
    route: '/admin/psikolog',
  },
  {
    icon: IIcons.Notes,
    label: 'Psikolog',
    route: '/catatan',
  },
  {
    icon: IIcons.Testimoni,
    label: 'Admin',
    route: '/testimoni',
  },
]
