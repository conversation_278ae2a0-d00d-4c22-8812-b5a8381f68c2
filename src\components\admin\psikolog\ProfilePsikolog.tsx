'use client'

import { LabelValue } from '@/components/_common/CardInfo/LabelValue'
import { ShowMore } from '@/components/_common/ShowMore/ShowMore'
import { AppBigText, Card, H4 } from '@/components/_common/ui'
import { VideoPsikolog } from './VideoPsikolog'
import { PsychologistProfile } from '@/interfaces/profile-service'
import { EducationHistoryView } from '@/components/psikolog/setting/PsychologistProfile/EducationHistoryView'
import moment from 'moment-timezone'
import { MOMENT_INPUT_DATE_FORMAT } from '@/constans/date'
import InputFile from '@/components/_common/InputFile/Index'
import { useToast } from '@/components/ui/use-toast'
import { useEffect, useRef, useState } from 'react'
import { NoDataFound } from '@/components/_common/NoData/NoDataFound'

function getAge(dateString: string, timezone: string) {
  const isoDate = moment(dateString).toISOString()
  const date = moment.tz(isoDate, MOMENT_INPUT_DATE_FORMAT, timezone)
  const years = moment().diff(date, 'years')
  const month = moment().diff(date.add(years, 'years'), 'months', false)
  const days = moment().diff(date.add(month, 'months'), 'days', false)
  return { years, month, days, date }
}

const BirthdateView = ({ date, timezone }: { date: string; timezone: string }) => {
  const age = getAge(date, timezone)
  const formatDate = moment(date).format('DD MMMM YYYY')
  const ageOfPsychologist = `(${age.years} Tahun ${age.month} Bulan ${age.days} Hari)`
  return (
    <div className="flex items-center gap-1">
      <span>{formatDate}</span>
      <span className="text-[#737373]">{ageOfPsychologist}</span>
    </div>
  )
}

export const ProfilePsikolog = ({
  fullName,
  nickname,
  userIdentity,
  educationHistory,
  bio,
  maritalStatus,
  birthDate,
  gender,
  birthOrder,
  ethnicity,
  religion,
  service,
  offlineLocation,
  bankAccount,
  video,
  youtubeVideo,
  occupation,
}: PsychologistProfile) => {
  const videoRef = useRef(null)
  const { toast } = useToast()
  const [linkVideo, setLinkVideo] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const history = educationHistory ?? []
  const childTo = birthOrder ? birthOrder.split('/')[0] : ''
  const totalSibling = birthOrder ? birthOrder.split('/')[1] : ''
  const birthOrderView = `${childTo} dari ${totalSibling} bersaudara`
  const accountView = (bankAccount ?? [])[0]
    ? `${(bankAccount ?? [])[0]?.bankName} ${(bankAccount ?? [])[0]?.bankAccount} - ${(bankAccount ?? [])[0]?.bankAccountName}`
    : '-'

  useEffect(() => {
    setLinkVideo(youtubeVideo ?? [])
  }, [youtubeVideo])

  const handleUpdateVideo = async (file: any) => {
    try {
      const formData = new FormData()
      formData.append('video', file)
      // await profileService.updatePsychologistProfile(formData)
      toast({
        variant: 'success',
        title: 'Perbaharui video berhasil',
      })
    } catch (error) {
      toast({
        variant: 'danger',
        title: 'Perbaharui video gagal',
      })
    }
  }

  const handleSubmit = async (newLink: string, index: number) => {
    setIsLoading(true)
    if (!newLink) {
      toast({
        variant: 'danger',
        title: 'Lengkapi link video dengan data yang valid',
      })
      setIsLoading(false)
      return false
    }
    try {
      const formData = new FormData()
      const linkVideoUpdate = [...linkVideo].map((linkItem, id) => {
        if (id === index) {
          return { ...linkItem, link: newLink }
        }
        return linkItem
      })
      const link = linkVideoUpdate.filter((val) => !!val.link)
      const payload =
        link.length > 0
          ? link.map((val) => {
              return {
                link: val.link,
                title: val.link,
                description: val.link,
              }
            })
          : []
      formData.append('youtubeVideos', JSON.stringify(payload))
      // await profileService.updatePsychologistProfile(formData)
      toast({
        variant: 'success',
        title: 'Perbaharui data youtube berhasil',
      })
      setIsLoading(false)
    } catch (error) {
      setIsLoading(false)
      toast({
        variant: 'danger',
        title: 'Perbaharui data youtube gagal',
      })
    }
  }
  return (
    <div className="grid grid-flow-cols-dense grid-cols-1 xs:grid-cols-1 lg:grid-cols-2 grid-rows-1 gap-4 items-start">
      <Card className="xs:p-3 sm:p-4 md:p-4 lg:p-4 xl:p-4 grid gap-4">
        <H4 bold>Informasi</H4>
        <div className="grid grid-flow-row-dense grid-cols-3 grid-rows-1 gap-4 ">
          <LabelValue label="Nama Pangilan" value={nickname ?? '-'} />
          <LabelValue
            label="Email"
            value={<span className="text-main-100">{userIdentity?.email ?? '-'}</span>}
          />
          <LabelValue label="Pendidikan" value={<EducationHistoryView history={history} />} />
          <LabelValue label="Bio" value={<ShowMore id="bio" text={bio ?? '-'} />} />
          <LabelValue label="Status Menikah" value={maritalStatus ?? '-'} />
          <LabelValue
            label="Tanggal Lahir"
            value={<BirthdateView date={birthDate} timezone={userIdentity?.userConfig?.TIMEZONE ?? ''} />}
          />
          <LabelValue label="Jenis Kelamin" value={gender} />
          <LabelValue label="Urutan Bersaudara" value={birthOrder ? birthOrderView : '-'} />
          <LabelValue label="Suku Bangsa" value={ethnicity ?? '-'} />
          <LabelValue label="Agama" value={religion ?? '-'} />
          <LabelValue label="Layanan" value={service ?? '-'} />
          <LabelValue label="Lokasi Praktik" value={offlineLocation ?? 'Tidak tersedia'} />
          <LabelValue label="No. Rekening" value={accountView} />
          <LabelValue label="Join Date" value="1 Januari 2023" />
          <LabelValue label="End Date" value="Masih menjadi psikolog aktif" />
        </div>
      </Card>
      <Card className="xs:p-3 sm:p-4 md:p-4 lg:p-4 xl:p-4 grid gap-4 overflow-hidden">
        <H4 bold>Media</H4>
        <AppBigText bold>Video Profil</AppBigText>
        <InputFile
          accept={'video/mp4'}
          onChange={(file) => handleUpdateVideo(file)}
          previewVideo={video}
          inputRef={videoRef}
          maxFileSizeMb={10}
        />
        <div className="grid grid-cols-3 grid-rows-1 gap-4 "></div>
        <AppBigText bold>Video Youtube</AppBigText>
        {youtubeVideo?.length ? (
          youtubeVideo.map((item, index) => {
            return (
              <VideoPsikolog
                key={item.id}
                isEdit={true}
                value={item.link}
                onSubmit={(val) => handleSubmit(val ?? '', index)}
                onEdit={() => console.log()}
                isLoading={isLoading}
              />
            )
          })
        ) : (
          <NoDataFound />
        )}
        {/* <VideoPsikolog link="https://www.youtube.com/watch?v=Q89Dzox4jAE" />
        <VideoPsikolog link="https://www.youtube.com/watch?v=Q89Dzox4jAE" /> */}
      </Card>
    </div>
  )
}
