import Image from 'next/image'
import Link from 'next/link'

type ServicesProps = {
  title: string
  content: any[]
}

export const Services = ({ title, content }: ServicesProps) => {
  return (
    <div className="flex flex-col items-center gap-y-15">
      <span className="text-subheading-md md:text-[38px] md:leading-[42px] font-bold text-gray-400 text-center">
        {title}
      </span>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-y-[70px] gap-x-10">
        {content.length &&
          content.map((item) => {
            return (
              <div key={item.id} className="flex flex-col items-center gap-y-4">
                <Image src={item.image} alt={'logo-' + item.id} height={128} width={128} />
                <span className="text-heading-sm font-bold text-gray-400 text-center">{item.heading}</span>
                <span className="text-body-lg font-medium text-gray-300 text-center">{item.content}</span>
              </div>
            )
          })}
        <div
          key={'not-found'}
          className="flex flex-col items-center justify-center gap-y-4 rounded-lg bg-gradient-to-b from-[#0190D4] to-50% to-[#30B2EF] p-6"
        >
          <span className="text-heading-sm font-bold text-white text-center">
            Belum menemukan yang Anda cari?
          </span>
          <span className="text-body-lg font-medium text-white text-center">
            Kami siap membantu{' '}
            <Link
              href={
                'https://api.whatsapp.com/send/?phone=6285173025865&text=Hi+Mental+Healing%2C+%28isi+pesan+kamu+disini%29&type=phone_number&app_absent=0'
              }
              className="underline text-white"
              target="_blank"
            >
              Hubungi kami
            </Link>{' '}
          </span>
        </div>
      </div>
    </div>
  )
}
