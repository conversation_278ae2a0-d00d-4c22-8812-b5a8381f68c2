import { profileService } from '@/services/profile.service'
import { useQuery } from '@tanstack/react-query'

export const useGetPageInformation = (id: string) => {
  return useQuery({
    queryKey: ['PageInformation', { id }],
    queryFn: () => {
      if (id) {
        return profileService
          .getPageInformationById(id)
          .then((response) => response)
          .catch(() => {
            return null
          })
      }
    },
  })
}
