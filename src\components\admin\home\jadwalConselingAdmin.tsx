import { IIcons, SVGIcons } from '@/components/_common/icon'
import BlueCalender from '../../../../public/images/blueCalender.svg'
import BlueCalenderMobile from '../../../../public/images/blueCalenderMobile.svg'

export default function JadwalConselingPsikolog() {
  return (
    <>
      <div className="flex items-center gap-[15px]">
        {/* img icon calendar */}
        <div className="w-[46px] h-[46px] md:w-[56px] md:h-[56px] rounded-[15px] flex items-center justify-center bg-[#E7F7FF] p-4 relative">
          <BlueCalender className="hidden md:inline-block" />
          <BlueCalenderMobile className="md:hidden inline-block absolute" />
        </div>
        {/* isi */}
        <div className="flex flex-col gap-[6px]">
          <p className="font-bold text-[14px] text-[#535353]">
            Anda belum memiliki jadwal. Bagikan profil Anda di sosial media untuk mendapatkan klien.
          </p>
          <a href="" className="text-[#039EE9] text-[14px] font-bold">
            Bagikan profil Anda
          </a>
        </div>
      </div>
    </>
  )
}
