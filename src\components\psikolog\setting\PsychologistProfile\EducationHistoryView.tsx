import { EducationHistoryProps } from '@/components/_common/EducationInput/EducationInput'
import moment from 'moment'

export const EducationHistoryView = ({ history }: { history: EducationHistoryProps[] }) => {
  return (
    <div className="flex flex-col py-1">
      {history.map((val, id) => {
        return (
          <span key={id} className="text-gray-400 font-normal text-body-lg">
            {`${val.level} ${val.university} `}
            <span className="text-gray-200">{`(Lulus ${moment(val.graduationYear).format('YYYY')})`}</span>
          </span>
        )
      })}
    </div>
  )
}
