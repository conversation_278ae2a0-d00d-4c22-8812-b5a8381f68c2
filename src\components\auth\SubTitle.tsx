type TitleProps = {
  heading: string
  subHeading?: string
  center?: boolean
}

const SubTitle = ({ heading, subHeading, center }: TitleProps) => {
  return (
    <div className={`flex flex-col space-y-[6px] ${center && 'items-center'}`}>
      <span className="text-subheading-md font-medium text-gray-400">{heading}</span>
      {subHeading && <span className="text-caption-md font-medium text-gray-200">{subHeading}</span>}
    </div>
  )
}

export default SubTitle
