/* eslint-disable @next/next/no-img-element */
'use client'

import { useEffect, useState } from 'react'
import { IIcons } from '../icon'
import VideoIcon from '@/assets/icons/video.svg'
import ButtonPrimary from '../ButtonPrimary'

export type InputFileProps = {
  accept: string
  onChange: (e: React.ChangeEvent<HTMLInputElement> | null) => void
  inputRef: any
  className?: string
  icon?: IIcons
  maxFileSizeMb?: number
  previewVideo?: string | ArrayBuffer | null | undefined
}

function InputFile({
  onChange,
  icon,
  accept,
  inputRef,
  className = '',
  maxFileSizeMb = 1,
  previewVideo,
}: InputFileProps) {
  const [error, setError] = useState<string>('')

  const [fileName, setFileName] = useState<string | null>(null)
  const [imagePreview, setImagePreview] = useState<string | ArrayBuffer | null | undefined>(null)
  const [videoPreview, setVideoPreview] = useState<string | ArrayBuffer | null | undefined>(previewVideo)

  useEffect(() => {
    setVideoPreview(previewVideo)
  }, [previewVideo])

  function previewFile(e: any) {
    const reader = new FileReader()

    const selectedFile = e.target.files[0]
    if (selectedFile) {
      if (maxFileSizeMb && selectedFile.size > maxFileSizeMb * 1e6) {
        setError(`Upload failed. File melebihi kapasitas maksimal (${maxFileSizeMb}MB).`)
        return
      } else {
        setError('')
        reader.readAsDataURL(selectedFile)
      }
    }

    reader.onload = (readerEvent) => {
      if (selectedFile.type.includes('image')) {
        setImagePreview(readerEvent.target?.result || null)
      } else if (selectedFile.type.includes('video')) {
        setVideoPreview(readerEvent.target?.result || null)
        onChange && onChange(selectedFile)
      }
      setFileName(selectedFile.name || null)
    }
  }

  function clearFiles() {
    setError('')
    setFileName(null)
    setImagePreview(null)
    setVideoPreview(null)
    onChange && onChange(null)
  }

  return (
    <div className="flex flex-col xs:flex-col md:flex-row items-start xs:items-start md:items-center gap-4">
      <label
        className={`cursor-pointer flex items-center justify-center w-full md:w-auto ${
          icon ? undefined : 'bg-grey-default'
        } bg-opacity-50 ${className}`}
      >
        <div className="w-full md:w-auto">
          {imagePreview != null && <img src={imagePreview as unknown as any} alt="img-thumb" />}
          {videoPreview != null && (
            <video
              className="md:w-[240px] md:h-[135px] w-full xs:w-full rounded-2xl bg-gray-100"
              controls
              src={videoPreview as unknown as any}
            ></video>
          )}
          {!imagePreview && !videoPreview && (
            <div className="flex flex-col items-center justify-center border border-line-200 rounded-2xl md:w-[240px] h-[135px] w-full xs:w-full">
              <VideoIcon />
              <span>Pilih Video</span>
            </div>
          )}
        </div>
        <input
          type="file"
          accept={accept}
          className="absolute w-0 h-0"
          onChange={previewFile}
          ref={inputRef}
        />
      </label>
      <div className="flex flex-col items-start gap-2">
        {fileName && <span className="w-full text-caption-md font-bold text-gray-400">{fileName}</span>}
        <span className="w-full text-caption-md font-medium text-gray-400">
          Format video dalam .mp4. Dari maksimal 1 menit. Maksimal memory {maxFileSizeMb}MB.
        </span>
        {fileName ? (
          <div className="flex gap-2">
            <ButtonPrimary variant="outlined" size="xs" onClick={() => inputRef.current.click()}>
              Ubah Video
            </ButtonPrimary>
            <ButtonPrimary variant="outlined" size="xs" color="gray" onClick={() => clearFiles()}>
              Hapus
            </ButtonPrimary>
          </div>
        ) : (
          <ButtonPrimary variant="outlined" size="xs" onClick={() => inputRef.current.click()}>
            Pilih Video
          </ButtonPrimary>
        )}
        {error ? <span className="text-red-500 text-xs">{error}</span> : null}
      </div>
    </div>
  )
}

export default InputFile
