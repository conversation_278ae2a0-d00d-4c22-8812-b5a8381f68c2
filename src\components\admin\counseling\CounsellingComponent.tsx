'use client'
import TabItem from '@/components/_common/tabs/TabItem'
import TabList from '@/components/_common/tabs/TabList'
import { Card } from '@/components/_common/ui'
import { ListingCounselling } from './ListingCounselling'
import { columns, columnsStatus } from './columns'
import { ColumnDef } from '@tanstack/react-table'
import { HeaderContent } from '../HeaderContent'
import { DateRange } from 'react-day-picker'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { ADMIN_COUNSELING_API, ADMIN_COUNSELING_PER_PSYCHOLOGIST_API } from '@/constans/API_PATH'
import {
  CancelledStatus,
  CompletedStatus,
  CounsellingTab,
  InProgressStatusForAdmin,
  UpcomingStatusForAdmin,
  WaitConfirmationsStatusForAdmin,
  WaitPaymentStatusForAdmin,
} from '@/constans/StaticOptions'
import moment from 'moment-timezone'
import { dispatch, useSelector } from '@/store'
import { setFilterDate } from '@/store/admin/counseling.reducer'
import { MOMENT_INPUT_DATE_FORMAT } from '@/constans/date'

export const CounsellingComponent = ({ id }: { id?: string }) => {
  const API = ADMIN_COUNSELING_API
  const refRefetchNow = useRef<any>()
  const refRefetchUpcoming = useRef<any>()
  const refRefetchWaitingConfirmations = useRef<any>()
  const refRefetchWaitingPayments = useRef<any>()
  const refRefetchCompleted = useRef<any>()
  const refRefetchCancelled = useRef<any>()
  const refRefetchAll = useRef<any>()
  const { counseling } = useSelector((state: any) => state.Meta)
  const { filterDate } = useSelector((state) => state.AdminCounseling)
  const [pageFilter, setPageFilter] = useState<any[]>([])
  const [activeTabIndex, setActiveTabIndex] = useState<number>(0)
  const [isSorted, setIsSorted] = useState<boolean>(false)
  const meta = counseling.meta
  const [search, setSearch] = useState<string | null>(null)
  const [method, setMethod] = useState<string | null>(null)
  const [status, setStatus] = useState<string | null>(null)

  let GeneratedAPI = API

  const dateRange = filterDate
    ? {
        from: filterDate.from ? moment(filterDate.from).toDate() : undefined,
        to: filterDate.to ? moment(filterDate.to).toDate() : undefined,
      }
    : { from: undefined, to: undefined }

  useEffect(() => {
    activeTabIndex === 0 && refRefetchNow.current && refRefetchNow.current()
    activeTabIndex === 1 && refRefetchUpcoming.current && refRefetchUpcoming.current()
    activeTabIndex === 2 && refRefetchWaitingConfirmations.current && refRefetchWaitingConfirmations.current()
    activeTabIndex === 3 && refRefetchWaitingPayments.current && refRefetchWaitingPayments.current()
    activeTabIndex === 4 && refRefetchCompleted.current && refRefetchCompleted.current()
    activeTabIndex === 5 && refRefetchCancelled.current && refRefetchCancelled.current()
    activeTabIndex === 6 && refRefetchAll.current && refRefetchAll.current()
  }, [activeTabIndex])

  const generateUrl = () => {
    const url = new URL('http://localhost')
    const dataBulk: any = {
      startDate: dateRange.from && dateRange.to ? moment(dateRange.from).utc(true).toISOString() : null,
      endDate: dateRange.from && dateRange.to ? moment(dateRange.to).utc(true).toISOString() : null,
      search: search ? String(search) : null,
      method: method ? String(method) : null,
      status: status ? String(status) : null,
      psychologistId: id ? String(id) : null,
    }
    Object.keys(dataBulk)
      .filter((key: string) => dataBulk[key] !== null)
      .forEach((key) => url.searchParams.append(key, dataBulk[key]))

    return decodeURIComponent(url.searchParams.toString())
  }

  const CounsellingData = [
    {
      id: 0,
      label: `Sedang Berlangsung (${meta?.additionalMeta?.inprogress ?? 0})`,
      content: (
        <ListingCounselling
          bulkFilter={generateUrl()}
          pageFilter={[{ key: 'status', operator: 'in', value: InProgressStatusForAdmin }]}
          refRefetch={refRefetchNow}
          fetchPath={GeneratedAPI.replace('[tab]', CounsellingTab.INPROGRESS)}
          showSecondFilter
          actions={['start', 'cancel', 'contactPsychologist', 'contactClient']}
          rangeDate={dateRange}
          columns={columns as unknown as ColumnDef<any, any>}
          onSearchChange={(val) => setSearch(val)}
          onFilterChange={(val) => setMethod(val)}
          onStatusChange={(val) => setStatus(val)}
        />
      ),
    },
    {
      id: 1,
      label: `Akan Datang (${meta?.additionalMeta?.upcoming ?? 0})`,
      content: (
        <ListingCounselling
          bulkFilter={generateUrl()}
          pageFilter={[{ key: 'status', operator: 'in', value: UpcomingStatusForAdmin }]}
          refRefetch={refRefetchUpcoming}
          fetchPath={GeneratedAPI.replace('[tab]', CounsellingTab.UPCOMING)}
          showSecondFilter
          actions={['start', 'reschedule', 'contactPsychologist', 'contactClient']}
          rangeDate={dateRange}
          columns={columnsStatus as unknown as ColumnDef<any, any>}
          onSearchChange={(val) => setSearch(val)}
          onFilterChange={(val) => setMethod(val)}
          onStatusChange={(val) => setStatus(val)}
        />
      ),
    },
    {
      id: 2,
      label: `Menunggu Konfirmasi (${meta?.additionalMeta?.waiting ?? 0})`,
      content: (
        <ListingCounselling
          bulkFilter={generateUrl()}
          pageFilter={[{ key: 'status', operator: 'in', value: WaitConfirmationsStatusForAdmin }]}
          refRefetch={refRefetchWaitingConfirmations}
          fetchPath={GeneratedAPI.replace('[tab]', CounsellingTab.WAITING)}
          showSecondFilter
          actions={['approve', 'reschedule', 'decline', 'contactPsychologist', 'contactClient']}
          rangeDate={dateRange}
          columns={columnsStatus as unknown as ColumnDef<any, any>}
          onSearchChange={(val) => setSearch(val)}
          onFilterChange={(val) => setMethod(val)}
          onStatusChange={(val) => setStatus(val)}
        />
      ),
    },
    {
      id: 3,
      label: `Menunggu Pembayaran (${meta?.additionalMeta?.waitingPayment ?? 0})`,
      content: (
        <ListingCounselling
          bulkFilter={generateUrl()}
          pageFilter={[{ key: 'status', operator: 'in', value: WaitPaymentStatusForAdmin }]}
          refRefetch={refRefetchWaitingPayments}
          fetchPath={GeneratedAPI.replace('[tab]', CounsellingTab.WAITING_PAYMENT)}
          actions={['contactClient']}
          rangeDate={dateRange}
          columns={columns as unknown as ColumnDef<any, any>}
          onSearchChange={(val) => setSearch(val)}
          onFilterChange={(val) => setMethod(val)}
          onStatusChange={(val) => setStatus(val)}
        />
      ),
    },
    {
      id: 4,
      label: `Selesai (${meta?.additionalMeta?.completed ?? 0})`,
      content: (
        <ListingCounselling
          bulkFilter={generateUrl()}
          pageFilter={[{ key: 'status', operator: 'in', value: CompletedStatus }]}
          refRefetch={refRefetchCompleted}
          fetchPath={GeneratedAPI.replace('[tab]', CounsellingTab.COMPLETED)}
          actions={['newSchedule', 'contactClient']}
          rangeDate={dateRange}
          columns={columns as unknown as ColumnDef<any, any>}
          onSearchChange={(val) => setSearch(val)}
          onFilterChange={(val) => setMethod(val)}
          onStatusChange={(val) => setStatus(val)}
        />
      ),
    },
    {
      id: 5,
      label: `Batal (${meta?.additionalMeta?.cancelled ?? 0})`,
      content: (
        <ListingCounselling
          bulkFilter={generateUrl()}
          pageFilter={[{ key: 'status', operator: 'in', value: CancelledStatus }]}
          refRefetch={refRefetchCancelled}
          fetchPath={GeneratedAPI.replace('[tab]', CounsellingTab.CANCELLED)}
          actions={['contactClient']}
          rangeDate={dateRange}
          columns={columns as unknown as ColumnDef<any, any>}
          onSearchChange={(val) => setSearch(val)}
          onFilterChange={(val) => setMethod(val)}
          onStatusChange={(val) => setStatus(val)}
        />
      ),
    },
    {
      id: 6,
      label: `Semua (${meta?.additionalMeta?.all ?? 0})`,
      content: (
        <ListingCounselling
          bulkFilter={generateUrl()}
          pageFilter={[]}
          refRefetch={refRefetchAll}
          fetchPath={GeneratedAPI.replace('[tab]', CounsellingTab.ALL)}
          actions={['start', 'reschedule', 'contactPsychologist', 'contactClient']}
          rangeDate={dateRange}
          columns={columnsStatus as unknown as ColumnDef<any, any>}
          onSearchChange={(val) => setSearch(val)}
          onFilterChange={(val) => setMethod(val)}
          onStatusChange={(val) => setStatus(val)}
        />
      ),
    },
  ]

  useEffect(() => {
    if (meta?.additionalMeta && !isSorted) {
      checkMeta()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [meta])

  useEffect(() => {
    setIsSorted(false)
  }, [filterDate])

  const checkMeta = () => {
    const sortOfTabs = [
      meta?.additionalMeta?.inprogress,
      meta?.additionalMeta?.upcoming,
      meta?.additionalMeta?.waiting,
      meta?.additionalMeta?.waitingPayment,
      meta?.additionalMeta?.completed,
    ]
    sortOfTabs.some((counterRow: number, index: number) => {
      if (counterRow > 0) {
        const refetchRef =
          index === 0
            ? refRefetchNow
            : index === 1
              ? refRefetchUpcoming
              : index === 2
                ? refRefetchWaitingConfirmations
                : index === 3
                  ? refRefetchWaitingPayments
                  : refRefetchCompleted
        setIsSorted(true)
        setActiveTabIndex(index)
        refetchRef.current && refetchRef.current()
        return true
      } else if (index === sortOfTabs.length - 1) {
        setIsSorted(true)
        setActiveTabIndex(index)
        refRefetchCompleted.current && refRefetchCompleted.current()
        return true
      } else {
        return false
      }
    })
  }

  const handleChangeDate = useCallback((date: DateRange) => {
    const payload = {
      from: date.from ? moment(date.from).format(MOMENT_INPUT_DATE_FORMAT) : undefined,
      to: date.to ? moment(date.to).format(MOMENT_INPUT_DATE_FORMAT) : undefined,
    }
    dispatch(setFilterDate(payload))
  }, [])

  return (
    <>
      <HeaderContent
        title="Konseling"
        date={dateRange}
        handleDatePicker={(date) => {
          handleChangeDate(date)
        }}
      />
      <Card className="border-0 p-0 xs:p-0 sm:border sm:p-6">
        <TabList
          className="sticky top-navbar z-30 bg-white"
          onClickTabs={(index) => {
            setActiveTabIndex(index)
          }}
          activeTabIndex={activeTabIndex}
        >
          {CounsellingData.map((counselling, index) => {
            return (
              <TabItem key={index} label={counselling.label}>
                {activeTabIndex === counselling.id ? counselling.content : null}
              </TabItem>
            )
          })}
        </TabList>
      </Card>
    </>
  )
}
