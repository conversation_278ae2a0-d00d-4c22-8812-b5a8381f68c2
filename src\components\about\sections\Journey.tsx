import Image from 'next/image'

export const Journey = ({ image, label, content }: { image: string; label: string; content: string }) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 px-4">
      <div className="flex h-full justify-center items-center">
        <div className="relative h-[362px] w-full flex justify-center items-center">
          <Image
            src={image}
            fill
            alt="logo-mh"
            style={{
              objectFit: 'scale-down',
              objectPosition: 'center',
            }}
          />
        </div>
      </div>
      <div className="flex flex-col gap-y-3">
        <span className="text-subheading-md md:text-[38px] md:leading-[42px] font-bold text-gray-400 text-center md:text-left">
          {label}
        </span>
        <span
          className="text-body-sm md:text-subheading-md font-medium text-gray-300 flex flex-col gap-y-4"
          dangerouslySetInnerHTML={{ __html: content }}
        ></span>
      </div>
    </div>
  )
}
