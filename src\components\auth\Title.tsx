type TitleProps = {
  title: string
  subTitle?: string
  center?: boolean
}

const Title = ({ title, subTitle, center }: TitleProps) => {
  return (
    <div className={`flex flex-col space-y-2 ${center && 'items-center'}`}>
      <span className="text-heading-md font-bold text-gray-400 text-center">{title}</span>
      <span className="text-body-sm font-medium text-gray-300 text-center">{subTitle}</span>
    </div>
  )
}

export default Title
