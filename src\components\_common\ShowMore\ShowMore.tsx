import { useState } from 'react'

interface ShowMoreProps {
  id: string
  text: string
  amountOfWords?: number
  className?: string
}

export const ShowMore = ({ id, text, className, amountOfWords = 36 }: ShowMoreProps) => {
  const [isExpanded, setIsExpanded] = useState(false)
  const splittedText = text.split(' ')
  const itCanOverflow = splittedText.length > amountOfWords
  const beginText = itCanOverflow ? splittedText.slice(0, amountOfWords - 1).join(' ') : text
  const endText = splittedText.slice(amountOfWords - 1).join(' ')

  const handleKeyboard = (e: any) => {
    if (e.code === 'Space' || e.code === 'Enter') {
      setIsExpanded(!isExpanded)
    }
  }

  return (
    <p className={className ?? ''} id={id}>
      {beginText}
      {itCanOverflow && (
        <>
          {!isExpanded && <span>... </span>}
          <span
            className={`transition ease-in-out duration-200 ${!isExpanded && 'hidden'}`}
            aria-hidden={!isExpanded}
          >
            {' '}
            {endText}
          </span>
          <span
            className="text-main-100 ml-2"
            role="button"
            tabIndex={0}
            aria-expanded={isExpanded}
            aria-controls={id}
            onKeyDown={handleKeyboard}
            onClick={() => setIsExpanded(!isExpanded)}
          >
            {isExpanded ? 'Lihat Sebagian' : 'Lihat Selengkapnya'}
          </span>
        </>
      )}
    </p>
  )
}
