import React, { useState, useEffect } from 'react'

const CountdownTimer = ({
  className,
  time,
  onComplete,
}: {
  className?: string
  time?: number
  onComplete?: () => void
}) => {
  const initialTime = (time ?? 2) * 60
  const [timeRemaining, setTimeRemaining] = useState(initialTime)
  const handleExpired = () => {
    onComplete && onComplete()
  }

  useEffect(() => {
    const timerInterval = setInterval(() => {
      setTimeRemaining((prevTime) => {
        if (prevTime === 0) {
          clearInterval(timerInterval)
          return 0
        } else {
          return prevTime - 1
        }
      })
    }, 1000)
    return () => clearInterval(timerInterval)
  }, [])

  useEffect(() => {
    if (timeRemaining === 0) {
      handleExpired()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [timeRemaining])

  const minutes =
    Math.floor((timeRemaining % 3600) / 60) < 10
      ? `0${Math.floor((timeRemaining % 3600) / 60)}`
      : Math.floor((timeRemaining % 3600) / 60)
  const seconds = timeRemaining % 60 < 10 ? `0${timeRemaining % 60}` : timeRemaining % 60

  return <span className={className ?? ''}>{`${minutes}:${seconds}`}</span>
}

export default CountdownTimer
