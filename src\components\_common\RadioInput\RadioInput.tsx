import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { OptionTypeProps } from '@/interfaces/components/_common/input'
import { cn } from '@/lib/utils'
import { RadioGroupProps } from '@radix-ui/react-radio-group'
import React from 'react'

type RadioInputInterface = {
  options: OptionTypeProps<string, string>[]
  label?: string
  errorMsg?: string
  note?: string
  placeholder?: string
  name: string
  onChange: (e: string) => void
  value?: string
  className?: string
  parentClass?: string
}

export const RadioInput = React.forwardRef<RadioGroupProps, RadioInputInterface>(function RadioInput(
  {
    placeholder,
    options,
    onChange,
    value,
    name,
    errorMsg,
    label,
    note,
    className,
    parentClass,
  }: RadioInputInterface,
  ref
) {
  return (
    <RadioGroup
      onValueChange={onChange}
      defaultValue={value}
      className={cn('flex flex-col space-y-1', parentClass)}
    >
      <label className="text-body-md font-bold text-gray-400">{label}</label>
      {options.map((item) => {
        return (
          <div key={item.value} className={cn(`flex items-center space-x-3 space-y-0`, className)}>
            <div className="flex items-center justify-center">
              <RadioGroupItem checked={item.value === value} value={item.value} id={item.value} />
            </div>
            <label htmlFor={item.value} className="font-normal">
              {item.label}
            </label>
          </div>
        )
      })}
      {errorMsg && <div className="text-xs text-red-500 text-left mt-1">{errorMsg}</div>}
    </RadioGroup>
  )
})
