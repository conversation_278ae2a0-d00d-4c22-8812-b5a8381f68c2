import { dashboardService } from '@/services/dashboard.service'
import { useQuery } from '@tanstack/react-query'

export const useGetTestimonyCounter = () => {
  return useQuery({
    queryKey: ['TotalTestimonyCounter'],
    queryFn: () =>
      dashboardService
        .adminGetTotaltestimonyCounter()
        .then((response) => response)
        .catch((error) => {
          console.log(error)
        }),
  })
}
