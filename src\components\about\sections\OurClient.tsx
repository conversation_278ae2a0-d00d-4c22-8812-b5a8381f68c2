/* eslint-disable @next/next/no-img-element */
import Marquee from 'react-fast-marquee'

const OurClient = ({
  heading,
  subHeading,
  content,
}: {
  heading: string
  subHeading: string
  content: string[][]
}) => {
  return (
    <div className="flex flex-col gap-y-6 items-center">
      <div className="flex flex-col gap-y-3 items-center px-4">
        <span className="text-subheading-md md:text-heading-md font-bold text-gray-400 text-center">
          {heading}
        </span>
        <span className="text-body-md md:text-body-lg font-medium text-gray-300 text-center">
          {subHeading}
        </span>
      </div>
      <div className="flex flex-col gap-y-[40px] justify-between items-center grayscale w-full relative">
        <Marquee direction="left" autoFill={true}>
          {content[0].length &&
            content[0].map((image) => {
              return (
                <div key={image} className="relative h-[80px] w-auto flex justify-center items-center">
                  <img
                    src={image}
                    alt="logo icon"
                    className="mr-[70px]"
                    style={{
                      width: 'auto',
                      height: '80px',
                      objectFit: 'contain',
                    }}
                    width={50}
                    height={80}
                  />
                </div>
              )
            })}
        </Marquee>
        <Marquee direction="left" autoFill={true}>
          {content[1].length &&
            content[1].map((image) => {
              return (
                <div key={image} className="relative h-[80px] w-auto flex justify-center items-center">
                  <img
                    src={image}
                    alt="logo icon"
                    className="mr-[70px]"
                    style={{
                      width: 'auto',
                      height: '80px',
                      objectFit: 'contain',
                    }}
                    width={50}
                    height={80}
                  />
                </div>
              )
            })}
        </Marquee>
      </div>
    </div>
  )
}

export default OurClient
