'use client'

import { LabelValue } from '@/components/_common/CardInfo/LabelValue'
import { Card, H4 } from '@/components/_common/ui'
import { PsychologistProfile } from '@/interfaces/profile-service'
import moment from 'moment-timezone'
import { MOMENT_INPUT_DATE_FORMAT } from '@/constans/date'

function getAge(dateString: string, timezone: string) {
  const isoDate = moment(dateString).toISOString()
  const date = moment.tz(isoDate, MOMENT_INPUT_DATE_FORMAT, timezone)
  const years = moment().diff(date, 'years')
  const month = moment().diff(date.add(years, 'years'), 'months', false)
  const days = moment().diff(date.add(month, 'months'), 'days', false)
  return { years, month, days, date }
}

const BirthdateView = ({ date, timezone }: { date: string; timezone: string }) => {
  const age = getAge(date, timezone)
  const formatDate = moment(date).format('DD MMMM YYYY')
  const ageOfPsychologist = `(${age.years} Tahun ${age.month} Bulan ${age.days} Hari)`
  return (
    <div className="flex items-center gap-1">
      <span>{formatDate}</span>
      <span className="text-[#737373]">{ageOfPsychologist}</span>
    </div>
  )
}

export const ProfileClient = ({
  nickname,
  userIdentity,
  maritalStatus,
  birthDate,
  gender,
  birthOrder,
  ethnicity,
  religion,
  domicile,
  occupation,
  education,
}: PsychologistProfile & {
  education: string
}) => {
  const childTo = birthOrder ? birthOrder.split('/')[0] : ''
  const totalSibling = birthOrder ? birthOrder.split('/')[1] : ''
  const birthOrderView = `${childTo} dari ${totalSibling} bersaudara`

  return (
    <div className="grid grid-flow-cols-dense grid-cols-1 xs:grid-cols-1 lg:grid-cols-2 grid-rows-1 gap-4 items-start">
      <Card className="xs:p-3 sm:p-4 md:p-4 lg:p-4 xl:p-4 grid gap-4">
        <H4 bold>Informasi</H4>
        <div className="grid grid-flow-row-dense grid-cols-3 grid-rows-1 gap-4 ">
          <LabelValue label="Nama Pangilan" value={nickname ?? '-'} />
          <LabelValue
            label="Email"
            value={<span className="text-main-100">{userIdentity?.email ?? '-'}</span>}
          />
          <LabelValue label="Jenis Kelamin" value={gender} />
          <LabelValue label="Pendidikan" value={education ?? '-'} />
          <LabelValue label="Pekerjaan" value={occupation ?? '-'} />
          <LabelValue label="Domisili" value={domicile ?? '-'} />
          <LabelValue label="Status Menikah" value={maritalStatus ?? '-'} />
          <LabelValue
            label="Tanggal Lahir"
            value={<BirthdateView date={birthDate} timezone={userIdentity?.userConfig?.TIMEZONE ?? ''} />}
          />
          <LabelValue label="Urutan Bersaudara" value={birthOrder ? birthOrderView : '-'} />
          <LabelValue label="Suku Bangsa" value={ethnicity ?? '-'} />
          <LabelValue label="Agama" value={religion ?? '-'} />
        </div>
      </Card>
    </div>
  )
}
