'use client'
import { <PERSON>er<PERSON>ontent } from '@/components/admin/HeaderContent'
import Breadcrumb from '@/components/breadcrumbs/Breadcrumbs'
import CardBantuan from './helpCard'
import StillNeedHelp from './stillNeedHelp'
import { useEffect, useState } from 'react'
import { profileService } from '@/services/profile.service'
import { NoDataFound } from '@/components/_common/NoData/NoDataFound'
import { useRouter, useSearchParams } from 'next/navigation'
import { Routes } from '@/constans/routes'
import { useGetInformationCategory } from '../hook/useGetInformationCategory.hook'
import AppInput from '@/components/_common/input/Input'
import { IIcons } from '@/components/_common/icon'

interface FaqItem {
  id: number
  question: string
  answer: string
}

export default function HelpCenterComponent() {
  const router = useRouter()
  const searchParam = useSearchParams()
  const isMobile = searchParam.get('isMobile')

  const [faqData, setFaqData] = useState<FaqItem[]>([])
  const [loading, setLoading] = useState<boolean>(false)
  const [error, setError] = useState<string | null>(null)
  const [search, setSearch] = useState<string | null>(null)

  useEffect(() => {
    setLoading(true)
    const fetchData = async () => {
      try {
        const response = await profileService.getFaq('HelpCenter')
        setFaqData(response ?? [])
      } catch (err: any) {
        setError(err.message ?? 'Failed to fetch data')
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  const { data: helpCenterData } = useGetInformationCategory('HelpCenter')
  const filterHelpCenter = search
    ? (helpCenterData ?? []).filter(
        (val: any) =>
          (val.title ?? '').toLowerCase().includes(search.toLowerCase()) ||
          (val.subtitle ?? '').toLowerCase().includes(search.toLowerCase()) ||
          (val.description ?? '').toLowerCase().includes(search.toLowerCase()) ||
          (val.content ?? '').toLowerCase().includes(search.toLowerCase())
      )
    : helpCenterData

  const handleClickItem = (id: string) => {
    router.push(`${Routes.UserPusatBantuan}/${id}${isMobile ? '?isMobile=true' : ''}`)
  }

  return (
    <>
      <div className="w-full lg:mt-[64px] px-4 lg:px-0 lg:w-[930px] xl:w-[1120px] max-w-[1120px] flex flex-col justify-center gap-6 relative z-0 py-4 lg:py-0 ">
        <HeaderContent className="mb-0 hidden md:block" title="Pusat Bantuan" />
        <div className="flex flex-col gap-4">
          <Breadcrumb containerClasses="hidden md:flex" pageName="Pusat Bantuan" />
          <div className="flex flex-col gap-2">
            <h1 className="max-w-[200px] text-[20px] font-bold text-[#222222]">
              Hi Teman Healing, Ada yang bisa kami bantu?
            </h1>
            <span className="text-[12px] text-[#535353]">Temukan jawabanmu disini</span>
          </div>
          <div className="w-full">
            <AppInput
              prefixIcon={IIcons.Search}
              name="search"
              type="text"
              placeholder="Cari disini.."
              value={search ?? ''}
              onChange={(e) => setSearch(e.target.value)}
              className={'w-full'}
            />
          </div>
          {/* card bantuan */}
          <div className="flex flex-col gap-4">
            {filterHelpCenter?.length ? (
              filterHelpCenter?.map((helpItem: any) => {
                return (
                  <CardBantuan
                    key={helpItem.id}
                    header={helpItem.title}
                    subHeader={helpItem.subtitle || helpItem.description}
                    onClickItem={() => handleClickItem(helpItem.id)}
                  />
                )
              })
            ) : (
              <NoDataFound />
            )}
          </div>
          {/* Hide this for a while until got new decission */}
          {/* <div className="flex flex-col gap-4 mt-4">
            <h2 className="text-[16px] text-[#222222] font-bold">Yang Sering Ditanyakan</h2>
            {loading ? (
              <AccordionSkeleton />
            ) : error ? (
              <NoDataFound note="Data Tidak ditemukan." />
            ) : (
              faqData.map((item) => (
                <AccordionHelpCenter key={item.id} header={item.question} answer={item.answer} />
              ))
            )}
          </div> */}
        </div>
      </div>
      <div className="w-full lg:mb-[196px] lg:mt-[64px] px-4 lg:px-0 lg:w-[930px] xl:w-[1120px] max-w-[1120px] flex flex-col justify-center gap-6 relative z-0 py-4 lg:py-0 md:mb-0">
        <StillNeedHelp />
      </div>
    </>
  )
}
