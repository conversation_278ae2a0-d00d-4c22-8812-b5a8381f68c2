import { STATIC_DATA } from '@/constans/STATIC_DATA'
import ColaborationsLogo from '../sections/ColaborationsLogo'
import { Correlation } from '../sections/Correlation'
import { Services } from '../sections/Services'
import { Testimony } from '../sections/Testimony/Testimony'
import { OurAdvantages } from '../sections/OurAdvantages'
import { OurApproaches } from '../sections/OurApproaches'
import { AssessmentForm } from '../sections/AssessmentForm'
import { Faq } from '../sections/Faq'
import { Container } from '../sections/Container'

export const MHEducationMain = () => {
  return (
    <div className="gap-y-[100px] w-full flex flex-col items-center">
      <div className="mt-10 w-full relative">
        <ColaborationsLogo {...STATIC_DATA.MHEducation.colaboration} />
      </div>
      <div className="min-h-[500px] flex flex-col justify-center max-w-MHmax md:px-10 lg:px-40 relative w-full gap-y-[100px]">
        <Correlation {...STATIC_DATA.MHEducation.section1} />
      </div>

      <Container className="bg-[#E6F5FD] py-15">
        <Services {...STATIC_DATA.MHEducation.section2} />
      </Container>

      <div className="min-h-[500px] flex flex-col justify-center max-w-MHmax md:px-10 lg:px-40 relative w-full gap-y-[100px]">
        <Testimony category="MHEducation" {...STATIC_DATA.MHEducation.section3} />
        <OurAdvantages {...STATIC_DATA.MHEducation.section4} />
      </div>

      <Container className="bg-gradient-to-t from-[#E6F5FD] to-white">
        <OurApproaches {...STATIC_DATA.MHEducation.section5} />
      </Container>

      <div
        id="faq"
        className="min-h-[500px] flex flex-col justify-center max-w-MHmax md:px-10 lg:px-40 relative w-full gap-y-[100px]"
      >
        <div className="px-4">
          <Faq category="MHEducation" />
        </div>
        <AssessmentForm />
      </div>
    </div>
  )
}

export default MHEducationMain
