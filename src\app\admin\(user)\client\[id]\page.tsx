import Breadcrumb from '@/components/breadcrumbs/Breadcrumbs'
import { Container } from '@/components/_common/ui'
import DetailClientComponent from '@/components/admin/client/DetailClientComponent'
import { Suspense } from 'react'

const DetailKlien = async ({ params }: { params: Promise<{ id: string }> }) => {
  const clientId = (await params).id
  return (
    <Suspense>
      <Breadcrumb containerClasses="pb-2" pageName="Detail Psikolog" />
      <Container>
        <DetailClientComponent clientId={clientId} />
      </Container>
    </Suspense>
  )
}

export function generateStaticParams() {
  return [{ id: 'test-id' }]
}

export default DetailKlien
