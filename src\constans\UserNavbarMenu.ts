import { IIcons } from '@/components/_common/icon'
import { MenuItemProps } from './SidebarMenu'

type UserMenuProps = MenuItemProps & {
  width: number
}

export const UserMenuItems: UserMenuProps[] = [
  {
    icon: IIcons.Home,
    label: 'Home',
    route: '/home',
    width: 46,
    labelMobile: 'Home',
    showInMobile: true,
    showInDesktop: true,
  },
  {
    icon: IIcons.Search,
    label: 'Psikolog',
    route: '/search-psikolog',
    width: 63,
    labelMobile: 'Search',
    showInMobile: true,
    showInDesktop: true,
  },
  {
    icon: IIcons.CounselingGroup,
    label: 'MH for Business',
    route: '/mh-business',
    width: 130,
    labelMobile: 'Business',
    showInMobile: false,
    showInDesktop: true,
  },
  {
    icon: IIcons.Notes,
    label: 'MH for Education',
    route: '/mh-education',
    width: 150,
    labelMobile: 'Education',
    showInMobile: false,
    showInDesktop: true,
  },
  {
    icon: IIcons.Notes,
    label: 'Tentang <PERSON>',
    route: '/about-us',
    width: 120,
    labelMobile: 'About',
    showInMobile: false,
    showInDesktop: true,
  },
  {
    icon: IIcons.Time,
    label: 'Jadwal',
    route: '/schedule',
    width: 120,
    labelMobile: 'Jadwal',
    showInMobile: true,
    showInDesktop: false,
  },
  {
    icon: IIcons.User,
    label: 'Profile',
    route: '/profile',
    width: 120,
    labelMobile: 'Akun',
    showInMobile: true,
    showInDesktop: false,
  },
  {
    icon: IIcons.Notes,
    label: 'Transaksi',
    route: '/transaksi',
    width: 80,
    labelMobile: 'Transaksi',
    showInMobile: true,
    showInDesktop: false,
  },
]
