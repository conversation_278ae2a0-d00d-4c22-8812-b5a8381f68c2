import { Separator } from '../ui/separator'
import { AvatarWithInfo } from '../_common/CardInfo/AvatarWithInfo'
import moment from 'moment'

export enum NotificationType {
  Personal = 'PERSONAL',
}
export enum EventName {
  CounselingApproved = 'COUNSELING_APPROVED',
}

export type NotificationItemType = {
  type: string
  eventName: string
  pushMessageHtml: string
  createdAt: string
  imageUrl: string | null
  isBodyItem: boolean
}

export const NotificationItem = ({
  type,
  eventName,
  pushMessageHtml,
  createdAt,
  imageUrl,
  isBodyItem,
}: NotificationItemType) => {
  return (
    <>
      <li className={`flex flex-row py-4 justify-between items-center ${isBodyItem ? '' : 'px-6'}`}>
        <AvatarWithInfo
          wrapClassName="items-center"
          className={`w-[40px] h-[40px]`}
          classImage={type === NotificationType.Personal && imageUrl ? '' : 'bg-line-200 object-scale-down'}
          image={imageUrl ?? '/icons/bell.svg'}
          name={type}
        >
          <div className="flex flex-col gap-y-1">
            <span className="text-body-md font-medium text-gray-400">
              {type === NotificationType.Personal ? (
                <span
                  className="[&>span]:text-[#039EE9]"
                  dangerouslySetInnerHTML={{ __html: pushMessageHtml }}
                ></span>
              ) : null}
              {/* {type === NotificationType.Approval ? (
                <span>
                  Psikolog <span className="text-[#039EE9]">{psikolog}</span> {content}{' '}
                  <span className="text-[#039EE9]">{author}</span>
                </span>
              ) : type === NotificationType.SubmitNewSchedule ? (
                <span>
                  Jadwal Konseling <span className="text-[#039EE9]">{author}</span> {content}{' '}
                  <span className="text-[#039EE9]">{psikolog}</span>
                </span>
              ) : type === NotificationType.Announcement ? (
                `${content}`
              ) : eventName === EventName.CounselingApproved ? (
                <span>{pushTitle}</span>
              ) : null} */}
            </span>
            <span className="text-caption-md font-medium text-gray-200">
              {moment(createdAt).locale('id').format('LLL')}
            </span>
          </div>
        </AvatarWithInfo>
        {/* <div className="flex flex-col h-full justify-center ml-4">
          <SVGIcons name={IIcons.ArrowRight} />
        </div> */}
      </li>
      <Separator orientation="horizontal" />
    </>
  )
}
