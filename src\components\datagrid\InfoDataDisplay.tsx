import { AppBigCaption, AppMediumText } from '../_common/ui'

type InfoDataDisplayType = {
  title: string
  subTitle: string
}
export const InfoDataDisplay = ({ title, subTitle }: InfoDataDisplayType) => {
  return (
    <div className="flex items-center flex-start gap-x-2">
      <div className="flex flex-col gap-y-1">
        <AppMediumText className="text-gray-400 capitalize">{title}</AppMediumText>
        <AppBigCaption className="text-gray-200">{subTitle}</AppBigCaption>
      </div>
    </div>
  )
}
