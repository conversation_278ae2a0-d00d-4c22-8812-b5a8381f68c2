'use client'

import { useState, useRef } from 'react'
import { AppModal } from '@/components/_common/Modal/AppModal'
import ButtonPrimary from '@/components/_common/ButtonPrimary'
import { Calendar } from '@/components/ui/calendar'
import AppInput from '@/components/_common/input/Input'
import { useForm } from 'react-hook-form'
import { psychologistService } from '@/services/psychologist.service'
import { InputImage } from '@/components/_common/InputImage'

interface WithdrawalFormDialogProps {
  isOpen: boolean
  onClose: () => void
  psychologistId: string
}

interface WithdrawalFormValues {
  amount: string
  transferDate: Date
  attachmentPhoto: FileList
  hours: string
  minutes: string
}

export function WithdrawalFormDialog({ isOpen, onClose, psychologistId }: WithdrawalFormDialogProps) {
  const [selectedTime, setSelectedTime] = useState({
    hours: '00',
    minutes: '00',
  })
  const [showDatePicker, setShowDatePicker] = useState(false)
  const fileInputRef = useRef(null)
  const [previewImage, setPreviewImage] = useState<string>('')

  const {
    register,
    setValue,
    getValues,
    handleSubmit,
    formState: { errors },
  } = useForm<WithdrawalFormValues>({
    defaultValues: {
      amount: '',
      transferDate: undefined,
      attachmentPhoto: undefined,
    },
  })

  register('transferDate', {
    required: 'Tanggal transfer wajib diisi',
    validate: (value) => {
      if (!value) return 'Tanggal transfer wajib diisi'
      return true
    },
  })

  register('attachmentPhoto', {
    required: 'Bukti transfer wajib diupload',
    validate: {
      fileSize: (value) => {
        const file = value?.[0]
        if (file && file.size > 2 * 1024 * 1024) {
          return 'Ukuran file maksimal 2MB'
        }
        return true
      },
      fileType: (value) => {
        const file = value?.[0]
        if (file && !file.type.includes('image/')) {
          return 'File harus berupa gambar'
        }
        return true
      },
    },
  })

  const handleFileChange = (file: File) => {
    setValue('attachmentPhoto', [file] as unknown as FileList, {
      shouldValidate: true,
    })
  }

  const formatDateTime = (date?: Date) => {
    if (!date) return ''
    return new Date(date).toLocaleString('id-ID', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  const onSubmit = async (data: WithdrawalFormValues) => {
    try {
      if (!data.transferDate) {
        throw new Error('Tanggal transfer wajib diisi')
      }

      const formData = new FormData()

      // Format amount and date
      const amount = data.amount.replace(/[^\d]/g, '')
      const transferDate = (() => {
        const date = new Date(data.transferDate)
        date.setHours(parseInt(selectedTime.hours))
        date.setMinutes(parseInt(selectedTime.minutes))
        return date.toISOString()
      })()

      // Append basic form fields
      formData.append('amount', amount)
      formData.append('transferDate', transferDate)

      // Append file if it exists
      if (data.attachmentPhoto?.[0]) {
        formData.append('attachmentPhoto', data.attachmentPhoto[0])
      }

      console.log('Form data:', formData)

      await psychologistService.postPsychologistWithdrawal(formData, psychologistId)
      onClose() // Uncomment this to close modal after successful submission
      console.log('Withdrawal submitted successfully')
    } catch (error) {
      console.error('Error submitting withdrawal:', error)
    }
  }

  return (
    <AppModal
      open={isOpen}
      onClose={onClose}
      title="Bukti Tarik Pendapatan Psikolog"
      className="w-full max-w-[700px]"
    >
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6 py-4">
        <AppInput
          {...register('amount', { required: 'Nominal penarikan wajib diisi' })}
          label="Nominal Penarikan Dana*"
          type="text"
          placeholder="Rp2.000.000"
          errorMsg={errors.amount?.message}
        />
        <div className="space-y-2">
          <label className="text-sm font-medium">Tanggal Transfer*</label>
          <div
            className="w-full rounded-lg border p-3 cursor-pointer hover:bg-gray-50"
            onClick={() => setShowDatePicker(true)}
          >
            {formatDateTime(getValues('transferDate')) || 'Pilih tanggal dan waktu transfer'}
          </div>
          {errors.transferDate && <span className="text-red-500 text-sm">{errors.transferDate.message}</span>}
        </div>

        <AppModal
          open={showDatePicker}
          onClose={() => setShowDatePicker(false)}
          title="Pilih Tanggal dan Waktu Transfer"
          className="w-full max-w-[360px]"
        >
          <div className="space-y-4 p-4">
            <Calendar
              mode="single"
              selected={getValues('transferDate')}
              onSelect={(date) => {
                if (date) {
                  const currentDate = new Date(date)
                  currentDate.setHours(parseInt(selectedTime.hours))
                  currentDate.setMinutes(parseInt(selectedTime.minutes))
                  setValue('transferDate', currentDate, {
                    shouldValidate: true,
                    shouldDirty: true,
                  })
                }
              }}
              className="w-full"
              captionLayout="dropdown-buttons"
              fromYear={2023}
              toYear={new Date().getFullYear()}
            />

            <div className="flex items-end gap-2 pt-4 border-t">
              <div className="flex-1 space-y-2">
                <label className="text-sm font-medium">Jam</label>
                <input
                  type="number"
                  min="0"
                  max="23"
                  value={selectedTime.hours}
                  onChange={(e) => {
                    const value = e.target.value
                    const hours = value === '' ? '00' : value.padStart(2, '0')
                    setSelectedTime((prev) => ({
                      ...prev,
                      hours: Math.min(23, Math.max(0, parseInt(hours)))
                        .toString()
                        .padStart(2, '0'),
                    }))

                    if (getValues('transferDate')) {
                      const currentDate = new Date(getValues('transferDate'))
                      currentDate.setHours(parseInt(hours))
                      setValue('transferDate', currentDate, { shouldValidate: true })
                    }
                  }}
                  className="w-full rounded-lg border p-2"
                  placeholder="00"
                />
              </div>
              <span className="mb-2">:</span>
              <div className="flex-1 space-y-2">
                <label className="text-sm font-medium">Menit</label>
                <input
                  type="number"
                  min="0"
                  max="59"
                  value={selectedTime.minutes}
                  onChange={(e) => {
                    const value = e.target.value
                    const minutes = value === '' ? '00' : value.padStart(2, '0')
                    setSelectedTime((prev) => ({
                      ...prev,
                      minutes: Math.min(59, Math.max(0, parseInt(minutes)))
                        .toString()
                        .padStart(2, '0'),
                    }))

                    if (getValues('transferDate')) {
                      const currentDate = new Date(getValues('transferDate'))
                      currentDate.setMinutes(parseInt(minutes))
                      setValue('transferDate', currentDate, { shouldValidate: true })
                    }
                  }}
                  className="w-full rounded-lg border p-2"
                  placeholder="00"
                />
              </div>
            </div>

            <div className="flex justify-end space-x-4 mt-4">
              <ButtonPrimary
                variant="outlined"
                color="gray"
                onClick={() => setShowDatePicker(false)}
                size="sm"
              >
                Batal
              </ButtonPrimary>
              <ButtonPrimary variant="contained" onClick={() => setShowDatePicker(false)} size="sm">
                Pilih
              </ButtonPrimary>
            </div>
          </div>
        </AppModal>

        <div className="space-y-2">
          <InputImage
            label="Bukti Transfer*"
            name="attachmentPhoto"
            accept="image/*"
            maxFileSizeMb={2}
            preview={previewImage}
            inputRef={fileInputRef}
            onChange={handleFileChange}
            className="w-full h-[200px] object-cover"
          />
          {errors.attachmentPhoto && (
            <span className="text-red-500 text-sm">{errors.attachmentPhoto.message}</span>
          )}
        </div>

        <div className="flex justify-end space-x-4">
          <ButtonPrimary
            variant="outlined"
            color="gray"
            onClick={onClose}
            className="min-w-[140px]"
            size="sm"
            type="button"
          >
            Batal
          </ButtonPrimary>
          <ButtonPrimary variant="contained" className="min-w-[140px]" size="sm" type="submit">
            Simpan
          </ButtonPrimary>
        </div>
      </form>
    </AppModal>
  )
}
