import { config } from '@/constans/config'
import { httpRequest } from '@/utils/network'

export class ContactService {
  async postAssestmentEducation(payload: any) {
    return await httpRequest({
      method: 'post',
      url: `${config?.apiBaseUrl}api/contact-form/education`,
      data: payload,
    })
  }
  async postAssestmentBusiness(payload: any) {
    return await httpRequest({
      method: 'post',
      url: `${config?.apiBaseUrl}api/contact-form/business`,
      data: payload,
    })
  }
}

export const contactService = new ContactService()
