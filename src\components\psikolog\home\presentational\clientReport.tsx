import { IIcons, SVGIcons } from '@/components/_common/icon'

export default function ClientReport() {
  return (
    <>
      <div className="flex flex-col gap-[16px] bg-white rounded-[15px] border border-[#EBEBEB] p-[24px]">
        <div className="flex flex-col gap-[12px]">
          <div className="flex justify-between items-center">
            <p className="font-bold text-[14px] md:text-[16px] text-[#222222]">Menunggu Klien Report</p>
            <a href="" className="text-[#039EE9] text-[12px] md:text-[14px] font-bold">
              Lihat Semua
            </a>
          </div>
          {/* 1 */}
          <div className="flex justify-between items-center py-[1rem]">
            <div className="flex items-center gap-[12px]">
              {/* img profile */}
              <div className="flex items-center justify-center bg-[#E7F7FF] w-[28px] h-[28px] md:w-[36px] md:h-[36px] rounded-full">
                <span className="text-[#222222] text-[10px] md:text-[14px] font-bold">HS</span>
              </div>
              {/* title */}
              <div className="flex flex-col gap-1">
                <p className="font-bold text-[#222222] text-[12px] md:text-[14px]">
                  Tulis klien report untuk Hilmi Salim
                </p>
                <p className="text-[#535353] text-[10px] md:text-[12px]">
                  Konseling di 17 Agu 2024, 09:00-10:00 WIB{' '}
                </p>
              </div>
            </div>
            <SVGIcons className="ml-2" name={IIcons.ArrowRight} />
          </div>
          {/* 2 */}
          <div className="flex justify-between items-center py-[1rem]">
            <div className="flex items-center gap-[12px]">
              {/* img profile */}
              <div className="flex items-center justify-center bg-[#E7F7FF] w-[28px] h-[28px] md:w-[36px] md:h-[36px] rounded-full">
                <span className="text-[#222222] text-[10px] md:text-[14px] font-bold">HS</span>
              </div>
              {/* title */}
              <div className="flex flex-col gap-1">
                <p className="font-bold text-[#222222] text-[12px] md:text-[14px]">
                  Tulis klien report untuk Hilmi Salim
                </p>
                <p className="text-[#535353] text-[10px] md:text-[12px]">
                  Konseling di 17 Agu 2024, 09:00-10:00 WIB{' '}
                </p>
              </div>
            </div>
            <SVGIcons className="ml-2" name={IIcons.ArrowRight} />
          </div>
        </div>
      </div>
    </>
  )
}
