// import { ScheduleListProps } from '@/components/psikolog/schedule/ScheduleList'
import { IModalActionState } from '@/interfaces/components/psikolog/schedule'
import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import moment from 'moment'

type ClientItemProps = {
  id: string
  userIdentityId: string
  fullName: string
  nickname: string
  birthDate: string
  gender: string
  phoneNumber: string
  birthOrder: string
  religion: string
  ethnicity: string
  domicile: string
  maritalStatus: string
  education: string
  occupation: string
  profilePhoto: string
  balance: number
  joinDate: string
  endDate: string
}

export type ClientReportProps = {
  id: string
  psychologistId: string
  clientId: string
  counselingId: string
  activityId: null | string
  data: null | string
  anamnesis: string
  intervention: string
  task: string
  assessmentFile: string
  notesForClient: string
  nextCounselingAnswer: boolean
  nextCounselingDate: string
  nextCounselingDuration: number
  createdAt: string
  createdBy: null | string
  modifiedAt: string
  modifiedBy: null | string
}
export type ScheduleListProps = {
  id: string
  psychologistId: string
  clientId: string
  clientReportId: null | string
  clientReport?: ClientReportProps
  testimonyId: null | string
  problemCategoryId: null | string
  startTime: string
  endTime: string
  startTimeByClient: null | string
  endTimeByClient: null | string
  startTimeByPsychologist: null | string
  endTimeByPsychologist: null | string
  complaint: string
  expectation: string
  status: string
  description: string
  method: string
  location: string
  duration: number
  client: ClientItemProps
  payment: string | null
  problemCategory: ProblemCategoryProps[] | null
  createdAt: string
  messageForPsychologist: string | null
  messageForClient: string | null
}
type ProblemCategoryProps = {
  id: string
  problemCategory: string
  iconUrl: null | string
  order: number | null
  createdAt: string
  createdBy: string
  modifiedAt: string
  modifiedBy: string
}

export type RescheduleDetailProps = {
  date: string | undefined
  time: string | null
  note?: string | null
}

type PsikologProps = {
  isLoading: boolean
  showModalAction: IModalActionState | null
  modalState: {
    modal: IModalActionState | null
    item: ScheduleListProps | null
  }
  weekView: {
    start: string | null
    end: string | null
  }
  rescheduleDetail: RescheduleDetailProps
  rescheduleStep: number
  rejectStep: number
  rejectNote?: string | null
}

const scheduleSlice = createSlice({
  name: 'psikolog-schedule',
  initialState: {
    isLoading: false,
    showModalAction: null,
    modalState: {
      modal: null,
      item: null,
    },
    rescheduleDetail: {
      date: undefined,
      time: null,
      note: null,
    },
    weekView: {
      start: moment().format('YYYY-MM-DD'),
      end: moment().add(6, 'days').format('YYYY-MM-DD'),
    },
    rescheduleStep: 1,
    rejectStep: 1,
    rejectNote: '',
  } as PsikologProps,
  reducers: {
    toggleModalAction(state, action: PayloadAction<IModalActionState>) {
      state.showModalAction = action.payload
    },
    setModalState(state, action: PayloadAction<PsikologProps['modalState']>) {
      state.modalState = action.payload
    },
    setRescheduleStep(state, action: PayloadAction<number>) {
      state.rescheduleStep = action.payload
    },
    setRescheduleDetail(state, action: PayloadAction<RescheduleDetailProps>) {
      state.rescheduleDetail = action.payload
    },
    setRejectStep(state, action: PayloadAction<number>) {
      state.rejectStep = action.payload
    },
    setRejectNote(state, action: PayloadAction<string | null>) {
      state.rejectNote = action.payload
    },
    setWeekView(state, action: PayloadAction<any>) {
      state.weekView = action.payload
    },
  },
})

export const {
  toggleModalAction,
  setModalState,
  setRescheduleStep,
  setRescheduleDetail,
  setRejectStep,
  setRejectNote,
  setWeekView,
} = scheduleSlice.actions
export default scheduleSlice.reducer
