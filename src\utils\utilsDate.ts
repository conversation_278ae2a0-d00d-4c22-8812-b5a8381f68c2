const DAYS_IN_INDONESIAN = ['<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', 'Sabtu']

const MONTHS_IN_INDONESIAN = [
  '<PERSON><PERSON><PERSON>',
  '<PERSON><PERSON><PERSON>',
  '<PERSON><PERSON>',
  'April',
  '<PERSON>',
  '<PERSON><PERSON>',
  '<PERSON><PERSON>',
  '<PERSON>gus<PERSON>',
  'September',
  'Ok<PERSON><PERSON>',
  'November',
  '<PERSON><PERSON><PERSON>',
]

/**
 * Format a date to Indonesian format: "Senin, 1 Agustus 2024"
 *
 * @param date - Date object to format
 * @returns Formatted date string in Indonesian format
 */
export const formatDate = (date: Date): string => {
  if (!date || isNaN(date.getTime())) {
    return 'Invalid date'
  }

  const day = DAYS_IN_INDONESIAN[date.getDay()]
  const dateNum = date.getDate()
  const month = MONTHS_IN_INDONESIAN[date.getMonth()]
  const year = date.getFullYear()

  return `${day}, ${dateNum} ${month} ${year}`
}

/**
 * Format time to "HH.MM" format (24-hour clock)
 *
 * @param date - Date object containing the time to format
 * @returns Formatted time string
 */
export const formatTime = (date: Date): string => {
  if (!date || isNaN(date.getTime())) {
    return 'Invalid time'
  }

  const hours = date.getHours().toString().padStart(2, '0')
  const minutes = date.getMinutes().toString().padStart(2, '0')

  return `${hours}.${minutes}`
}

/**
 * Format a datetime range for display
 *
 * @param startTime - Start date and time
 * @param endTime - End date and time
 * @returns Formatted string with date and time range
 */
export const formatDateTimeRange = (startTime: Date, endTime: Date): string => {
  if (!startTime || !endTime || isNaN(startTime.getTime()) || isNaN(endTime.getTime())) {
    return 'Invalid datetime range'
  }

  const formattedDate = formatDate(startTime)
  const formattedStartTime = formatTime(startTime)
  const formattedEndTime = formatTime(endTime)

  return `${formattedDate}, ${formattedStartTime} - ${formattedEndTime} WIB`
}

/**
 * Calculate the remaining time from now until the target date
 * Returns a formatted string like "23:45:12" (hours:minutes:seconds)
 *
 * @param targetDate - The target date to calculate remaining time to
 * @returns Formatted remaining time string
 */
export const calculateRemainingTime = (targetDate: Date): string => {
  if (!targetDate || isNaN(targetDate.getTime())) {
    return '00:00:00'
  }

  const now = new Date()
  const difference = targetDate.getTime() - now.getTime()

  // Return 00:00:00 if the target date is in the past
  if (difference <= 0) {
    return '00:00:00'
  }

  // Calculate hours, minutes, seconds
  const hours = Math.floor(difference / (1000 * 60 * 60))
  const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60))
  const seconds = Math.floor((difference % (1000 * 60)) / 1000)

  // Format with leading zeros
  const formattedHours = hours.toString().padStart(2, '0')
  const formattedMinutes = minutes.toString().padStart(2, '0')
  const formattedSeconds = seconds.toString().padStart(2, '0')

  return `${formattedHours}:${formattedMinutes}:${formattedSeconds}`
}

/**
 * Check if a date is today
 *
 * @param date - Date to check
 * @returns Boolean indicating if the date is today
 */
export const isToday = (date: Date): boolean => {
  if (!date || isNaN(date.getTime())) {
    return false
  }

  const today = new Date()
  return (
    date.getDate() === today.getDate() &&
    date.getMonth() === today.getMonth() &&
    date.getFullYear() === today.getFullYear()
  )
}

/**
 * Get relative time description (Today, Tomorrow, or the actual date)
 *
 * @param date - Date to format
 * @returns String describing the relative date
 */
export const getRelativeDate = (date: Date): string => {
  if (!date || isNaN(date.getTime())) {
    return 'Invalid date'
  }

  if (isToday(date)) {
    return 'Hari ini'
  }

  const tomorrow = new Date()
  tomorrow.setDate(tomorrow.getDate() + 1)

  if (
    date.getDate() === tomorrow.getDate() &&
    date.getMonth() === tomorrow.getMonth() &&
    date.getFullYear() === tomorrow.getFullYear()
  ) {
    return 'Besok'
  }

  return formatDate(date)
}
