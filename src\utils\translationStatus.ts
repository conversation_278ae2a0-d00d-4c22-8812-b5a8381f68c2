import { PaymentStatus } from '@/constans/StaticOptions'

export function translateCounselingStatus(status: string) {
  let result = {}
  switch (status) {
    case PaymentStatus.INPROGRESS:
      result = { variant: 'info', value: 'Sedang Berlangsung' }
      break
    case PaymentStatus.APPROVED:
      result = { variant: 'info', value: 'Akan Datang' }
      break
    case PaymentStatus.PAID_PAYMENT:
    case PaymentStatus.RESCHEDULE_BY_CLIENT:
      result = { variant: 'base', value: 'Menunggu Konfirmasi Psikolog' }
      break
    case PaymentStatus.RESCHEDULE_BY_PSYCHOLOGIST:
      result = { variant: 'base', value: 'Menung<PERSON> (Pindah Jadwal)' }
      break
    case PaymentStatus.PENDING_PAYMENT:
      result = { variant: 'base', value: 'Menunggu Pembayaran' }
      break
    case PaymentStatus.COMPLETED:
      result = { variant: 'success', value: '<PERSON><PERSON><PERSON>' }
      break
    case PaymentStatus.EXPIRED_PAYMENT:
    case PaymentStatus.CANCELLED_BY_PSYCHOLOGIST:
    case PaymentStatus.CANCELLED_BY_ADMIN:
    case PaymentStatus.REJECTED_BY_PSYCHOLOGIST:
      result = { variant: 'danger', value: 'Batal' }
      break
    default:
      result = { variant: 'base', value: status }
      break
  }
  return result as { variant: 'info' | 'base' | 'success' | 'danger'; value: string }
}

// export function translationCounselingStatusToVariant
