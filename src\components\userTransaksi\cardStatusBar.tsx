import ButtonPrimary from '../_common/ButtonPrimary'
import { formatRupiah } from '@/lib/utils' // For currency formatting
import { format } from 'date-fns' // For date formatting

export interface Payment {
  id: string
  status: string
  amount: number
  originalAmount: number
  voucherAmount: number
  discountAmount?: number | any
  platformFee?: number
  paidAt: string | null
  createdAt: string
  gatewayUrl: string
  gatewayAdditionalData?: {
    id: string
    amount: number
    status: string
    paid_at?: string
    created?: string
    payment_method?: string
    ewallet_type?: string
  }
}

interface CardStatusBarProps {
  title: string
  payment?: Payment
}

export default function CardStatusBar({ title, payment }: CardStatusBarProps) {
  const titleBar = () => {
    if (title === 'Konseling dengan Psikolog') {
      return 'Berhasil Dijadwalkan'
    } else if (title === 'Menunggu Pembayaran Konseling') {
      return 'Menunggu Pembayaran'
    } else if (title === 'Menunggu Konfirmasi Psikolog') {
      return 'Menunggu Konfirmasi Psikolog'
    } else if (title === 'Menunggu Konfirmasi Kamu') {
      return 'Menunggu Konfirmasi Kamu'
    } else if (title === 'Konseling Selesai') {
      return 'Konseling Selesai'
    } else if (title === 'Konseling Dibatalkan') {
      return 'Konseling Dibatalkan'
    } else if (title === 'Pembayaran Konseling Gagal') {
      return 'Pembayaran Konseling Gagal'
    }
    return title
  }

  const tipColor = () => {
    if (title === 'Konseling dengan Psikolog' || title === 'Konseling Selesai') {
      return 'bg-[#039EE9]'
    } else if (
      title === 'Menunggu Konfirmasi Psikolog' ||
      title === 'Menunggu Konfirmasi Kamu' ||
      title === 'Menunggu Pembayaran Konseling'
    ) {
      return 'bg-[#FED748]'
    } else {
      return 'bg-[#E42B3B]'
    }
  }

  const tipColorBorder = () => {
    if (title === 'Konseling dengan Psikolog' || title === 'Konseling Selesai') {
      return 'hover:border-[#039EE9]'
    } else if (
      title === 'Menunggu Konfirmasi Psikolog' ||
      title === 'Menunggu Konfirmasi Kamu' ||
      title === 'Menunggu Pembayaran Konseling'
    ) {
      return 'hover:border-[#FED748]'
    } else {
      return 'hover:border-[#E42B3B]'
    }
  }

  const paymentCountDown = () => {
    if (titleBar() === 'Menunggu Pembayaran' && payment) {
      // Calculate expiry time (24 hours from creation)
      const createdDate = new Date(payment.createdAt)
      const expiryDate = new Date(createdDate.getTime() + 24 * 60 * 60 * 1000)

      // Format the expiry date
      const formattedExpiryDate = format(expiryDate, 'dd MMM yyyy, HH:mm')

      // Calculate remaining time
      const now = new Date()
      const timeLeft = expiryDate.getTime() - now.getTime()

      // Convert to hours, minutes, seconds
      const hours = Math.floor(timeLeft / (1000 * 60 * 60))
      const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60))
      const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000)

      return (
        <>
          <div className="flex flex-col gap-4">
            <div className="w-full h-[1px] bg-[#EBEBEB]"></div>
            <div className="flex flex-col gap-[18px]">
              <div className="flex justify-between items-center">
                <div className="flex flex-col gap-1">
                  <span className="text-[14px] text-[#222222]">Bayar sebelum</span>
                  <span className="text-[14px] text-[#535353]">{formattedExpiryDate} WIB</span>
                </div>
                <span className="text-[14px] text-[#E42B3B]">
                  Sisa Waktu {hours}:{minutes.toString().padStart(2, '0')}:
                  {seconds.toString().padStart(2, '0')}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <div className="flex flex-col gap-1">
                  <span className="text-[14px] text-[#535353]">Total biaya</span>
                  <span className="text-[16px] text-[#222222] font-bold">{formatRupiah(payment.amount)}</span>
                  {payment.voucherAmount > 0 && (
                    <span className="text-[12px] text-[#535353]">
                      Hemat {formatRupiah(payment.voucherAmount)}
                    </span>
                  )}
                  {payment.discountAmount > 0 && (
                    <span className="text-[12px] text-[#535353]">
                      Diskon {formatRupiah(payment.discountAmount)}
                    </span>
                  )}
                </div>
                {payment.status === 'PENDING' && (
                  <ButtonPrimary
                    className="min-w-[143px] rounded-full"
                    variant={'contained'}
                    size="xs"
                    onClick={() => (window.location.href = payment.gatewayUrl)}
                  >
                    Bayar Konseling
                  </ButtonPrimary>
                )}
              </div>
            </div>
          </div>
        </>
      )
    }
    return null
  }

  // Show payment details for PAID status
  const paymentDetails = () => {
    if (payment && payment.status === 'PAID') {
      const paymentMethod = payment.gatewayAdditionalData?.payment_method || 'EWALLET'
      const ewalletType = payment.gatewayAdditionalData?.ewallet_type || ''
      const paymentMethodDisplay = ewalletType || paymentMethod

      const paidDate = payment.paidAt
        ? format(new Date(payment.paidAt), 'dd MMM yyyy, HH:mm')
        : format(new Date(), 'dd MMM yyyy, HH:mm')

      return (
        <>
          <div className="flex flex-col gap-4">
            <div className="w-full h-[1px] bg-[#EBEBEB]"></div>
            <div className="flex flex-col gap-[18px]">
              <div className="flex justify-between items-center">
                <div className="flex flex-col gap-1">
                  <span className="text-[14px] text-[#222222]">Metode Pembayaran</span>
                  <span className="text-[14px] text-[#535353]">{paymentMethodDisplay}</span>
                </div>
                <div className="flex flex-col gap-1 items-end">
                  <span className="text-[14px] text-[#222222]">Tanggal Pembayaran</span>
                  <span className="text-[14px] text-[#535353]">{paidDate} WIB</span>
                </div>
              </div>
              <div className="flex justify-between items-center">
                <div className="flex flex-col gap-1">
                  <span className="text-[14px] text-[#535353]">Total biaya</span>
                  <span className="text-[16px] text-[#222222] font-bold">{formatRupiah(payment.amount)}</span>
                  {payment.voucherAmount > 0 && (
                    <span className="text-[12px] text-[#535353]">
                      Hemat {formatRupiah(payment.voucherAmount)}
                    </span>
                  )}
                </div>
              </div>
            </div>
          </div>
        </>
      )
    }
    return null
  }

  // Determine which payment content to show based on status
  const renderPaymentContent = () => {
    if (!payment) {
      console.log('No payment data available') // Debug log
      return null
    }

    if (payment.status === 'PENDING') {
      return paymentCountDown()
    } else if (payment.status === 'PAID') {
      return paymentDetails()
    }

    return null
  }

  return (
    <>
      <div
        className={`w-full flex flex-col gap-4 bg-white border border-[#ebebeb] ${tipColorBorder()} rounded-[15px] p-4 relative`}
      >
        <div className={`${tipColor()} h-5 w-1 rounded-tr-md rounded-br-md absolute left-0 top-4`}></div>
        <span className="text-[14px] font-bold">{titleBar()}</span>
        {payment && renderPaymentContent()}
      </div>
    </>
  )
}
