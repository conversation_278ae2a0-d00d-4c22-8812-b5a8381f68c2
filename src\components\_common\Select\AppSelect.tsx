import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
} from '@/components/ui/select'
import { cn } from '@/lib/utils'
import React from 'react'

type AppSelectProps = {
  options: { label: string; value: string }[]
  renderOption?: (option: { label: string; value: string }) => JSX.Element
  placeholder: string
  className?: string
  wrapClass?: string
  onChange: (value: any) => void
  value?: string
  errorMsg?: string
  name?: string
  label?: string
  useFilterIcon?: boolean
}
export const AppSelect = React.forwardRef<AppSelectProps, AppSelectProps>(function AppSelect(
  { name, label, className, wrapClass, options, placeholder, value, errorMsg, useFilterIcon, onChange },
  ref
) {
  return (
    <div className={cn('flex flex-col w-full relative', wrapClass)}>
      <Select
        name={name ? name : ''}
        defaultValue={value ?? ''}
        value={value}
        onValueChange={(e) => {
          onChange(e)
        }}
      >
        {label && (
          <SelectGroup>
            <SelectLabel className="p-0 pb-2">{label}</SelectLabel>
          </SelectGroup>
        )}
        <SelectTrigger
          useFilterIcon={useFilterIcon}
          className={cn(`w-full h-auto rounded-2xl border-line-200 shadow-none text-body-md`, className)}
        >
          <SelectValue placeholder={placeholder} />
        </SelectTrigger>
        <SelectContent>
          <SelectGroup>
            {options.map((opt, index) => (
              <SelectItem key={index} value={opt.value}>
                {opt.label}
              </SelectItem>
            ))}
          </SelectGroup>
        </SelectContent>
      </Select>
      {errorMsg && <span className="text-caption-md text-danger-100">{errorMsg}</span>}
    </div>
  )
})
