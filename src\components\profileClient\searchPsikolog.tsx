'use client'
import ButtonPrimary from '@/components/_common/ButtonPrimary'
import { useRouter } from 'next/navigation'

export default function SearchPsikolog({ route }: { route: string }) {
  const router = useRouter()

  return (
    <div className="bg-[#E7F7FF] py-20 hidden md:flex items-center justify-center w-full mt-[274px] h-[450px] relative">
      <div className="flex flex-col items-center justify-center gap-[55px] mb-48">
        <div className="flex flex-col items-center justify-center gap-3">
          <h3 className="text-[20px] text-black font-bold">
            <PERSON>a seperti kesehatan fisik, mental pun harus dijaga.
          </h3>
          <span className="text-[#535353] text-[14px]">
            J<PERSON><PERSON>an konsultasi dengan Psikolog di MentalHealing.id
          </span>
          <ButtonPrimary
            onClick={() => router.push(route)}
            className="min-w-[143px] rounded-full"
            variant={'contained'}
            size="xs"
          >
            Cari Psikolog
          </ButtonPrimary>
        </div>
      </div>
      <figure className="absolute bottom-0 left-1/2 transform -translate-x-1/2">
        <img src={'/ilustration/call.svg'} alt="ilustration" />
      </figure>
    </div>
  )
}
