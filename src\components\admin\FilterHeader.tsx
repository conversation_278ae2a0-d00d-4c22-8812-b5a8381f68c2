import { useState } from 'react'
import { IIcons } from '../_common/icon'
import AppInput from '../_common/input/Input'
import { AppSelect } from '../_common/Select/AppSelect'
import ButtonPrimary from '../_common/ButtonPrimary'

import { AppSheet } from '../_common/Sheet/AppSheet'
import { RadioInput } from '../_common/RadioInput/RadioInput'
import { Separator } from '../ui/separator'

export const FilterHeader = ({
  labelFirstFilter,
  labelSecondFilter,
  firstOptions,
  secondOptions,
  showSecondFilter,
  onChangeFilter,
  onChangeSecondFilter,
  onChangeSearch,
}: {
  labelFirstFilter?: string
  labelSecondFilter?: string
  firstOptions?: any[]
  secondOptions?: any[]
  showSecondFilter?: boolean
  onChangeFilter?: (value: string) => void
  onChangeSecondFilter?: (value: string) => void
  onChangeSearch?: (value: string) => void
}) => {
  const [search, setSearch] = useState<string>('')
  const [filter, setFilter] = useState<string>('')
  const [secondFilter, setSecondFilter] = useState<string>('')
  const [toggle, setToggle] = useState<boolean>(false)

  const toggleSheet = () => {
    setToggle((prev) => !prev)
  }

  return (
    <>
      {onChangeSearch && (
        <AppInput
          type="text"
          placeholder="Cari disini.."
          value={search}
          onChange={(e) => {
            setSearch(e.target.value)
            onChangeSearch(e.target.value)
          }}
          errorMsg={''}
          prefixIcon={IIcons.Search}
        />
      )}
      <div className="gap-x-5 justify-between md:justify-start hidden md:flex">
        {firstOptions && onChangeFilter && (
          <AppSelect
            useFilterIcon
            name="filter"
            options={firstOptions}
            className="md:min-w-[200px] text-gray-300 text-body-md h-full "
            placeholder={labelFirstFilter ?? ''}
            onChange={(val) => {
              setFilter(val)
              onChangeFilter(val)
            }}
            value={filter}
            errorMsg={''}
          />
        )}
        {showSecondFilter && secondOptions && onChangeSecondFilter && (
          <AppSelect
            useFilterIcon
            name="secondFilter"
            options={secondOptions}
            className="md:min-w-[200px] text-gray-300 text-body-md h-full"
            placeholder={labelSecondFilter ?? ''}
            onChange={(val) => {
              setSecondFilter(val)
              onChangeSecondFilter(val)
            }}
            value={secondFilter}
            errorMsg={''}
          />
        )}
      </div>
      {firstOptions?.length || secondOptions?.length ? (
        <>
          <ButtonPrimary
            className="md:hidden"
            textSize="font-medium"
            textColor="text-gray-300"
            variant="outlined"
            color="gray"
            size="sm"
            icon={IIcons.Filter}
            onClick={toggleSheet}
          >
            Filter
          </ButtonPrimary>
          <AppSheet
            open={toggle}
            onClose={toggleSheet}
            title="Filter"
            className="max-h-[90vh] px-4 pt-[22px] pb-6"
          >
            <div className="py-4 grid gap-4">
              {firstOptions && onChangeFilter && (
                <RadioInput
                  options={firstOptions}
                  name={'filter'}
                  value={filter}
                  label={labelFirstFilter}
                  errorMsg={''}
                  onChange={(val) => {
                    setFilter(val)
                    onChangeFilter(val)
                  }}
                  parentClass="[&>label]:text-body-lg"
                  className="justify-between flex-row-reverse space-x-0 [&>label]:space-x-0"
                />
              )}
              {showSecondFilter && secondOptions && onChangeSecondFilter && (
                <>
                  <Separator orientation="horizontal" />
                  <RadioInput
                    options={secondOptions}
                    name={'secondFilter'}
                    value={secondFilter}
                    label={labelSecondFilter}
                    errorMsg={''}
                    onChange={(val) => {
                      setSecondFilter(val)
                      onChangeSecondFilter(val)
                    }}
                    parentClass="[&>label]:text-body-lg"
                    className="justify-between flex-row-reverse space-x-0 [&>label]:space-x-0"
                  />
                </>
              )}
            </div>
          </AppSheet>
        </>
      ) : null}
    </>
  )
}
