'use client'
import { sanitizeForId } from '@/utils/stringUtils'
import { TabItemProps } from './TabList'
import { twMerge } from 'tailwind-merge'

type TabItemPropsAnimated = TabItemProps & {
  isActive?: boolean
  className?: string
}

const TabItem: React.FC<TabItemPropsAnimated> = ({ className, label, children, isActive = false }) => (
  <>
    <div className={`transform w-full transition ease-in-out duration-200 ${isActive ? 'show' : 'hidden'}`}>
      <div
        className={twMerge(`tab-content py-3 bg-light ${className ?? ''}`)}
        role="tabpanel"
        aria-labelledby={`tab-${sanitizeForId(label)}`}
        id={`panel-${sanitizeForId(label)}`}
      >
        {children}
      </div>
    </div>
  </>
)

export default TabItem
