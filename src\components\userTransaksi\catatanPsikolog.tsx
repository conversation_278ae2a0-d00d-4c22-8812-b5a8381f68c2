'use client'
import React, { useState } from 'react'
import { IIcons, SVGIcons } from '../_common/icon'
import InfoDetail from './infoDetail'

interface CatatanPsikologProps {
  notes?: string
  tasks?: string
  nextSchedule?: string
  onUpdateClick?: () => void
  isEditable?: boolean
}

export default function CatatanPsikolog({
  notes,
  tasks,
  nextSchedule,
  onUpdateClick,
  isEditable = false,
}: CatatanPsikologProps) {
  const [accordionOpen, setAccordionOpen] = useState(false)

  const hasContent = notes || tasks || nextSchedule

  if (!hasContent) {
    return (
      <div className="w-full bg-white border border-[#ebebeb] rounded-[15px] p-3">
        <div className="flex flex-col">
          <span className="text-[#222222] text-[14px] font-bold">Catatan dari Psikolog</span>
          <span className="text-[#535353] text-[12px]">Belum tersedia</span>
        </div>
      </div>
    )
  }

  return (
    <div className="w-full bg-white border border-[#ebebeb] rounded-[15px] px-3">
      <div className="bg-transparent py-1 xl:py-0 relative z-10">
        <div className="flex flex-col gap-2 w-full bg-transparent transition-all duration-500 ease-in-out py-3">
          <div className="flex justify-between items-center">
            <span className="font-bold text-[#222222] text-[16px]">Catatan dari Psikolog</span>
            <div className="flex gap-2 items-center">
              {isEditable && (
                <button
                  onClick={onUpdateClick}
                  className="text-[#039EE9] text-[14px] flex items-center gap-1"
                >
                  <SVGIcons name={IIcons.Edit} className="w-4 h-4" />
                  Ubah Catatan Konseling
                </button>
              )}
              <div onClick={() => setAccordionOpen(!accordionOpen)} className="cursor-pointer">
                <SVGIcons
                  className={`transition-all duration-500 ease-in-out ${
                    accordionOpen ? 'rotate-0' : 'rotate-[180deg]'
                  }`}
                  name={IIcons.ArrowUp}
                />
              </div>
            </div>
          </div>
        </div>
        <div
          className={`bg-transparent grid overflow-hidden transition-all duration-500 ease-in-out text-slate-600 ${
            accordionOpen ? 'grid-rows-[1fr] opacity-100' : 'grid-rows-[0fr] opacity-0 py-0'
          }`}
        >
          <div className="flex flex-col py-3 gap-4">
            {notes && <InfoDetail title="Catatan" content={notes} />}
            {tasks && <InfoDetail title="Tugas" content={tasks} />}
            {nextSchedule && <InfoDetail title="Saran Jadwal Berikutnya" content={nextSchedule} />}
          </div>
        </div>
      </div>
    </div>
  )
}
